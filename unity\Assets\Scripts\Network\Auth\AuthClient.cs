using System.Text;
using Configuration;
using Data;
using Network.Payload;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using UnityEngine;
using UnityEngine.Networking;

namespace Network.Auth
{
    public static class AuthClient
    {
        private const string CognitoEndpoint = "https://cognito-idp.eu-west-1.amazonaws.com/";

        private static string GetClientId()
        {
            return ConfigurationManager.Configuration.AppIds.ClientId;
        }

        private static void SetCommonHeaders(UnityWebRequest request, string amzTarget)
        {
            request.SetRequestHeader("Content-Type", "application/x-amz-json-1.1");
            request.SetRequestHeader("X-Amz-Target", "AWSCognitoIdentityProviderService." + amzTarget);
            request.SetRequestHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36");
        }

        private static UnityWebRequest CreatePostJsonRequest(string json, string amzTarget)
        {
            var request = new UnityWebRequest(CognitoEndpoint, "POST");
            var bodyRaw = Encoding.UTF8.GetBytes(json);
            request.uploadHandler = new UploadHandlerRaw(bodyRaw);
            request.downloadHandler = new DownloadHandlerBuffer();
            SetCommonHeaders(request, amzTarget);
            return request;
        }

        public static UnityWebRequest GetLoginRequest(string username, string password)
        {
            var payload = new LoginPayload
            {
                AuthFlow = "USER_PASSWORD_AUTH",
                ClientId = GetClientId(),
                AuthParameters = new AuthParameters
                {
                    USERNAME = username,
                    PASSWORD = password
                }
            };

            var json = JsonConvert.SerializeObject(payload);
            return CreatePostJsonRequest(json, "InitiateAuth"); ;
        }
        
        public static UnityWebRequest GetLogoutRequest(string accessToken)
        {
            var payload = new LogoutPayload()
            {
                AccessToken = accessToken
            };

            var json = JsonConvert.SerializeObject(payload);
            return CreatePostJsonRequest(json, "GlobalSignOut"); ;
        }

        public static UnityWebRequest GetSignUpRequest(string username, string password, string email)
        {
            var userAttributes = new UserAttribute[]
            {
                new UserAttribute { Name = "email", Value = email },
                new UserAttribute { Name = "custom:deviceId", Value = SessionData.TvDeviceId }
            };

            var payload = new SignUpPayload
            {
                ClientId = GetClientId(),
                Username = username,
                Password = password,
                UserAttributes = userAttributes
            };

            var json = JsonConvert.SerializeObject(payload);
            return CreatePostJsonRequest(json, "SignUp"); ;
        }

        public static UnityWebRequest GetRefreshAccessTokenRequest(string refreshToken)
        {
            var payload = new
            {
                AuthFlow = "REFRESH_TOKEN_AUTH",
                ClientId = GetClientId(),
                AuthParameters = new
                {
                    REFRESH_TOKEN = refreshToken
                }
            };

            var json = JsonConvert.SerializeObject(payload);
            return CreatePostJsonRequest(json, "InitiateAuth");
        }

        public static UnityWebRequest GetForgotPasswordRequest(string username)
        {
            var payload = new
            {
                ClientId = GetClientId(),
                Username = username
            };

            var json = JsonConvert.SerializeObject(payload);
            return CreatePostJsonRequest(json, "ForgotPassword");
        }
        
        public static UnityWebRequest GetConfirmForgotPasswordRequest(string username, string password, string confirmationCode)
        {
            var payload = new
            {
                ClientId = GetClientId(),
                Username = username,
                Password = password,
                ConfirmationCode = confirmationCode
            };

            var json = JsonConvert.SerializeObject(payload);
            return CreatePostJsonRequest(json, "ConfirmForgotPassword");
        }

        public static string ParseTokenFromResponse(string response, string tokenId)
        {
            if (string.IsNullOrEmpty(response))
                return string.Empty;

            try
            {
                var jObject = JObject.Parse(response);
                var accessToken = jObject["AuthenticationResult"]?[tokenId]?.ToString();
                return accessToken ?? string.Empty;
            }
            catch
            {
                return string.Empty;
            }
        }

        public static AuthResponse GetAuthResponse(string response)
        {
            if (string.IsNullOrEmpty(response))
            {
                Debug.LogError("Response is null or empty.");
                return null;
            }

            try
            {
                return JsonConvert.DeserializeObject<AuthResponse>(response);
            }
            catch (JsonException ex)
            {
                Debug.LogError($"Failed to parse auth response: {ex.Message}");
                return null;
            }
        }
    }
}
