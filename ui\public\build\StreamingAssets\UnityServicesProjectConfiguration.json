{"Keys": ["com.unity.services.core.environment-name", "com.unity.services.core.cloud-environment", "com.unity.services.core.version", "com.unity.services.core.initializer-assembly-qualified-names", "com.unity.services.cloud-diagnostics.version", "com.unity.services.cloud-diagnostics.initializer-assembly-qualified-names", "com.unity.services.core.all-package-names"], "Values": [{"m_Value": "production", "m_IsReadOnly": false}, {"m_Value": "production", "m_IsReadOnly": false}, {"m_Value": "1.13.0", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.Core.Registration.CorePackageInitializer, Unity.Services.Core.Registration, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null;Unity.Services.Core.Internal.IInitializablePackageV2, Unity.Services.Core.Internal, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "1.0.10", "m_IsReadOnly": true}, {"m_Value": "Unity.Services.CloudDiagnostics.CloudDiagnosticsInitializer, Unity.Services.CloudDiagnostics, Version=0.0.0.0, Culture=neutral, PublicKeyToken=null", "m_IsReadOnly": true}, {"m_Value": "com.unity.services.core;com.unity.services.cloud-diagnostics", "m_IsReadOnly": false}]}