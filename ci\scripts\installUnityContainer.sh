set -euo pipefail

if [ "$UNITY_BUILD_REQUIRED" = "true" ]; then
    echo "Build is required, proceeding with Unity build..."
    if [ "$BUILD_TARGET" = "android" ] || [ "$BUILD_TARGET" = "webgl" ]; then
    echo "Pulling Docker image from ECR"
    IMAGE_NAME=$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com/unity-ci-build-images:$UNITY_VERSION-$BUILD_TARGET-3.1.0
    docker pull $IMAGE_NAME || {
        echo "ECR pull failed. Trying Docker Hub..."
        IMAGE_NAME=unityci/editor:$UNITY_VERSION-$BUILD_TARGET-3.1.0
        docker pull $IMAGE_NAME
    }

    # Check if the container already exists and remove it
    if [ "$(docker ps -aq -f name=unity-build)" ]; then
        echo "Removing existing container with name 'unity-build'..."
        docker rm -f unity-build
    fi

    docker run -d --name unity-build --entrypoint tail  \
        -v "$PWD":/workspace \
        -v "$PWD/.npmrc":/root/.npmrc \
        -e UNITY_USERNAME=$UNITY_USERNAME \
        -e UNITY_PASSWORD=$UNITY_PASSWORD \
        -e UNITY_SERIAL=$UNITY_SERIAL \
        -e GH_TOKEN=$GH_TOKEN \
    -e BUILD_DIR="./workspace/unity" \
        $IMAGE_NAME -f /dev/null
    else
    echo "Unsupported build target: $BUILD_TARGET"
    fi
else
    echo "No changes detected in Unity project, skipping build."
fi