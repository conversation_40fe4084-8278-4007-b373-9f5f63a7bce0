using JetBrains.Annotations;
using Newtonsoft.Json;

namespace BallManager.StrokeData.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class HoleDetailsDataContract
    {
        [JsonProperty("holeNumber")]
        public int HoleNumber;

        [JsonProperty("tee")]
        public CoordinatesDataContract Tee;

        [JsonProperty("pin")]
        public CoordinatesDataContract Pin;

        [JsonProperty("centerOfFairway")] 
        [CanBeNull] 
        public CoordinatesDataContract CenterOfFairway;
        
        [JsonProperty("parNumber")]
        public int? ParNumber { get; set; }

        public bool IsValid()
        {
            if (Tee == null || Pin == null)
            {
                return false;
            }
            return true;
        }
    }
}