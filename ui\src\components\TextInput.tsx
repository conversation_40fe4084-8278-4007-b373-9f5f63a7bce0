import { forwardRef, ReactElement, InputHTMLAttributes } from "react";

export const TextInput = forwardRef<
  HTMLInputElement,
  {
    label: string;
    icon?: ReactElement;
    error?: string | boolean;
  } & InputHTMLAttributes<HTMLInputElement>
>(({ label, icon, error, ...props }, ref) => {
  return (
    <div className="flex flex-col gap-2">
      <label
        className={`text-sm tracking-tight ${!!error ? "text-red-500" : "text-white/70"}`}
      >
        {label}
        {error && typeof error === "string" ? ` - ${error}` : ""}
      </label>
      <div className="relative">
        <input
          ref={ref}
          placeholder={`${label} `}
          name={label}
          className={`px-4 py-3 w-full rounded-md border bg-transparent shadow-sm text-white/80 tracking-tight focus:border-gold outline-none [.validated_&]:invalid:border-red-500 ${!!error ? "border-red-500" : " border-white/25"}`}
          {...props}
        />
        {icon}
      </div>
    </div>
  );
});
