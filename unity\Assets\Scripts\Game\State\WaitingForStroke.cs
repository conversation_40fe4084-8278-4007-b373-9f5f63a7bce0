using BallManager.StrokeData.Data;
using Network.Services;
using UnityEngine;

namespace Game.State
{
    public class WaitingForStroke : BaseGameState
    {
        [SerializeField] private GameObject waitingForStrokeUi;
        [SerializeField] private int MaxWaitingTime = 45;
        // TODO: Add 3d prediction marker

        public override void Enter()
        {
            tabMenuBackButton.OnBackButtonPressed += BackButtonPressed;
            StrokeService.StrokeReceived += OnStrokeReceived;
            
            waitingForStrokeUi.SetActive(true);
            Invoke(nameof(StopWaitingForStroke), MaxWaitingTime);
        }

        public override void Exit()
        {
            tabMenuBackButton.OnBackButtonPressed -= BackButtonPressed;
            StrokeService.StrokeReceived -= OnStrokeReceived;

            waitingForStrokeUi.SetActive(false);
            CancelInvoke(nameof(StopWaitingForStroke));
        }
        
        private void BackButtonPressed()
        {
            onFinished?.Invoke(GameStateType.ResetGame);
        }

        private void OnStrokeReceived(StrokeData strokeData)
        {
            strokeManager.ShowStroke(strokeData);
            onFinished?.Invoke(GameStateType.ShowingStroke);
        }

        private void StopWaitingForStroke()
        {
            onFinished?.Invoke(GameStateType.ResetGame);
        }
    }
}
