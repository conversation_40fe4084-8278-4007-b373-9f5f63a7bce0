﻿using System;
using System.IO;
using Bootstrap;
using Network.Services;
using Sky.GenEx.Toast;
using Sky.GenEx.Utilities;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Network.Auth
{
    public class AuthTokenManager : MonoSingleton<AuthTokenManager>, ITokenProvider
    {
        private const int ReloadWaitTime = 2;
        private bool scheduledRefreshPending = false;

        private AuthTokens authTokens;
        private TokenStorage tokenStorage;

        public event Action<string> TokenRefreshed;
        public string AccessToken => authTokens?.AccessToken.Token;
        public bool IsAccessTokenValid => authTokens != null && IsTokenValid(authTokens.AccessToken);
        public bool IsRefreshTokenValid => authTokens != null && IsTokenValid(authTokens.RefreshToken);

        public async Awaitable Initialize()
        {
            tokenStorage = new TokenStorage();
            await LoadAuthTokens();
        }

        private async Awaitable LoadAuthTokens()
        {
            authTokens = tokenStorage.LoadAuthTokens();

            if (authTokens == null)
            {
                Debug.Log("No auth token found, user is not logged in.");
            }
            else
            {
                await RefreshAccessTokenScheduled();
            }
        }

        private bool IsTokenValid(AuthToken token)
        {
            if (token == null)
                return false;

            if (DateTime.Now >= token.Expiration)
                return false;

            return true;
        }

        public void UpdateTokens(AuthTokens authTokens)
        {
            if (authTokens == null)
            {
                Debug.LogError("AuthTokens cannot be null.");
                return;
            }
            this.authTokens = authTokens;
            tokenStorage.SaveAuthTokens(this.authTokens);
            Debug.Log("Auth tokens updated successfully.");
            TokenRefreshed?.Invoke(this.authTokens.AccessToken.Token);
            ScheduleTokenRefresh();
        }

        public void ClearTokens()
        {
            authTokens = null;
            tokenStorage.ClearAuthTokens();
            CancelTokenRefresh();
        }

        private void ScheduleTokenRefresh()
        {
            if (authTokens == null || authTokens.AccessToken == null)
                return;

            // Cancel any previous scheduled refresh
            CancelTokenRefresh();

            var timeUntilRefresh = (authTokens.AccessToken.Expiration - DateTime.Now).TotalSeconds;
            if (timeUntilRefresh <= 0)
            {
                // If already expired or within lead time, refresh immediately
                RefreshAccessTokenScheduled();
            }
            else
            {
                Invoke(nameof(RefreshAccessTokenScheduled), (float)timeUntilRefresh);
                scheduledRefreshPending = true;
                Debug.Log($"Scheduled access token refresh in {timeUntilRefresh} seconds.");
            }
        }

        private void CancelTokenRefresh()
        {
            if (scheduledRefreshPending)
            {
                CancelInvoke(nameof(RefreshAccessTokenScheduled));
                scheduledRefreshPending = false;
            }
        }

        private async Awaitable RefreshAccessTokenScheduled()
        {
            scheduledRefreshPending = false;
            if (authTokens == null || authTokens.RefreshToken == null)
                return;

            Debug.Log("Refreshing access token (scheduled)...");
            var refreshResult = await AuthService.RefreshAccessToken(authTokens.RefreshToken.Token);
            if (refreshResult.IsSuccess)
            {
                authTokens.UpdateAccessToken(refreshResult.Value);
                tokenStorage.SaveAuthTokens(authTokens);
                TokenRefreshed?.Invoke(authTokens.AccessToken.Token);
                Debug.Log("Access token refreshed.");
                ScheduleTokenRefresh();
            }
            else
            {
                ClearTokens();
                MessageToastController.Instance.ShowToast("Something went wrong. We will restart the app", ToastDuration.Long, ToastType.Error);
                ReloadApp();
            }
        }

        protected override void OnDestroy()
        {
            CancelTokenRefresh();

            base.OnDestroy();
        }


        private async Awaitable ReloadApp()
        {
            authTokens = null;
            await Awaitable.WaitForSecondsAsync(ReloadWaitTime);
            var scenePath = BootstrapKeys.LOGIN_SCENE_PATH;
            var sceneName = Path.GetFileNameWithoutExtension(scenePath);
            Debug.Log($"Loading scene: {sceneName}");
            SceneManager.LoadScene(sceneName);
        }
    }
}