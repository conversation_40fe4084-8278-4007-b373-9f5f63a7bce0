using System;
using TMPro;
using UnityEngine;

namespace Sky.GenEx.InfiniteScroller.Samples.GolfResultsSample
{
    public class PastResultRow : MonoBehaviour
    {
        [SerializeField]
        private TextMeshProUGUI golferNameText;

        [SerializeField]
        private string holeNumberTextTemplate = "Hole {0}";

        [SerializeField]
        private TextMeshProUGUI holeNumberText;

        [SerializeField]
        public string holeTimeFormat = "ddd, HH:mm";

        [SerializeField]
        private TextMeshProUGUI holeTimeText;
        
        [SerializeField]
        private string scoreTextTemplate = "+{0}pts";

        [SerializeField]
        private TextMeshProUGUI scoreText;

        public void Refresh(PastPredictionScoreData data)
        {
            RefreshGolferName(data.GolferName);
            RefreshHoleNumber(data.HoleNumber);
            RefreshHoleTime(data.CreatedTimestamp);
            RefreshScore(data.Score);
        }

        private void RefreshGolferName(string golferName)
        {
            if (golferNameText == null)
                return;

            golferNameText.text = golferName;
        }

        private void RefreshHoleNumber(int holeNumber)
        {
            if (holeNumberText == null)
                return;

            holeNumberText.text = string.Format(holeNumberTextTemplate, holeNumber);
        }

        private void RefreshHoleTime(DateTime holeTime)
        {
            if (holeTimeText == null)
                return;

            holeTimeText.text = holeTime.ToString(holeTimeFormat);
        }

        private void RefreshScore(int score)
        {
            if (scoreText == null)
                return;
            
            scoreText.text = string.Format(scoreTextTemplate, score);
        }
    }
}