using App.CameraSystem.StateMachine.States.Base;
using App.CameraSystem.StateMachine.Types;
using Configuration;

namespace App.CameraSystem.StateMachine.States
{
    public class LoadingCamera : BaseCameraState
    {
        protected override void Initialize()
        {
        }
        
        public override void Enter()
        {
            if (ConfigurationManager.Configuration == null)
            {
                ConfigurationManager.ConfigLoaded += OnConfigLoaded;
            }
            else
            {
                OnConfigLoaded();
            }
        }

        public override void Exit()
        {
            ConfigurationManager.ConfigLoaded -= OnConfigLoaded;
        }

        private void OnConfigLoaded()
        {
            ChangeState(CameraStateType.StartingView);
        }
    }
}
