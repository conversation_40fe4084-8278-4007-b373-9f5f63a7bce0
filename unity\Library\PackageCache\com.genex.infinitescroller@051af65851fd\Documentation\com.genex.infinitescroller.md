﻿# Infinite Scroller
This package provides an infinite scroller implementation forked from: https://github.com/mopsicus/uis.

## Features
- Handles creating items in the scroller based on data and recycling them when scrolling.
- Pull to refresh callback
- End of scroll load more callback

## Dependencies
- com.unity.textmeshpro@3.0.6

## Assembly References
[None]

## Installation - Package Manager
1. Open the Unity Package Manager (Window > Package Manager).
2. Click the "+" button in the top left corner.
3. Select "Add package from git URL..."

For URL use as explained on the root of the repository. 

## Samples

Check out [GolfResultsSample](../Samples/GolfResultsSample) on an example usage. 

# Content
Scroller - Component that handles creating the scroller items and recycling them.