﻿using System;
using Prediction;
using PredictionWindow;
using Scoring;
using Stroke;
using TabMenu;
using TabMenu.Containers;
using UnityEngine;

namespace Game.State
{
    public abstract class BaseGameState : MonoBehaviour, IGameState
    {
        protected Action<GameStateType> onFinished;
        
        protected TabMenuManager tabMenuManager;
        protected TabMenuBackButton tabMenuBackButton;
        protected PredictionWindowManager predictionWindowManager;
        protected PredictionManager predictionManager;
        protected StrokeManager strokeManager;
        protected ScoreManager scoreManager;
        protected PlayContainer playContainer;

        public void SetupState(Action<GameStateType> onFinished,
            TabMenuManager tabMenuManager,
            TabMenuBackButton tabMenuBackButton,
            PredictionWindowManager predictionWindowManager,
            PredictionManager predictionManager,
            StrokeManager strokeManager, ScoreManager scoreManager,
            PlayContainer playContainer)
        {
            this.tabMenuManager = tabMenuManager ?? throw new ArgumentNullException(nameof(tabMenuManager), "TabMenuManager cannot be null.");
            this.tabMenuBackButton = tabMenuBackButton ?? throw new ArgumentNullException(nameof(tabMenuBackButton), "TabMenuBackButton cannot be null.");
            this.predictionWindowManager = predictionWindowManager ?? throw new ArgumentNullException(nameof(predictionWindowManager), "PredictionWindowManager cannot be null.");
            this.predictionManager = predictionManager ?? throw new ArgumentNullException(nameof(predictionManager), "PredictionManager cannot be null.");
            this.strokeManager = strokeManager ?? throw new ArgumentNullException(nameof(strokeManager), "StrokeManager cannot be null.");
            this.scoreManager = scoreManager ?? throw new ArgumentNullException(nameof(scoreManager), "ScoreManager cannot be null.");
            this.playContainer = playContainer ?? throw new ArgumentNullException(nameof(playContainer), "PlayContainer cannot be null.");
            this.onFinished = onFinished ?? throw new ArgumentNullException(nameof(onFinished), "onFinished callback cannot be null.");
        }

        public abstract void Enter();
        public abstract void Exit();
    }
}
