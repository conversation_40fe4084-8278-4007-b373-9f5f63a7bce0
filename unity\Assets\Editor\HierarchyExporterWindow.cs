using System.Text;
using UnityEngine;
using UnityEditor;

namespace Editor
{
    public class HierarchyExporterWindow : EditorWindow
    {
        private bool includeInactiveObjects = true;
        private bool includeComponentDetails = true;
        private Vector2 scrollPosition;
        private string exportedText = "";

        [MenuItem("Tools/Hierarchy Exporter")]
        public static void ShowWindow()
        {
            GetWindow<HierarchyExporterWindow>("Hierarchy Exporter");
        }

        private void OnGUI()
        {
            GUILayout.Label("Hierarchy Export Settings", EditorStyles.boldLabel);
            
            includeInactiveObjects = EditorGUILayout.Toggle("Include Inactive Objects", includeInactiveObjects);
            includeComponentDetails = EditorGUILayout.Toggle("Include Component Details", includeComponentDetails);
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("Export Current Scene Hierarchy", GUILayout.Height(30)))
            {
                ExportSceneHierarchy();
            }
            
            if (GUILayout.But<PERSON>("Export Selected Prefab", GUILayout.Height(30)))
            {
                ExportSelectedPrefab();
            }
            
            EditorGUILayout.Space();
            
            if (!string.IsNullOrEmpty(exportedText))
            {
                GUILayout.Label("Exported Hierarchy (Copied to Clipboard):", EditorStyles.boldLabel);
                
                scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);
                EditorGUILayout.TextArea(exportedText, GUILayout.ExpandHeight(true));
                EditorGUILayout.EndScrollView();
                
                if (GUILayout.Button("Copy to Clipboard Again"))
                {
                    EditorGUIUtility.systemCopyBuffer = exportedText;
                    Debug.Log("Hierarchy exported to clipboard!");
                }
            }
        }

        private void ExportSceneHierarchy()
        {
            var sb = new StringBuilder();
            sb.AppendLine("=== UNITY SCENE HIERARCHY EXPORT ===");
            sb.AppendLine($"Scene: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}");
            sb.AppendLine($"Export Time: {System.DateTime.Now}");
            sb.AppendLine();

            // Get all root objects in scene
            GameObject[] rootObjects = UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects();
            
            foreach (GameObject rootObj in rootObjects)
            {
                if (!includeInactiveObjects && !rootObj.activeInHierarchy)
                    continue;
                    
                ExportGameObject(sb, rootObj, 0);
            }

            exportedText = sb.ToString();
            EditorGUIUtility.systemCopyBuffer = exportedText;
            Debug.Log("Scene hierarchy exported to clipboard!");
        }

        private void ExportSelectedPrefab()
        {
            GameObject selectedObject = Selection.activeGameObject;
            
            if (selectedObject == null)
            {
                EditorUtility.DisplayDialog("No Selection", "Please select a GameObject or Prefab in the Project window or Hierarchy.", "OK");
                return;
            }

            var sb = new StringBuilder();
            
            // Check if it's a prefab asset
            if (PrefabUtility.IsPartOfPrefabAsset(selectedObject))
            {
                sb.AppendLine("=== UNITY PREFAB EXPORT ===");
                sb.AppendLine($"Prefab: {selectedObject.name}");
                sb.AppendLine($"Asset Path: {AssetDatabase.GetAssetPath(selectedObject)}");
            }
            else
            {
                sb.AppendLine("=== UNITY GAMEOBJECT EXPORT ===");
                sb.AppendLine($"GameObject: {selectedObject.name}");
                sb.AppendLine($"Scene: {selectedObject.scene.name}");
            }
            
            sb.AppendLine($"Export Time: {System.DateTime.Now}");
            sb.AppendLine();

            ExportGameObject(sb, selectedObject, 0);

            exportedText = sb.ToString();
            EditorGUIUtility.systemCopyBuffer = exportedText;
            Debug.Log($"GameObject/Prefab '{selectedObject.name}' exported to clipboard!");
        }

        private void ExportGameObject(StringBuilder sb, GameObject obj, int depth)
        {
            string indent = new string(' ', depth * 2);
            string activeStatus = obj.activeInHierarchy ? "" : " [INACTIVE]";
            
            sb.AppendLine($"{indent}├─ {obj.name}{activeStatus}");
            
            if (includeComponentDetails)
            {
                Component[] components = obj.GetComponents<Component>();
                foreach (Component comp in components)
                {
                    if (comp == null) continue;
                    
                    string compIndent = new string(' ', (depth + 1) * 2);
                    sb.AppendLine($"{compIndent}└─ {comp.GetType().Name}");
                    
                    // Add specific component details
                    ExportComponentDetails(sb, comp, depth + 2);
                }
            }

            // Export children
            for (int i = 0; i < obj.transform.childCount; i++)
            {
                GameObject child = obj.transform.GetChild(i).gameObject;
                if (!includeInactiveObjects && !child.activeInHierarchy)
                    continue;
                    
                ExportGameObject(sb, child, depth + 1);
            }
        }

        private void ExportComponentDetails(StringBuilder sb, Component comp, int depth)
        {
            string indent = new string(' ', depth * 2);
            
            switch (comp)
            {
                case RectTransform rt:
                    sb.AppendLine($"{indent}• Pos:{rt.localPosition} Anchor:{rt.anchorMin}-{rt.anchorMax} Size:{rt.sizeDelta}");
                    break;
                    
                case Transform t:
                    sb.AppendLine($"{indent}• Pos:{t.localPosition} Rot:{t.localEulerAngles} Scale:{t.localScale}");
                    break;
                    
                case UnityEngine.UI.Image img:
                    sb.AppendLine($"{indent}• Sprite:{(img.sprite ? img.sprite.name : "None")} Color:{img.color}");
                    break;
                    
                case UnityEngine.UI.Text txt:
                    string shortText = txt.text.Length > 30 ? txt.text.Substring(0, 30) + "..." : txt.text;
                    sb.AppendLine($"{indent}• Text:\"{shortText}\" Font:{(txt.font ? txt.font.name : "None")}");
                    break;
                    
                case UnityEngine.UI.Button btn:
                    sb.AppendLine($"{indent}• Interactable:{btn.interactable} Events:{btn.onClick.GetPersistentEventCount()}");
                    break;
                    
                case Canvas canvas:
                    sb.AppendLine($"{indent}• Mode:{canvas.renderMode} Order:{canvas.sortingOrder}");
                    break;
                    
                default:
                    // For custom components, show only the most important fields (limit to 3)
                    var fields = comp.GetType().GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                    int fieldCount = 0;
                    foreach (var field in fields)
                    {
                        if (fieldCount >= 3) break; // Limit to 3 fields per component
                        
                        if (field.IsPublic || field.GetCustomAttributes(typeof(SerializeField), false).Length > 0)
                        {
                            try
                            {
                                var value = field.GetValue(comp);
                                string valueStr = value?.ToString();
                                if (valueStr != null && valueStr.Length > 20)
                                    valueStr = valueStr.Substring(0, 20) + "...";
                                sb.AppendLine($"{indent}• {field.Name}:{valueStr}");
                                fieldCount++;
                            }
                            catch
                            {
                                sb.AppendLine($"{indent}• {field.Name}:[Error]");
                                fieldCount++;
                            }
                        }
                    }
                    break;
            }
        }
    }
}

