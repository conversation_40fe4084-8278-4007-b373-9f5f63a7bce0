using System;
using System.Collections.Generic;
using Configuration;
using Network.Services;
using UnityEngine;

namespace Headshots
{
    public static class HeadshotService
    {
        private static List<Headshot> headshotCache = new List<Headshot>();
        private static HashSet<int> headshotsBeingFetched = new HashSet<int>();

        public static async Awaitable<Headshot> GetHeadshot(int golferId)
        {
            while (headshotsBeingFetched.Contains(golferId))
            {
                await Awaitable.NextFrameAsync();
            }

            var headshot = headshotCache.Find(h => h.Id == golferId);
            if (headshot != null)
            {
                return headshot;
            }

            try
            {
                var url = $"{ConfigurationManager.Configuration.AWSUrlData.HeadshotsUrl}{golferId}.png";
                var sprite = await ImageFetchService.GetImage(url);
                headshot = headshotCache.Find(h => h.Id == golferId);
                if (headshot == null)
                {
                    if (sprite == null)
                    {
                        Debug.LogWarning($"No headshot found for golfer ID: {golferId}");
                        return null;
                    }
                    headshot = new Headshot(golferId, sprite);
                    headshotCache.Add(headshot);
                }
                return headshot;
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error fetching headshot for golfer {golferId}: {ex.Message}");
                return null;
            }
            finally
            {
                headshotsBeingFetched.Remove(golferId);
            }
        }
    }
}