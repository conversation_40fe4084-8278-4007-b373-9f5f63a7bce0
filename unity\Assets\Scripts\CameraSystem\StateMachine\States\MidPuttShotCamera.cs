using App.CameraSystem.StateMachine.Data;
using App.CameraSystem.StateMachine.States.Base;
using BallManager.Ball;
using UnityEngine;

namespace App.CameraSystem.StateMachine.States
{
	public class MidPuttShotCamera : BaseCameraState, ICameraStateWithPayload<TrackableTargets>
	{
		private IGolfBall ball;
		private Transform hole;
        private Vector3 positionOffset = new Vector3(0f, 2f, 0f);

		private const float LookOffsetVertical = 2f;

		public void SetPayload(TrackableTargets targets)
		{
			if (targets.Ball == null)
			{
				Debug.LogError("MidPuttShotCamera: Ball is null in the provided TrackableTargets.");
				return;	
			}
			if (targets.HoleTransform == null)
			{
				Debug.LogError("MidPuttShotCamera: HoleTransform is null in the provided TrackableTargets.");
				return;	
			}
			ball = targets.Ball;
			hole = targets.HoleTransform;
			StateCamera.transform.LookAt(hole);
		}

		public override void Enter()
		{
			SetCameraTarget();
		}
		
        public override void Exit()
		{
		}

		private void SetCameraTarget()
		{
			StateCamera.transform.position = ball.Transform.position;
			Vector3 targetPosition = new Vector3(hole.position.x, hole.position.y + LookOffsetVertical, hole.position.z);
			
			StateCamera.transform.position += StateCamera.transform.TransformDirection(positionOffset);
			StateCamera.transform.LookAt(targetPosition);
		}
    }
}