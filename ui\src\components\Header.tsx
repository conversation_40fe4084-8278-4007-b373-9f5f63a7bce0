import { ReactElement, useEffect, useState } from "react";
import { useNavigate, useLocation } from "react-router";
import { ChevronIcon } from "./icons";

export const Header = ({
  label = "",
  trailing,
  divider = true,
  className = "",
}: {
  label?: string | ReactElement;
  trailing?: ReactElement;
  divider?: boolean;
  className?: string;
}) => {
  const navigate = useNavigate();
  const location = useLocation();

  const handleBackClick = () => {
    void navigate("/welcome", { replace: true });
  };

  const [canGoBack, setCanGoBack] = useState(false);
  useEffect(() => {
    setCanGoBack(location.pathname !== "/welcome");
  }, [location, setCanGoBack]);

  return (
    <div
      className={`w-full min-h-11 flex px-4 py-3 justify-between items-center ${divider && "border-b-white/5 border-b-2"} ${className}`}
    >
      <div
        className="h-6 aspect-square"
        onClick={canGoBack ? handleBackClick : undefined}
      >
        {canGoBack && <ChevronIcon direction="left" className="stroke-white" />}
      </div>
      <div className="text-white h-9 text-3xl text-center px-2">{label}</div>
      <div className="h-6 aspect-square">{trailing}</div>
    </div>
  );
};
