import {
  AuthSession,
  fetchAuthSession,
  fetchUserAttributes,
} from "aws-amplify/auth";
import { useEffect, useState, JSX, createContext, useCallback } from "react";
import { Navigate } from "react-router";

export const AuthContext = createContext<AuthSession | null>(null);

export const ProtectedRoute: ({
  children,
}: {
  children: JSX.Element;
}) => JSX.Element = ({ children }) => {
  const [validSession, setValidSession] = useState<AuthSession | null>(null);
  const [socialFTUE, setSocialFTUE] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [refreshTimeout, setRefreshTimeout] = useState<NodeJS.Timeout | null>(
    null,
  );

  const validateAuthSession = async () => {
    if (document.visibilityState !== "visible") return;
    try {
      const session = await fetchAuthSession({ forceRefresh: true });
      if (!!session && !!session.tokens?.accessToken) {
        const userAttributes = await fetchUserAttributes();
        if (!userAttributes.preferred_username) {
          setSocialFTUE(true);
        }
        setValidSession(session);
      } else {
        setValidSession(null);
      }
    } catch (error) {
      setValidSession(null);
    } finally {
      setIsLoading(false);
    }
  };

  // validate auth session on load
  useEffect(() => {
    void validateAuthSession();
  }, []);

  // whenever page gains focus again, check the existing refresh timeout
  const onVisibilityChange = useCallback(() => {
    if (document.visibilityState !== "visible") return;
    console.log("Checking existing auth session");
    if (refreshTimeout) {
      clearTimeout(refreshTimeout);
      setRefreshTimeout(null);
    }
    if (validSession) setExpirationTimeout(validSession);
    else void validateAuthSession();
  }, [refreshTimeout, validSession]);

  const setExpirationTimeout = (session: AuthSession) => {
    if (!session.tokens?.accessToken?.payload?.exp) return;
    const accessTokenExpiry = session.tokens.accessToken.payload.exp * 1000;
    const timeoutDuration = Math.max(
      0,
      accessTokenExpiry - new Date().getTime() - 60 * 1000 * 5,
    ); // Refresh 5 minutes before expiry (or immediately if already expired)
    console.log("Access token expires:", new Date(accessTokenExpiry));
    setRefreshTimeout(setTimeout(validateAuthSession, timeoutDuration));
  };

  // refresh the auth token 5 minutes before expiry
  useEffect(() => {
    if (!validSession) return;
    setExpirationTimeout(validSession);
    return () => {
      // clear timeout if session value changes
      if (refreshTimeout) {
        clearTimeout(refreshTimeout);
        setRefreshTimeout(null);
      }
    };
  }, [validSession]);

  useEffect(() => {
    document.addEventListener("visibilitychange", onVisibilityChange);
    return () => {
      document.removeEventListener("visibilitychange", onVisibilityChange);
    };
  }, [refreshTimeout]);

  if (isLoading) {
    return <></>;
  }

  if (socialFTUE) {
    return <Navigate to="/ftue?social=true" />;
  }

  if (!validSession) {
    return <Navigate to="/welcome" />;
  }

  return (
    <AuthContext.Provider value={validSession}>{children}</AuthContext.Provider>
  );
};
