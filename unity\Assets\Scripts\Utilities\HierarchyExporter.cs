using System.Text;
using UnityEngine;
using UnityEngine.UI;

namespace Utilities
{
    public class HierarchyExporter : MonoBehaviour
    {
        [Header("Export Settings")]
        [SerializeField] private bool includeInactiveObjects = true;
        [SerializeField] private bool includeComponentDetails = true;
        [SerializeField] private int maxDepth = 10;
        
        [Header("Export Controls")]
        [SerializeField] private Button exportButton;
        [SerializeField] private Text outputText;
        
        private StringBuilder sb;

        private void Start()
        {
            if (exportButton != null)
                exportButton.onClick.AddListener(ExportHierarchy);
        }

        [ContextMenu("Export Hierarchy")]
        public void ExportHierarchy()
        {
            sb = new StringBuilder();
            sb.AppendLine("=== UNITY HIERARCHY EXPORT ===");
            sb.AppendLine($"Scene: {UnityEngine.SceneManagement.SceneManager.GetActiveScene().name}");
            sb.AppendLine($"Export Time: {System.DateTime.Now}");
            sb.AppendLine();

            // Get all root objects in scene
            GameObject[] rootObjects = UnityEngine.SceneManagement.SceneManager.GetActiveScene().GetRootGameObjects();
            
            foreach (GameObject rootObj in rootObjects)
            {
                if (!includeInactiveObjects && !rootObj.activeInHierarchy)
                    continue;
                    
                ExportGameObject(rootObj, 0);
            }

            string result = sb.ToString();
            
            // Copy to clipboard
            GUIUtility.systemCopyBuffer = result;
            
            // Display in UI if available
            if (outputText != null)
            {
                outputText.text = result;
            }
            
            Debug.Log("Hierarchy exported to clipboard!");
            Debug.Log(result);
        }

        private void ExportGameObject(GameObject obj, int depth)
        {
            if (depth > maxDepth) return;

            string indent = new string(' ', depth * 2);
            string activeStatus = obj.activeInHierarchy ? "" : " [INACTIVE]";
            
            sb.AppendLine($"{indent}├─ {obj.name}{activeStatus}");
            
            if (includeComponentDetails)
            {
                Component[] components = obj.GetComponents<Component>();
                foreach (Component comp in components)
                {
                    if (comp == null) continue;
                    
                    string compIndent = new string(' ', (depth + 1) * 2);
                    sb.AppendLine($"{compIndent}└─ {comp.GetType().Name}");
                    
                    // Add specific component details
                    ExportComponentDetails(comp, depth + 2);
                }
            }

            // Export children
            for (int i = 0; i < obj.transform.childCount; i++)
            {
                GameObject child = obj.transform.GetChild(i).gameObject;
                if (!includeInactiveObjects && !child.activeInHierarchy)
                    continue;
                    
                ExportGameObject(child, depth + 1);
            }
        }

        private void ExportComponentDetails(Component comp, int depth)
        {
            string indent = new string(' ', depth * 2);
            
            switch (comp)
            {
                case RectTransform rt:
                    sb.AppendLine($"{indent}• AnchorMin: {rt.anchorMin}");
                    sb.AppendLine($"{indent}• AnchorMax: {rt.anchorMax}");
                    sb.AppendLine($"{indent}• SizeDelta: {rt.sizeDelta}");
                    sb.AppendLine($"{indent}• Position: {rt.localPosition}");
                    break;
                    
                case Transform t:
                    sb.AppendLine($"{indent}• Position: {t.localPosition}");
                    sb.AppendLine($"{indent}• Rotation: {t.localEulerAngles}");
                    sb.AppendLine($"{indent}• Scale: {t.localScale}");
                    break;
                    
                case Image img:
                    sb.AppendLine($"{indent}• Sprite: {(img.sprite ? img.sprite.name : "None")}");
                    sb.AppendLine($"{indent}• Color: {img.color}");
                    break;
                    
                case Text txt:
                    sb.AppendLine($"{indent}• Text: \"{txt.text}\"");
                    sb.AppendLine($"{indent}• Font: {(txt.font ? txt.font.name : "None")}");
                    break;
                    
                case Button btn:
                    sb.AppendLine($"{indent}• Interactable: {btn.interactable}");
                    sb.AppendLine($"{indent}• OnClick Events: {btn.onClick.GetPersistentEventCount()}");
                    break;
                    
                case Canvas canvas:
                    sb.AppendLine($"{indent}• Render Mode: {canvas.renderMode}");
                    sb.AppendLine($"{indent}• Sort Order: {canvas.sortingOrder}");
                    break;
                    
                default:
                    // For custom components, try to get some basic info
                    var fields = comp.GetType().GetFields(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance);
                    foreach (var field in fields)
                    {
                        if (field.IsPublic && field.GetCustomAttributes(typeof(SerializeField), false).Length > 0)
                        {
                            try
                            {
                                var value = field.GetValue(comp);
                                sb.AppendLine($"{indent}• {field.Name}: {value}");
                            }
                            catch
                            {
                                sb.AppendLine($"{indent}• {field.Name}: [Error reading value]");
                            }
                        }
                    }
                    break;
            }
        }

        private void OnDestroy()
        {
            if (exportButton != null)
                exportButton.onClick.RemoveListener(ExportHierarchy);
        }
    }
}
