﻿using System;
using Configuration;

namespace Network.Auth
{
    public class AuthTokens
    {        
        private int refreshTokenValidityDays = 1;

        public AuthToken AccessToken { get; private set; }
        public AuthToken RefreshToken { get; private set; }

        public AuthTokens(AuthenticationResult authenticationResult)
        {
            refreshTokenValidityDays = ConfigurationManager.Configuration.ConfigurableValues.RefreshTokenValidityDays;
            AccessToken = new AuthToken(authenticationResult.AccessToken, DateTime.Now.AddSeconds(authenticationResult.ExpiresIn));
            RefreshToken = new AuthToken(authenticationResult.RefreshToken, DateTime.Now.AddDays(refreshTokenValidityDays));
        }

        public AuthTokens(AuthToken accessToken, AuthToken refreshToken)
        {
            AccessToken = accessToken ?? throw new ArgumentNullException(nameof(accessToken));
            RefreshToken = refreshToken ?? throw new ArgumentNullException(nameof(refreshToken));
        }

        public void UpdateAccessToken(AuthToken authToken)
        {
            AccessToken = authToken;
        }
    }
}