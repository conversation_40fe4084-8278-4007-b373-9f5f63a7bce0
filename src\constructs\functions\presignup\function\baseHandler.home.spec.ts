import { PreSignUpTriggerEvent } from "aws-lambda";
import words from "profane-words";
import { baseHandler } from "./baseHandler";
import { getHouseholdSize } from "./utils/getHouseholdSize";
import { isExistingSignInAlias } from "./utils/isExistingSignInAlias";

jest.mock("./utils/getHouseholdSize");
jest.mock("./utils/isExistingSignInAlias");
jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
jest.mock("profane-words", () => ({ includes: jest.fn(() => false) }));
jest.mock("@sky-uk/coins-utilities", () => ({
  getEnvironmentVariable: jest.fn((key) => {
    const env: Record<string, string> = {
      HOUSEHOLD_TABLE_NAME: "TestTable",
      MAX_HOUSEHOLD_SIZE: "5",
      APP_VARIANT: "home",
    };
    return env[key] || "";
  }),
}));

describe("home - baseHandler (preSignUp)", () => {
  const event = {
    request: {
      userAttributes: {
        "custom:deviceId": "device-1",
        email: "<EMAIL>",
      },
    },
    userName: "testuser",
    response: {},
  } as unknown as PreSignUpTriggerEvent;

  beforeEach(() => {
    jest.clearAllMocks();
    (words.includes as jest.Mock).mockReturnValue(false);
  });

  it("should auto confirm and verify if all checks pass", async () => {
    (getHouseholdSize as jest.Mock).mockResolvedValue(2);
    (isExistingSignInAlias as jest.Mock).mockResolvedValue(false);
    const result = await baseHandler(event);
    expect(result.response.autoConfirmUser).toBe(true);
    expect(result.response.autoVerifyEmail).toBe(true);
  });

  it("should throw if deviceId is missing", async () => {
    await expect(
      baseHandler({
        ...event,
        request: {
          userAttributes: {
            ...event.request.userAttributes,
            "custom:deviceId": "",
          },
        },
      }),
    ).rejects.toThrow(": Missing deviceId");
  });

  it("should throw if username is missing", async () => {
    await expect(baseHandler({ ...event, userName: "" })).rejects.toThrow(
      ": Missing username",
    );
  });

  it.each([
    ["UppercaseUser", "starts with uppercase"],
    ["1username", "starts with number"],
    ["_username", "starts with underscore"],
    ["user-name", "contains hyphen"],
    ["user.name", "contains dot"],
    ["user@name", "contains special character"],
    ["user name", "contains space"],
  ])(
    "should throw if username is invalid: %s (%s)",
    async (invalidUsername) => {
      const inputEvent = { ...event, userName: invalidUsername };
      await expect(baseHandler(inputEvent)).rejects.toThrow(
        ": Username is invalid",
      );
    },
  );

  it("should throw if username is profane", async () => {
    (words.includes as jest.Mock).mockReturnValue(true);
    await expect(baseHandler(event)).rejects.toThrow(": Username is invalid");
  });

  it("should throw if email is missing", async () => {
    await expect(
      baseHandler({
        ...event,
        request: {
          userAttributes: { ...event.request.userAttributes, email: "" },
        },
      }),
    ).rejects.toThrow(": Missing email");
  });

  it("should throw if getHouseholdSize throws", async () => {
    (getHouseholdSize as jest.Mock).mockRejectedValue(new Error("fail"));
    await expect(baseHandler(event)).rejects.toThrow(": Internal Server Error");
  });

  it("should throw if household size is equal or greater than 5", async () => {
    (getHouseholdSize as jest.Mock).mockResolvedValue(5);
    await expect(baseHandler(event)).rejects.toThrow(
      ": Household limit reached",
    );
  });

  it("should throw if isExistingSignInAlias throws", async () => {
    (getHouseholdSize as jest.Mock).mockResolvedValue(2);
    (isExistingSignInAlias as jest.Mock).mockRejectedValue(new Error("fail"));
    await expect(baseHandler(event)).rejects.toThrow(": Internal Server Error");
  });

  it("should throw if isExistingSignInAlias returns true", async () => {
    (getHouseholdSize as jest.Mock).mockResolvedValue(2);
    (isExistingSignInAlias as jest.Mock).mockResolvedValue(true);
    await expect(baseHandler(event)).rejects.toThrow(": Email already exists");
  });

  it.each([
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
    "<EMAIL>",
  ])(
    "should return Invalid email if email domain is disposable one: %s",
    async (disposableEmail) => {
      const inputEvent = {
        ...event,
        request: {
          userAttributes: {
            ...event.request.userAttributes,
            email: disposableEmail,
          },
        },
      };

      await expect(baseHandler(inputEvent)).rejects.toThrow(": Invalid email");
    },
  );
});
