using System.Linq;
using BallManager.StrokeData.DataContracts;

namespace BallManager.StrokeData.Data
{
	public class BallTrajectoryData
	{
		public float[] XFit { get; }
		public float[] YFit { get; }
		public float[] ZFit { get; }
		public float[] TimeInterval { get; }
		
		public BallTrajectoryData(BallTrajectoryDataContract ballTrajectoryDataContract)
		{
			XFit = ballTrajectoryDataContract.XFit.Select(x => (float)x).ToArray();
			YFit = ballTrajectoryDataContract.YFit.Select(y => (float)y).ToArray();
			ZFit = ballTrajectoryDataContract.ZFit.Select(z => (float)z).ToArray();
			TimeInterval = ballTrajectoryDataContract.TimeInterval.Select(t => (float)t).ToArray();
		}
	}
}
