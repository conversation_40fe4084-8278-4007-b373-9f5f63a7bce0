using Testing.Scoring;

namespace Network.Queries
{
    public class GetPastPredictionScoresQuery : BaseQuery
    {
        public override string QueryString => queryString;
        public override string Name => "GetPastPredictionScoresQuery";

        private string queryString;

        public GetPastPredictionScoresQuery(PastPredictionScoresDataInput queryParams)
        {
            queryString = $@"
                query GetPastPredictionScoresQuery {{
                    getPastPredictionScores(input: {{
                        tournamentId: ""{queryParams.TournamentId}"",
                        playerId: ""{queryParams.PlayerId}"",
                        limit: {queryParams.Limit}" +
                        (string.IsNullOrEmpty(queryParams.NextToken) ? "" : $@",nextToken: ""{queryParams.NextToken}""") +
                    $@"}})
                    {{
                        items {{
                            createdTimestamp
                            golferId
                            holeNumber
                            playerId
                            score
                            tournamentId
                            golferName
                        }}
                        nextToken
                    }}
                }}
            ";
        }
    }
}