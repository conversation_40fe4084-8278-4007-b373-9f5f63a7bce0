import { useRef, useState } from "react";
import { useNavigate } from "react-router";
import {
  <PERSON><PERSON>,
  PageDescriptor,
  TextInput,
  PasswordInput,
  Button,
} from "../components";
import { attemptSignIn } from "../services/auth";
import { RecordPageView } from "../services/rum";
import { getFormValue } from "../services/textUtils";

export function Login() {
  const formRef = useRef<HTMLFormElement>(null);
  const [invalidCredentials, setInvalidCredentials] = useState(false);
  const [loading, setLoading] = useState(false);
  const navigate = useNavigate();

  const resetPassword =
    sessionStorage.getItem("successfullyResetPassword") === "true";

  const onSubmit = async () => {
    setLoading(true);
    formRef.current?.classList.add("validated");
    if (!formRef.current || !formRef.current!.checkValidity()) {
      setLoading(false);
      return;
    }
    // submit creation form
    const username = getFormValue(formRef, "Email or Username");
    const password = getFormValue(formRef, "Password");
    try {
      await attemptSignIn(username, password);
    } catch (err) {
      if (err === "UserNotFoundException" || err === "NotAuthorizedException") {
        setInvalidCredentials(true);
      }
    } finally {
      setLoading(false);
    }
  };

  RecordPageView("Login");
  return (
    <div className="h-full w-full flex flex-col">
      <Header />
      <div className="flex flex-col gap-6 px-4 py-6 overflow-y-auto flex-grow">
        <PageDescriptor
          heading="Welcome back"
          description="Login with an option below"
        />
        <form
          className="flex flex-col flex-grow gap-5"
          noValidate
          ref={formRef}
        >
          {resetPassword && (
            <div className="bg-green-500 text-white w-full rounded-md font-bold p-2">
              You have successfully reset your password, please login with your
              new password.
            </div>
          )}
          <TextInput
            label="Email or Username"
            required
            pattern=".*\S+.*"
            onChange={() => setInvalidCredentials(false)}
            error={
              invalidCredentials ? "Incorrect Username or Password" : undefined
            }
          />
          <PasswordInput
            onChange={() => setInvalidCredentials(false)}
            error={invalidCredentials}
          />
          <Button
            onClick={onSubmit}
            label="Start playing"
            className="mt-auto"
            disabled={loading}
          />
          <div
            className="text-white text-center underline font-medium text-base cursor-pointer"
            onClick={() => void navigate("/forgot")}
          >
            Forgot your password?
          </div>
        </form>
      </div>
    </div>
  );
}
