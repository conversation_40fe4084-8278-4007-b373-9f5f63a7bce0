import { SVGProps } from "react";
export const ChevronIcon = ({
  className = "",
  direction = "right",
  ...props
}: {
  direction?: "left" | "right";
} & SVGProps<SVGSVGElement>) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 84 84"
      fillRule="evenodd"
      clipRule="evenodd"
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`${className} ${direction === "left" ? "transform scale-x-[-1]" : ""}`}
      {...props}
    >
      <path
        d="M23.919,77.162l35.495,-35.495l-35.495,-35.495"
        fill="none"
        fillRule="nonzero"
        strokeWidth={11.83}
      />
    </svg>
  );
};
