using System;

namespace Sky.GenEx.InfiniteScroller.Samples.GolfResultsSample
{
    public class PastPredictionScoreData
    {
        public DateTime CreatedTimestamp { get; }
        public int GolferId { get; }
        public int HoleNumber { get; }
        public int Score { get; }
        public string GolferName { get; }

        public PastPredictionScoreData(DateTime createdTimestamp, int golferId, int holeNumber, int score, string golferName)
        {
            CreatedTimestamp = createdTimestamp;
            GolferId = golferId;
            HoleNumber = holeNumber;
            Score = score;
            GolferName = golferName;
        }
    }
}