using Configuration.Impl.DataContract;
using Data;
using PredictionWindow.Data;
using PredictionWindow.DataContracts;
using UnityEngine;

namespace Network.Mutations
{
    public class AddHomePlayerPredictionMutation : BaseMutation
    {
        private PlayerPredictionDataContract input;

        public AddHomePlayerPredictionMutation(PredictionWindowData predictionWindowData, Vector3 position)
        {
            var location = new Vector3DataContract
            {
                X = position.x,
                Y = position.y,
                Z = position.z
            };

            input = new PlayerPredictionDataContract
            {
                TournamentId = predictionWindowData.TournamentId,
                RoundNumber = predictionWindowData.RoundNumber,
                HoleNumber = predictionWindowData.HoleDetails.Number,
                StrokeNumber = predictionWindowData.StrokeNumber,
                GolferId = predictionWindowData.Golfer.PlayerId,
                Location = location,
                PlayerId = SessionData.UserId
            };
        }

        public override string Name => "AddHomePlayerPrediction";

        public override string MutationString => $@"
            mutation AddHomePlayerPrediction {{
              addHomePlayerPrediction(input: {{
                tournamentId: ""{input.TournamentId}"",
                roundNumber: {input.RoundNumber},
                holeNumber: {input.HoleNumber},
                strokeNumber: {input.StrokeNumber},
                golferId: {input.GolferId},
                location: {{ x: {input.Location.X}, y: {input.Location.Y}, z: {input.Location.Z} }},
                playerId: ""{input.PlayerId}""
              }})
            }}";
    }
}