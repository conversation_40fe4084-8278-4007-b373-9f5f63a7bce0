using App.CameraSystem.StateMachine.Data;
using App.CameraSystem.StateMachine.States.Base;
using BallManager.Ball;
using Configuration;
using UnityEngine;

namespace App.CameraSystem.StateMachine.States
{
	public class BeforeShotCamera : BaseCameraState, ICameraStateWithPayload<TrackableTargets>
	{
		[SerializeField] private Transform targetPoint;
		private Vector3 offset;
		
		private IGolfBall ball;
		private Transform hole;

		public void SetPayload(TrackableTargets targets)
		{
			if (targets.Ball == null)
			{
				Debug.LogError("BeforeShotCamera: Ball is null in the provided TrackableTargets.");
				return;	
			}
			if (targets.HoleTransform == null)
			{
				Debug.LogError("BeforeShotCamera: HoleTransform is null in the provided TrackableTargets.");
				return;	
			}
			ball = targets.Ball;
			hole = targets.HoleTransform;
			StateCamera.transform.position = ball.Transform.position;
		}

		public override void Enter()
		{
			offset = ConfigurationManager.Configuration.ConfigurableValues.CameraOffsets.CameraStartingOffset;
			
			targetPoint.SetParent(null);

			SetCameraTarget();
		}

		public override void Exit()
		{
			targetPoint.SetParent(StateCamera.transform);
		}

		private void SetCameraTarget()
		{
			targetPoint.position = new Vector3(hole.position.x, hole.position.y, hole.position.z);
			StateCamera.transform.LookAt(targetPoint);
			StateCamera.LookAt = targetPoint;
			StateCamera.transform.position += StateCamera.transform.TransformDirection(offset);
		}
	}
}