import { Sha256 } from "@aws-crypto/sha256-js";
import { defaultProvider } from "@aws-sdk/credential-provider-node";
import { HttpRequest } from "@smithy/protocol-http";
import { SignatureV4 } from "@smithy/signature-v4";
import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";
import retry from "async-retry";

const createSigner = () =>
  new SignatureV4({
    credentials: defaultProvider(),
    service: "appsync",
    region: "eu-west-1",
    sha256: Sha256,
  });

export type GraphQLRequestBody = {
  query: string;
  operationName?: string;
  variables?: Record<string, any>;
};

type SendGraphQLRequestParams = {
  body: GraphQLRequestBody;
  apiUrl: string;
  apiId: string;
  signer?: SignatureV4;
  options?: { retries: number; minTimeout: number; maxRetryTime: number };
};

type IHttpRequest = ReturnType<typeof SignatureV4.prototype.sign>;

export const signRequest = async (
  body: string,
  apiUrl: string,
  apiId: string,
  signer?: SignatureV4,
): Promise<IHttpRequest> => {
  const url = new URL(apiUrl);
  signer = signer || createSigner();

  const request = new HttpRequest({
    hostname: url.hostname,
    path: url.pathname,
    body,
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "x-verified-bot": apiId,
      host: url.hostname,
    },
  });

  return signer.sign(request);
};

type GraphQLResponse = {
  data: Record<string, any>;
  errors: Array<{ message: string; errorType?: string }>;
};

export const sendGraphQLRequest = async ({
  body,
  apiUrl,
  apiId,
  signer,
  options = { retries: 3, minTimeout: 500, maxRetryTime: 3000 },
}: SendGraphQLRequestParams): Promise<GraphQLResponse> => {
  const signedRequest = await signRequest(
    JSON.stringify(body),
    apiUrl,
    apiId,
    signer,
  );
  return retry(async (_: any, attempt: any) => {
    const response = await fetch(apiUrl, signedRequest);
    const result = (await response.json()) as GraphQLResponse;

    if (result?.errors?.length) {
      const errors = result.errors.map((e) => ({
        message: e.message,
        type: e.errorType,
      }));
      logger.error("GraphQL request failed:", {
        attempt,
        apiUrl,
        errors,
        body,
      });
      throw new Error(result.errors.map((e) => e.message).join(", "));
    }
    return result;
  }, options);
};
