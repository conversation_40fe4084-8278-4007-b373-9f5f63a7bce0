import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as cdk from "aws-cdk-lib/core";
import { Construct } from "constructs";

type DomainStackProps = cdk.NestedStackProps &
  Readonly<{
    stage: string;
  }>;

export class DomainStack extends cdk.NestedStack {
  readonly certificate: acm.ICertificate;
  readonly domainName: string;

  constructor(scope: Construct, id: string, props?: DomainStackProps) {
    super(scope, id, props);

    const domainName = "gamethegreen.com";
    this.certificate = new acm.Certificate(this, "GtgCertificate", {
      domainName,
      subjectAlternativeNames: [`*.${domainName}`],
      validation: acm.CertificateValidation.fromDns(),
      certificateName: "gtg-certificate",
    });
    this.domainName = domainName;
  }
}
