using Network.Services;
using PredictionWindow.Data;
using UnityEngine;

namespace Game.State
{
    public class WaitingForPredictionWindow : BaseGameState
    {
        public override void Enter()
        {
            PredictionWindowService.PredictionWindowReceived += OnPredictionWindowReceived;
        }

        public override void Exit()
        {
            PredictionWindowService.PredictionWindowReceived -= OnPredictionWindowReceived;
        }

        private void OnPredictionWindowReceived(PredictionWindowData predictionWindowData)
        {
            if (predictionWindowData.Status != PredictionWindowStatus.OPEN)
            {
                Debug.Log("Prediction Window is not open, ignoring.");
                return;
            }
            
            predictionWindowManager.OpenPredictionWindow(predictionWindowData);

            onFinished?.Invoke(GameStateType.PredictionWindowOpen);
        }
    }
}
