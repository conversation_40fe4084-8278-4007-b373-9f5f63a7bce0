// ~~ Generated by projen. To modify, edit .projenrc.js and run "bunx projen".
{
  "env": {
    "jest": true,
    "node": true
  },
  "root": true,
  "plugins": [
    "@typescript-eslint",
    "import"
  ],
  "parser": "@typescript-eslint/parser",
  "parserOptions": {
    "ecmaVersion": 2018,
    "sourceType": "module",
    "project": "./tsconfig.dev.json"
  },
  "extends": [
    "plugin:import/typescript",
    "plugin:prettier/recommended"
  ],
  "settings": {
    "import/parsers": {
      "@typescript-eslint/parser": [
        ".ts",
        ".tsx"
      ]
    },
    "import/resolver": {
      "node": {},
      "typescript": {
        "project": "./tsconfig.dev.json",
        "alwaysTryTypes": true
      }
    }
  },
  "ignorePatterns": [
    "*.js",
    "*.d.ts",
    "node_modules/",
    "*.generated.ts",
    "coverage",
    "jest.setup.ts",
    "!.projenrc.ts",
    "!projenrc/**/*.ts",
    "**/build/*"
  ],
  "rules": {
    "curly": [
      "error",
      "multi-line",
      "consistent"
    ],
    "@typescript-eslint/no-require-imports": "error",
    "import/no-extraneous-dependencies": [
      "error",
      {
        "devDependencies": [
          "**/src/**/*.test.tsx",
          "**/src/setupTests.ts"
        ],
        "optionalDependencies": false,
        "peerDependencies": true
      }
    ],
    "import/no-unresolved": [
      "error"
    ],
    "import/order": [
      "warn",
      {
        "groups": [
          "builtin",
          "external"
        ],
        "alphabetize": {
          "order": "asc",
          "caseInsensitive": true
        }
      }
    ],
    "import/no-duplicates": [
      "error"
    ],
    "no-shadow": [
      "off"
    ],
    "@typescript-eslint/no-shadow": "error",
    "@typescript-eslint/no-floating-promises": "error",
    "no-return-await": [
      "off"
    ],
    "@typescript-eslint/return-await": "error",
    "dot-notation": [
      "error"
    ],
    "no-bitwise": [
      "error"
    ],
    "@typescript-eslint/member-ordering": [
      "error",
      {
        "default": [
          "public-static-field",
          "public-static-method",
          "protected-static-field",
          "protected-static-method",
          "private-static-field",
          "private-static-method",
          "field",
          "constructor",
          "method"
        ]
      }
    ]
  },
  "overrides": []
}
