using App.CameraSystem.StateMachine.States.Base;
using UnityEngine;

namespace App.CameraSystem.StateMachine.States
{
    public class StartingCamera : BaseCameraState
    {

        protected override void Initialize()
        {
        }

        public override void Enter()
        {            
            Debug.LogWarning("Starting camera course panning implementation is not implemented.");
        }

        public override void Exit()
        {           
            StateCamera.LookAt = null;
        }
    }
}
