type ButtonProps = {
  className?: string;
  label: string;
  icon?: string;
  onClick?: () => void;
  disabled?: boolean;
};

export const Button = ({
  label,
  onClick,
  icon,
  className = "",
  disabled = false,
}: ButtonProps) => {
  return (
    <div
      className={`bg-gold rounded-[64px] px-6 py-4 text-center flex gap-2 justify-center items-center ${!disabled ? "cursor-pointer hover:opacity-80" : "opacity-30"} ${className}`}
      onClick={disabled ? undefined : onClick}
    >
      {icon && (
        <div className="relative h-full aspect-square">
          <img className="object-contain" src={icon} alt={`${label} icon`} />
        </div>
      )}
      <div>{label}</div>
    </div>
  );
};
