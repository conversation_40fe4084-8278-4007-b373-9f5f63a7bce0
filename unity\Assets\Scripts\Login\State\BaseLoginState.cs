﻿using System;
using UnityEngine;

namespace Login.State
{
    public abstract class BaseLoginState : MonoBehaviour, IAuthState
    {
        protected Action<AuthState> onFinished;

        public void SetupState(Action<AuthState> onFinished)
        {
            this.onFinished = onFinished ?? throw new ArgumentNullException(nameof(onFinished), "onFinished callback cannot be null.");
        }

        public abstract void Enter();
        public abstract void Exit();
    }
}
