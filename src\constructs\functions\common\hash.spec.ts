import { hash } from "./hash";

describe("hash", () => {
  it("should return the same hash for the same input, case and whitespace insensitive", () => {
    const input1 = "<EMAIL> ";
    const input2 = " <EMAIL>";
    expect(hash(input1)).toBe(hash(input2));
  });

  it("should return a 64 character hex string", () => {
    const result = hash("abc");
    expect(result).toMatch(/^[a-f0-9]{64}$/);
  });

  it("should produce different hashes for different inputs", () => {
    expect(hash("abc")).not.toBe(hash("def"));
  });
});
