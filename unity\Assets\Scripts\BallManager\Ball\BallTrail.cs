using UnityEngine;

namespace BallManager.Ball
{
    public class BallTrail : MonoBehaviour
    {
        [SerializeField] private TrailRenderer trailRenderer;
        [SerializeField] private Color color = Color.cyan;
        
        public void Initialize()
        {
            trailRenderer.startColor = color;
            trailRenderer.endColor = new Color(color.r, color.g, color.b, 0);
        }
    }
}