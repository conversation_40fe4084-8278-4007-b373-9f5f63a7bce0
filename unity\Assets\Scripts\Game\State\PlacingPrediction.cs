using Network.Services;
using Prediction;
using PredictionWindow.Data;
using Sky.GenEx.ArtToolKit;
using UnityEngine;
using UnityEngine.UI;

namespace Game.State
{
    public class PlacingPrediction : BaseGameState
    {
        [SerializeField] private GameObject placingPredictionUi;
        [SerializeField] private PredictionPinUi predictionPinUi;
        [SerializeField] private LineDrawer lineDrawer;
        [SerializeField] private Image predictionPinImage;

        public override void Enter()
        {
            tabMenuBackButton.OnBackButtonPressed += BackButtonPressed;
            predictionWindowManager.PredictionWindowTimer.OnTimerEnd += ClosePredictionWindow;
            PredictionWindowService.PredictionWindowReceived += OnPredictionWindowReceived;
            predictionManager.OnPredictionPlaced += OnPredictionPlaced;

            placingPredictionUi.SetActive(true);
            predictionPinUi.SetHeadshot(predictionManager.CurrentPredictionWindowData.Golfer.PlayerId);
        }

        public override void Exit()
        {
            tabMenuBackButton.OnBackButtonPressed -= BackButtonPressed;
            predictionWindowManager.PredictionWindowTimer.OnTimerEnd -= ClosePredictionWindow;
            PredictionWindowService.PredictionWindowReceived -= OnPredictionWindowReceived;
            predictionManager.OnPredictionPlaced -= OnPredictionPlaced;
            
            placingPredictionUi.SetActive(false);
            lineDrawer.RemoveLine();
            predictionManager.StopPrediction();
            predictionPinImage.gameObject.SetActive(false);
        }
        
        private void BackButtonPressed()
        {
            onFinished?.Invoke(GameStateType.ResetGame);
        }

        private void OnPredictionWindowReceived(PredictionWindowData predictionWindowData)
        {
            if (predictionWindowData.Status != PredictionWindowStatus.CLOSED)
            {
                Debug.Log("Prediction Window is not closing, ignoring.");
                return;
            }

            ClosePredictionWindow();
        }

        private void ClosePredictionWindow()
        {
            predictionWindowManager.ClosePredictionWindow();
            predictionManager.ConfirmPrediction(true);

            onFinished?.Invoke(GameStateType.WaitingForStroke);
        }
        
        private void OnPredictionPlaced()
        {
            predictionWindowManager.ClosePredictionWindow();
            onFinished?.Invoke(GameStateType.WaitingForStroke);
        }
    }
}
