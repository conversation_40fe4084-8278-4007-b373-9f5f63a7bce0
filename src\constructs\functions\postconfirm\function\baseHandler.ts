import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";
import { getEnvironmentVariable } from "@sky-uk/coins-utilities";
import { PostConfirmationTriggerEvent } from "aws-lambda";
import { addUserToLeaderboard } from "./utils/addUserToLeaderboard";
import { getTournamentId } from "./utils/getTournamentId";
import { incrementHouseholdSize } from "./utils/incrementHouseholdSize";
import { publishSignUpSuccess } from "./utils/publishSignUpSuccess";
import { hash } from "../../common/hash";

const LEADERBOARD_API_URL = getEnvironmentVariable("LEADERBOARD_API_URL");
const LEADERBOARD_API_ID = getEnvironmentVariable("LEADERBOARD_API_ID");
const GOLF_API_URL = getEnvironmentVariable("GOLF_API_URL");
const GOLF_API_ID = getEnvironmentVariable("GOLF_API_ID");
const HOUSEHOLD_TABLE_NAME = getEnvironmentVariable("HOUSEHOLD_TABLE_NAME");
const MAX_HOUSEHOLD_SIZE = getEnvironmentVariable("MAX_HOUSEHOLD_SIZE");
const APP_VARIANT = getEnvironmentVariable("APP_VARIANT"); // NOTE: comma separated list of app variants, e.g "home,onsite"

export const baseHandler = async (event: PostConfirmationTriggerEvent) => {
  if (event.triggerSource !== "PostConfirmation_ConfirmSignUp") {
    return event;
  }

  const userName = event.userName;
  if (!userName) {
    throw new Error(": Missing username");
  }

  const email = event.request.userAttributes.email;
  if (!email) {
    throw new Error(": Missing email");
  }

  const userId = event.request.userAttributes.sub;
  if (!userId) {
    throw new Error(": Missing userId");
  }

  if (APP_VARIANT.split(",").includes("onsite")) {
    try {
      const tournamentId = await getTournamentId({
        apiId: GOLF_API_ID,
        apiUrl: GOLF_API_URL,
      });
      await addUserToLeaderboard({
        userId,
        gameId: tournamentId,
        apiId: LEADERBOARD_API_ID,
        apiUrl: LEADERBOARD_API_URL,
      });
    } catch (error) {
      logger.error("Error Publishing Sign Up Success", { error });
      throw new Error(": Unable to publish sign up success");
    }
  }

  if (APP_VARIANT.split(",").includes("home")) {
    const deviceId = event.request.userAttributes["custom:deviceId"];
    if (!deviceId) {
      throw new Error(": Missing deviceId");
    }

    try {
      await incrementHouseholdSize({
        deviceId,
        signInAlias: hash(email),
        tableName: HOUSEHOLD_TABLE_NAME,
        maxHouseholdSize: Number(MAX_HOUSEHOLD_SIZE),
      });
    } catch (error) {
      logger.error("Error Incrementing Household Size", { error });
      throw new Error(": Unable to confirm user");
    }

    try {
      await publishSignUpSuccess({
        deviceId,
        userId,
        apiId: GOLF_API_ID,
        apiUrl: GOLF_API_URL,
      });
    } catch (error) {
      logger.error("Error Publishing Sign Up Success", { error });
      throw new Error(": Unable to publish sign up success");
    }
  }

  return event;
};
