fileFormatVersion: 2
guid: 7fa1f9e8c2e4188478cbd5a152ce790e
timeCreated: **********
licenseType: Pro
ModelImporter:
  serializedVersion: 22
  fileIDToRecycleName:
    100000: Head
    100002: HeadTop_End
    100004: Hips
    100006: LeftArm
    100008: LeftFoot
    100010: LeftForeArm
    100012: LeftHand
    100014: LeftHandIndex1
    100016: LeftHandIndex2
    100018: LeftHandIndex3
    100020: LeftHandIndex4
    100022: LeftHandMiddle1
    100024: LeftHandMiddle2
    100026: LeftHandMiddle3
    100028: LeftHandMiddle4
    100030: LeftHandPinky1
    100032: LeftHandPinky2
    100034: LeftHandPinky3
    100036: LeftHandPinky4
    100038: LeftHandRing1
    100040: LeftHandRing2
    100042: LeftHandRing3
    100044: LeftHandRing4
    100046: LeftHandThumb1
    100048: LeftHandThumb2
    100050: LeftHandThumb3
    100052: LeftHandThumb4
    100054: Left<PERSON>eg
    100056: Left<PERSON>houlder
    100058: LeftToe_End
    100060: Left<PERSON><PERSON>Base
    100062: LeftUpLeg
    100064: Neck
    100066: RightArm
    100068: RightFoot
    100070: RightForeArm
    100072: RightHand
    100074: RightHandIndex1
    100076: RightHandIndex2
    100078: RightHandIndex3
    100080: RightHandIndex4
    100082: RightHandMiddle1
    100084: RightHandMiddle2
    100086: RightHandMiddle3
    100088: RightHandMiddle4
    100090: RightHandPinky1
    100092: RightHandPinky2
    100094: RightHandPinky3
    100096: RightHandPinky4
    100098: RightHandRing1
    100100: RightHandRing2
    100102: RightHandRing3
    100104: RightHandRing4
    100106: RightHandThumb1
    100108: RightHandThumb2
    100110: RightHandThumb3
    100112: RightHandThumb4
    100114: RightLeg
    100116: RightShoulder
    100118: RightToe_End
    100120: RightToeBase
    100122: RightUpLeg
    100124: Spine
    100126: Spine1
    100128: Spine2
    100130: //RootNode
    400000: Head
    400002: HeadTop_End
    400004: Hips
    400006: LeftArm
    400008: LeftFoot
    400010: LeftForeArm
    400012: LeftHand
    400014: LeftHandIndex1
    400016: LeftHandIndex2
    400018: LeftHandIndex3
    400020: LeftHandIndex4
    400022: LeftHandMiddle1
    400024: LeftHandMiddle2
    400026: LeftHandMiddle3
    400028: LeftHandMiddle4
    400030: LeftHandPinky1
    400032: LeftHandPinky2
    400034: LeftHandPinky3
    400036: LeftHandPinky4
    400038: LeftHandRing1
    400040: LeftHandRing2
    400042: LeftHandRing3
    400044: LeftHandRing4
    400046: LeftHandThumb1
    400048: LeftHandThumb2
    400050: LeftHandThumb3
    400052: LeftHandThumb4
    400054: LeftLeg
    400056: LeftShoulder
    400058: LeftToe_End
    400060: LeftToeBase
    400062: LeftUpLeg
    400064: Neck
    400066: RightArm
    400068: RightFoot
    400070: RightForeArm
    400072: RightHand
    400074: RightHandIndex1
    400076: RightHandIndex2
    400078: RightHandIndex3
    400080: RightHandIndex4
    400082: RightHandMiddle1
    400084: RightHandMiddle2
    400086: RightHandMiddle3
    400088: RightHandMiddle4
    400090: RightHandPinky1
    400092: RightHandPinky2
    400094: RightHandPinky3
    400096: RightHandPinky4
    400098: RightHandRing1
    400100: RightHandRing2
    400102: RightHandRing3
    400104: RightHandRing4
    400106: RightHandThumb1
    400108: RightHandThumb2
    400110: RightHandThumb3
    400112: RightHandThumb4
    400114: RightLeg
    400116: RightShoulder
    400118: RightToe_End
    400120: RightToeBase
    400122: RightUpLeg
    400124: Spine
    400126: Spine1
    400128: Spine2
    400130: //RootNode
    7400000: Sprint
    9500000: //RootNode
  externalObjects: {}
  materials:
    importMaterials: 1
    materialName: 0
    materialSearch: 1
    materialLocation: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    importAnimatedCustomProperties: 0
    animationCompression: 3
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    extraUserProperties: []
    clipAnimations:
    - serializedVersion: 16
      name: Sprint
      takeName: sprint
      firstFrame: 0
      lastFrame: 26
      wrapMode: 0
      orientationOffsetY: 0
      level: 0
      cycleOffset: 1
      loop: 0
      hasAdditiveReferencePose: 0
      loopTime: 1
      loopBlend: 1
      loopBlendOrientation: 1
      loopBlendPositionY: 1
      loopBlendPositionXZ: 0
      keepOriginalOrientation: 0
      keepOriginalPositionY: 1
      keepOriginalPositionXZ: 1
      heightFromFeet: 0
      mirror: 1
      bodyMask: 01000000010000000100000001000000010000000100000001000000010000000100000001000000010000000100000001000000
      curves: []
      events: []
      transformMask: []
      maskType: 3
      maskSource: {instanceID: 0}
      additiveReferencePoseFrame: 0
    isReadable: 1
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importVisibility: 1
    importBlendShapes: 1
    importCameras: 1
    importLights: 1
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 1
    keepQuads: 0
    weldVertices: 1
    preserveHierarchy: 0
    indexFormat: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 60
    normalImportMode: 0
    tangentImportMode: 3
    normalCalculationMode: 4
  importAnimation: 1
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human:
    - boneName: Hips
      humanName: Hips
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftUpLeg
      humanName: LeftUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightUpLeg
      humanName: RightUpperLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftLeg
      humanName: LeftLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightLeg
      humanName: RightLowerLeg
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftFoot
      humanName: LeftFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightFoot
      humanName: RightFoot
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine
      humanName: Spine
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine1
      humanName: Chest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Neck
      humanName: Neck
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Head
      humanName: Head
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftShoulder
      humanName: LeftShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightShoulder
      humanName: RightShoulder
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftArm
      humanName: LeftUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightArm
      humanName: RightUpperArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftForeArm
      humanName: LeftLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightForeArm
      humanName: RightLowerArm
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHand
      humanName: LeftHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHand
      humanName: RightHand
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftToeBase
      humanName: LeftToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightToeBase
      humanName: RightToes
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb1
      humanName: Left Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb2
      humanName: Left Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandThumb3
      humanName: Left Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex1
      humanName: Left Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex2
      humanName: Left Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandIndex3
      humanName: Left Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle1
      humanName: Left Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle2
      humanName: Left Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandMiddle3
      humanName: Left Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing1
      humanName: Left Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing2
      humanName: Left Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandRing3
      humanName: Left Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky1
      humanName: Left Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky2
      humanName: Left Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: LeftHandPinky3
      humanName: Left Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb1
      humanName: Right Thumb Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb2
      humanName: Right Thumb Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandThumb3
      humanName: Right Thumb Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex1
      humanName: Right Index Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex2
      humanName: Right Index Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandIndex3
      humanName: Right Index Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle1
      humanName: Right Middle Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle2
      humanName: Right Middle Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandMiddle3
      humanName: Right Middle Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing1
      humanName: Right Ring Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing2
      humanName: Right Ring Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandRing3
      humanName: Right Ring Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky1
      humanName: Right Little Proximal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky2
      humanName: Right Little Intermediate
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: RightHandPinky3
      humanName: Right Little Distal
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    - boneName: Spine2
      humanName: UpperChest
      limit:
        min: {x: 0, y: 0, z: 0}
        max: {x: 0, y: 0, z: 0}
        value: {x: 0, y: 0, z: 0}
        length: 0
        modified: 0
    skeleton:
    - name: sprint(Clone)
      parentName: 
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Hips
      parentName: sprint(Clone)
      position: {x: -0.00550724, y: 0.97447234, z: -0.017625129}
      rotation: {x: -0.19369522, y: 0.1295161, z: -0.019728174, w: 0.97227496}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftUpLeg
      parentName: Hips
      position: {x: -0.110282145, y: -0.05455154, z: 0.020432295}
      rotation: {x: -0.18755136, y: -0.10271159, z: 0.9765241, w: 0.02599319}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftLeg
      parentName: LeftUpLeg
      position: {x: -1.8451196e-10, y: 0.47321993, z: -1.8309243e-11}
      rotation: {x: 0.29119927, y: 0.10484575, z: -0.009592211, w: -0.95085144}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftFoot
      parentName: LeftLeg
      position: {x: -9.287238e-12, y: 0.4299028, z: -5.687911e-10}
      rotation: {x: 0.64248466, y: 0.06464317, z: -0.038975827, w: 0.76257175}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToeBase
      parentName: LeftFoot
      position: {x: 2.020566e-10, y: 0.16963747, z: -0.000000001319964}
      rotation: {x: 0.3068817, y: 0.01971296, z: -0.0041099335, w: 0.9515346}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftToe_End
      parentName: LeftToeBase
      position: {x: 1.8547522e-10, y: 0.06318586, z: 1.867302e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightUpLeg
      parentName: Hips
      position: {x: 0.110282145, y: -0.05455154, z: 0.017048035}
      rotation: {x: 0.028641315, y: -0.075698234, z: 0.9958427, w: -0.04179612}
      scale: {x: 1, y: 1, z: 1}
    - name: RightLeg
      parentName: RightUpLeg
      position: {x: -8.8142875e-11, y: 0.47321326, z: 3.4250394e-11}
      rotation: {x: -0.32675835, y: 0.014365785, z: 0.044563647, w: 0.9439474}
      scale: {x: 1, y: 1, z: 1}
    - name: RightFoot
      parentName: RightLeg
      position: {x: 7.748421e-11, y: 0.42999738, z: -0.0000000010980948}
      rotation: {x: 0.7911156, y: -0.06399647, z: -0.10740287, w: 0.5987531}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToeBase
      parentName: RightFoot
      position: {x: 4.9132715e-11, y: 0.168777, z: 0.0000000051328195}
      rotation: {x: 0.40253216, y: -0.024237372, z: 0.00047910254, w: 0.91508484}
      scale: {x: 1, y: 1, z: 1}
    - name: RightToe_End
      parentName: RightToeBase
      position: {x: -2.4023825e-10, y: 0.06348027, z: 3.2390646e-11}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine
      parentName: Hips
      position: {x: -6.938894e-20, y: 0.09820991, z: -0.0017021184}
      rotation: {x: 0.28037083, y: -0.17383103, z: 0.07027151, w: 0.9414016}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine1
      parentName: Spine
      position: {x: -0, y: 0.11459545, z: 9.309463e-11}
      rotation: {x: 0.0037980492, y: -0.025475238, z: -0.07086971, w: 0.997153}
      scale: {x: 1, y: 1, z: 1}
    - name: Spine2
      parentName: Spine1
      position: {x: -0, y: 0.13096617, z: 5.0059998e-11}
      rotation: {x: 0.0012819235, y: -0.030491026, z: -0.068865106, w: 0.9971591}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftShoulder
      parentName: Spine2
      position: {x: -0.06315917, y: 0.13062665, z: -0.0008211929}
      rotation: {x: 0.53497916, y: -0.49268988, z: 0.5643987, w: 0.39052287}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftArm
      parentName: LeftShoulder
      position: {x: -3.7948155e-11, y: 0.13195542, z: 3.1595163e-10}
      rotation: {x: -0.058610268, y: -0.2663789, z: -0.1368518, w: 0.95230186}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftForeArm
      parentName: LeftArm
      position: {x: 3.1594272e-11, y: 0.284728, z: -5.65646e-11}
      rotation: {x: -0.011147974, y: 0.02240077, z: -0.005132632, w: 0.9996737}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHand
      parentName: LeftForeArm
      position: {x: -1.6414761e-10, y: 0.29908314, z: -8.136567e-12}
      rotation: {x: -0.14468066, y: -0.0040011425, z: 0.0065360093, w: 0.9894487}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex1
      parentName: LeftHand
      position: {x: 0.032593433, y: 0.08819151, z: 0.0048023863}
      rotation: {x: 0.06558897, y: 0.0073326207, z: -0.05902667, w: 0.99607235}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex2
      parentName: LeftHandIndex1
      position: {x: 0.000060722, y: 0.030705823, z: -1.1863221e-12}
      rotation: {x: 0.013164223, y: 0.020097781, z: -0.00060798245, w: 0.9997113}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex3
      parentName: LeftHandIndex2
      position: {x: -0.000013546999, y: 0.029072577, z: 1.2777264e-11}
      rotation: {x: 0.021666463, y: 0.012781646, z: 0.00028183273, w: 0.99968356}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandIndex4
      parentName: LeftHandIndex3
      position: {x: -0.00004717472, y: 0.02576944, z: -4.2829525e-11}
      rotation: {x: -1.3877788e-17, y: 1.3877788e-17, z: 5.551115e-17, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle1
      parentName: LeftHand
      position: {x: 0.009294576, y: 0.097100526, z: -0.0013351972}
      rotation: {x: 0.052564796, y: -0.0433199, z: -0.036694992, w: 0.9970025}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle2
      parentName: LeftHandMiddle1
      position: {x: 0.000088627334, y: 0.028160678, z: 2.533568e-11}
      rotation: {x: 0.015038164, y: -0.057408035, z: -0.0019045074, w: 0.99823576}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle3
      parentName: LeftHandMiddle2
      position: {x: -0.00005698787, y: 0.027361773, z: 1.3540387e-11}
      rotation: {x: 0.020691473, y: -0.04327271, z: 0.0013830638, w: 0.99884814}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandMiddle4
      parentName: LeftHandMiddle3
      position: {x: -0.000031638716, y: 0.023456343, z: -4.8478908e-12}
      rotation: {x: 4.1633363e-17, y: 2.7755576e-17, z: -1.1555581e-33, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky1
      parentName: LeftHand
      position: {x: -0.03189784, y: 0.07679171, z: 0.0044959183}
      rotation: {x: 0.05483667, y: 0.02830481, z: -0.03927665, w: 0.997321}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky2
      parentName: LeftHandPinky1
      position: {x: 0.0000062537842, y: 0.026215078, z: 5.2560265e-11}
      rotation: {x: 0.0131715005, y: 0.0644277, z: -0.0007380761, w: 0.99783516}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky3
      parentName: LeftHandPinky2
      position: {x: -0.000045871086, y: 0.02047027, z: 5.604903e-11}
      rotation: {x: 0.024440125, y: 0.04087371, z: 0.0029271212, w: 0.99886113}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandPinky4
      parentName: LeftHandPinky3
      position: {x: 0.000039617014, y: 0.019160997, z: 1.4733246e-11}
      rotation: {x: 0.000000034708414, y: 0.00000003827634, z: 0.00000007061518, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing1
      parentName: LeftHand
      position: {x: -0.009990172, y: 0.11161971, z: -0.0016355749}
      rotation: {x: 0.058641195, y: 0.012401693, z: -0.051645543, w: 0.99686515}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing2
      parentName: LeftHandRing1
      position: {x: -0.000019485717, y: 0.018662993, z: 3.7682638e-10}
      rotation: {x: 0.009951674, y: 0.11121421, z: 0.0030195836, w: 0.9937421}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing3
      parentName: LeftHandRing2
      position: {x: 0.00007238568, y: 0.019729609, z: -3.828717e-11}
      rotation: {x: 0.027675672, y: 0.046158686, z: -0.00186518, w: 0.998549}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandRing4
      parentName: LeftHandRing3
      position: {x: -0.00005289956, y: 0.014939406, z: 2.2617769e-10}
      rotation: {x: -0.000000043328622, y: 0.000000011541253, z: -0.00000008456816,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb1
      parentName: LeftHand
      position: {x: 0.019087303, y: 0.027286228, z: 0.016212132}
      rotation: {x: -0.19964336, y: 0.027008485, z: -0.33444622, w: 0.9206296}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb2
      parentName: LeftHandThumb1
      position: {x: -0.00061445613, y: 0.029622665, z: -4.0107379e-10}
      rotation: {x: 0.006589649, y: 0.008074749, z: 0.051395208, w: 0.99862397}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb3
      parentName: LeftHandThumb2
      position: {x: 0.0005632979, y: 0.03286468, z: -0.0000000010128807}
      rotation: {x: 0.017256508, y: -0.093036495, z: -0.029086173, w: 0.9950882}
      scale: {x: 1, y: 1, z: 1}
    - name: LeftHandThumb4
      parentName: LeftHandThumb3
      position: {x: 0.000051161922, y: 0.028500622, z: 0.0000000012282865}
      rotation: {x: -0.000000027217396, y: -0.0000000073511166, z: -0.0000000092380725,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: Neck
      parentName: Spine2
      position: {x: -8.673617e-21, y: 0.14733703, z: 0.0000000033478065}
      rotation: {x: -0.0584258, y: -0.010697919, z: 0.058124453, w: 0.99654084}
      scale: {x: 1, y: 1, z: 1}
    - name: Head
      parentName: Neck
      position: {x: -8.673617e-21, y: 0.04752304, z: 0.020397963}
      rotation: {x: -0.27073398, y: 0.025680395, z: 0.09143535, w: 0.9579578}
      scale: {x: 1, y: 1, z: 1}
    - name: HeadTop_End
      parentName: Head
      position: {x: -0, y: 0.20985276, z: 0.09007348}
      rotation: {x: 0, y: -0, z: -0, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightShoulder
      parentName: Spine2
      position: {x: 0.06315917, y: 0.13060822, z: 0.0002416501}
      rotation: {x: 0.5311364, y: 0.48519367, z: -0.4978301, w: 0.48440313}
      scale: {x: 1, y: 1, z: 1}
    - name: RightArm
      parentName: RightShoulder
      position: {x: -2.660434e-11, y: 0.13195542, z: 3.171084e-10}
      rotation: {x: -0.134773, y: -0.28457654, z: -0.06525929, w: 0.9468863}
      scale: {x: 1, y: 1, z: 1}
    - name: RightForeArm
      parentName: RightArm
      position: {x: -2.7981567e-10, y: 0.284544, z: -5.9894485e-11}
      rotation: {x: -0.007793529, y: -0.014527317, z: -0.005228656, w: 0.9998505}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHand
      parentName: RightForeArm
      position: {x: -1.5198842e-10, y: 0.29908353, z: 8.6913587e-13}
      rotation: {x: 0.035721082, y: 0.114178844, z: 0.026565984, w: 0.99246234}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex1
      parentName: RightHand
      position: {x: -0.03621528, y: 0.11747307, z: 0.0033559613}
      rotation: {x: 0.0524729, y: 0.04343282, z: 0.041823693, w: 0.9968004}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex2
      parentName: RightHandIndex1
      position: {x: -0.0000019000119, y: 0.02174354, z: 3.6810093e-11}
      rotation: {x: 0.011867927, y: 0.003534509, z: -0.00075000187, w: 0.9999231}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex3
      parentName: RightHandIndex2
      position: {x: 0.000018414778, y: 0.020176124, z: 6.7367978e-12}
      rotation: {x: 0.026373353, y: 0.028049963, z: -0.008306528, w: 0.9992241}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandIndex4
      parentName: RightHandIndex3
      position: {x: -0.000016515174, y: 0.016536057, z: 6.2972955e-10}
      rotation: {x: 9.7144515e-17, y: 1.3877788e-17, z: -5.551115e-17, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle1
      parentName: RightHand
      position: {x: -0.011585564, y: 0.106287256, z: -0.0023898676}
      rotation: {x: 0.046652418, y: 0.049720593, z: 0.044874888, w: 0.9966633}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle2
      parentName: RightHandMiddle1
      position: {x: 0.000025064648, y: 0.027443916, z: -2.6771574e-10}
      rotation: {x: 0.01989389, y: 0.010097592, z: -0.002825514, w: 0.9997471}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle3
      parentName: RightHandMiddle2
      position: {x: -0.00001572167, y: 0.025285017, z: -1.1165099e-10}
      rotation: {x: 0.01527896, y: 0.015733033, z: -0.0014033741, w: 0.9997585}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandMiddle4
      parentName: RightHandMiddle3
      position: {x: -0.0000093429935, y: 0.023478843, z: 1.5592434e-11}
      rotation: {x: -0.00000002863045, y: -0.00000003802838, z: -0.000000047822283,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky1
      parentName: RightHand
      position: {x: 0.03568082, y: 0.08469697, z: 0.0003023846}
      rotation: {x: 0.052002903, y: 0.008791964, z: 0.027307397, w: 0.9982348}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky2
      parentName: RightHandPinky1
      position: {x: -0.00001235679, y: 0.023127176, z: -2.687406e-10}
      rotation: {x: 0.011901441, y: 0.0149534205, z: -0.0008683394, w: 0.99981695}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky3
      parentName: RightHandPinky2
      position: {x: 0.0000057242146, y: 0.020767946, z: 1.4345573e-11}
      rotation: {x: 0.012557565, y: 0.011164036, z: -0.001320609, w: 0.999858}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandPinky4
      parentName: RightHandPinky3
      position: {x: 0.000006632599, y: 0.018836992, z: 5.6214446e-11}
      rotation: {x: 0.000000009763971, y: 6.245005e-17, z: 1.908196e-17, w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing1
      parentName: RightHand
      position: {x: 0.012120027, y: 0.11076295, z: -0.0005365405}
      rotation: {x: 0.047482584, y: 0.040163137, z: 0.04738934, w: 0.99693865}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing2
      parentName: RightHandRing1
      position: {x: 0.0000005502636, y: 0.022161495, z: -1.15574605e-10}
      rotation: {x: 0.014759773, y: -0.036408927, z: -0.0017165091, w: 0.9992265}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing3
      parentName: RightHandRing2
      position: {x: -0.00000786604, y: 0.020914963, z: -8.724754e-11}
      rotation: {x: 0.009583685, y: -0.045677073, z: -0.00067889126, w: 0.99891007}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandRing4
      parentName: RightHandRing3
      position: {x: 0.0000073158517, y: 0.018207468, z: -8.404868e-12}
      rotation: {x: -0.000000016082875, y: -0.00000006832993, z: -0.000000029431177,
        w: 1}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb1
      parentName: RightHand
      position: {x: -0.018816011, y: 0.029109262, z: 0.01581974}
      rotation: {x: 0.05666478, y: 0.036555234, z: 0.37795043, w: 0.92336684}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb2
      parentName: RightHandThumb1
      position: {x: 0.0010551222, y: 0.030462235, z: 8.4468466e-10}
      rotation: {x: -0.0048617814, y: 0.04161012, z: -0.083476014, w: 0.9956288}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb3
      parentName: RightHandThumb2
      position: {x: -0.0017913599, y: 0.032419857, z: 1.5043099e-10}
      rotation: {x: -0.034006752, y: 0.33353123, z: 0.053808197, w: 0.94058764}
      scale: {x: 1, y: 1, z: 1}
    - name: RightHandThumb4
      parentName: RightHandThumb3
      position: {x: 0.0007362342, y: 0.02916256, z: 0.0000000014472283}
      rotation: {x: 3.469447e-18, y: -2.7755576e-17, z: 9.62965e-35, w: 1}
      scale: {x: 1, y: 1, z: 1}
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 1
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 3
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
