using Newtonsoft.Json;

namespace BallManager.StrokeData.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class CoordinateLocationDataContract
    {
        [JsonProperty("code")]
        public string LocationShort;

        [JsonProperty("text")]
        public string LocationLong;

        [JsonProperty("x")]
        public float? X;

        [JsonProperty("y")]
        public float? Y;

        [JsonProperty("z")]
        public float? Z;
    }
}
