using Newtonsoft.Json;

namespace Configuration.Impl.DataContract
{
    [JsonObject(MemberSerialization.OptIn)]
    public class AWSUrlDataContract
    {
        [JsonProperty("graphql_url")]
        public string GraphQlUrl { get; private set; }

        [JsonProperty("ws_url")]
        public string WebsocketUrl { get; private set; }

        [JsonProperty("leaderboard_url")]
        public string LeaderboardUrl { get; private set; }

        [JsonProperty("ws_auth_host_url")]
        public string WebsocketAuthHostUrl { get; private set; }

        [JsonProperty("headshots_url")]
        public string HeadshotsUrl { get; private set; }
    }
}
