using BallManager.StrokeData.DataContracts;
using UnityEngine;

namespace BallManager.StrokeData.Data
{
	public class HoleDetailsData
	{
		public Vector3 TeePosition { get; }
		public Vector3 PinPosition { get; }
		public Vector3 TrajectoryAlignmentPosition { get; }
		public int Number { get; }
		public int Par { get; }

		public HoleDetailsData(HoleDetailsDataContract dataContract)
		{
			Number = dataContract.HoleNumber;
			Par = dataContract.ParNumber ?? 0;
			TeePosition = GetCoordinate(dataContract.Tee);
			PinPosition = GetCoordinate(dataContract.Pin);
			TrajectoryAlignmentPosition = SetTrajectoryAlignmentPosition(dataContract.Pin, dataContract.CenterOfFairway);
		}

		private Vector3 GetCoordinate(CoordinatesDataContract coordinatesDataContract)
		{
			return new CoordinateData(coordinatesDataContract).Coordinate;
		}

		private Vector3 SetTrajectoryAlignmentPosition(CoordinatesDataContract pin, CoordinatesDataContract centerOfFairway)
		{
			if (centerOfFairway.X == 0 & centerOfFairway.Y == 0 & centerOfFairway.Z == 0)
			{
				return GetCoordinate(pin);
			}

			return GetCoordinate(centerOfFairway);
		}
	}
}
