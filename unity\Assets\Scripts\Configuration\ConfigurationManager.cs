using System;
using Configuration.Impl.Data;
using Configuration.Impl.DataContract;
using Sky.GenEx.Files.Impl;
using Sky.GenEx.Promises;
using Sky.GenEx.Promises.Impl;
using UnityEngine;

namespace Configuration
{
    public static class ConfigurationManager
    {
        private const string ConfigFileNameLocal = "config.json";
        
        public static event Action ConfigLoaded;

        public static Config Configuration { get; private set; }

        public static IPromise LoadConfig()
        {
            var outcome = GetConfigPromise();
            outcome.Then(OnLoadComplete);
            outcome.Fail(error => { Debug.LogError($"Error loading config file: {error}"); });

            return new PromiseWrapper<ConfigurationDataContract>(outcome);
        }

        private static void OnLoadComplete(ConfigurationDataContract configDataContract)
        {
            Debug.Log($"Config file loaded");

            try
            {
                Configuration = new Config(configDataContract);
            }
            catch (Exception exception)
            {
                Debug.LogError(exception);
            }

            ConfigLoaded?.Invoke();
        }

        private static IPromise<ConfigurationDataContract> GetConfigPromise()
        {
                return FileService.Instance
                    .LoadJson<ConfigurationDataContract>(Application.streamingAssetsPath, ConfigFileNameLocal);
        }
    }
}