#!/usr/bin/env node

const fs = require("fs");

const reactConfigPath = "./ui/src/config.json";
const webAppOutputs = require("../../dist/app-web-outputs.json");

const newConfig = {
  config: {},
};

const webAppOutputKeys = [
  "GolfApiUrl",
  "GolfApiWsUrl",
  "LeaderboardApiUrl",
  "UserPoolClientId",
  "UserPoolId",
  "UserPoolDomain",
  "WebappDomainName",
  "AppEnv",
  "RumRegion",
  "RumIdentityPoolId",
  "RumAppMonitorId",
  "RumProxyUrl",
];

const configKey = Object.keys(webAppOutputs)[0];

webAppOutputKeys.forEach((key) => {
  newConfig.config[key] = webAppOutputs[configKey][key];
});

fs.writeFileSync(reactConfigPath, JSON.stringify(newConfig, null, 2));
