using Network.Auth;
using Network.Payload;
using UnityEngine;

namespace Data
{
    public static class SessionData
    {
        private const string FtueKey = "show_ftue";

        public static string UserName { get; private set; } = "DefaultUser";
        public static string UserId { get; private set; } = "DefaultId";
        public static string TournamentId { get; private set; } = "R2025011";
        public static string TvDeviceId { get; private set; } = "DefaultEditorTvDevice";

        public static void BindToTokenProvider(ITokenProvider tokenProvider)
        {
            tokenProvider.TokenRefreshed -= TokenRefreshed;
            tokenProvider.TokenRefreshed += TokenRefreshed;
            
            TokenRefreshed(tokenProvider.AccessToken);
        }

        private static void TokenRefreshed(string accessToken)
        {
            if (string.IsNullOrEmpty(accessToken))
                return;

            AccessTokenPayload accessTokenPayload = AccessTokenReader.ReadAccessToken(accessToken);
            if (accessTokenPayload != null)
            {
                SetUserName(accessTokenPayload.username);
                SetUserId(accessTokenPayload.sub);
            }
        }

        public static bool ShowFTUE
        {
            get => PlayerPrefs.GetString(FtueKey, "YES") == "YES";
        }

        private static void SetUserName(string userName)
        {
            UserName = userName;
            Debug.Log($"User name set to: {UserName}");
        }

        private static void SetUserId(string userId)
        {
            UserId = userId;
            Debug.Log($"User id set to: {UserId}");
        }

        public static void SetTournamentId(string tournamentId)
        {
            TournamentId = tournamentId;
            Debug.Log($"Tournament id set to: {TournamentId}");
        }

        public static void SetTvDeviceId(string deviceId)
        {
            TvDeviceId = deviceId;
            Debug.Log($"Device id set to: {TvDeviceId}");
        }

        public static void ToggleFTUE(bool show)
        {            
            PlayerPrefs.SetString(FtueKey, (show) ? "YES" : "NO");
            PlayerPrefs.Save();
            Debug.Log("First Time User Experience " + (show ? "enabled." : "completed."));
        }
    }
}