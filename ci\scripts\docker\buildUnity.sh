#!/bin/bash
DEFAULT_BUILD_TARGET="android"

helpFunction()
{
   echo ""
   echo "Usage: $0 -b builtTarget"
   echo "-h -> help"
   echo "-b buildTarget -> Unity build target, default is $DEFAULT_BUILD_TARGET"
   exit 1
}

# Read the command line arguments
while getopts "b:h" opt
do
   case "$opt" in
      b)
         if [ -z "$OPTARG" ]; then 
            echo "Error: -b requires an argument"
            helpFunction
         fi
         BUILD_TARGET="$OPTARG" ;;
      h)
         helpFunction ;;
      *)
         helpFunction ;;
   esac
done

if [ -z "$BUILD_TARGET" ]
then
   echo "No build target specified, defaulting to $DEFAULT_BUILD_TARGET";
   BUILD_TARGET=$DEFAULT_BUILD_TARGET
fi

echo "Building unity to target $BUILD_TARGET"

# Install dependencies
apt-get update -y -qq
apt-get upgrade -y -qq
apt install -y -q curl
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip -q awscliv2.zip
./aws/install
aws --version
echo "Installed AWS CLI version: $(aws --version)"

if [ "${BUILD_TARGET,,}" == "android" ]; then
  echo "Building for Android"
  BUILD_FUNCTION_EXTENSION="Android"
elif [ "${BUILD_TARGET,,}" == "webgl" ]; then
  echo "Building for WebGL"
  BUILD_FUNCTION_EXTENSION="WebGL"
else
  echo "Unknown or unsupported build target: $BUILD_TARGET"
  exit 1
fi

# Do Build
cd unity
npx @sky-uk/automation-unity@1.0.7 build -u "$UNITY_USERNAME" -p "$UNITY_PASSWORD" -s "$UNITY_SERIAL" -d "$BUILD_DIR" -e "Editor.BuildScript.PerformBuild$BUILD_FUNCTION_EXTENSION" -t $BUILD_TARGET