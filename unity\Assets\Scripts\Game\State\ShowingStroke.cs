using Data;
using Scoring.Data;

namespace Game.State
{
    public class ShowingStroke : BaseGameState
    {
        private PredictionScoreData scoreData;
        
        public override void Enter()
        {
            tabMenuBackButton.OnBackButtonPressed += BackButtonPressed;
            strokeManager.onFinished += OnStrokeFinished;

            GetScore();
        }

        public override void Exit()
        {
            tabMenuBackButton.OnBackButtonPressed -= BackButtonPressed;
            strokeManager.onFinished -= OnStrokeFinished;
        }
        
        private void BackButtonPressed()
        {
            onFinished?.Invoke(GameStateType.ResetGame);
        }

        private void GetScore()
        {
            scoreManager.GetScore(
                SessionData.TournamentId,
                strokeManager.CurrentStrokeData,
                SessionData.UserName
            );
        }

        private void OnStrokeFinished()
        {
            strokeManager.onFinished -= OnStrokeFinished;
            onFinished?.Invoke(GameStateType.ShowingScore);
        }
    }
}
