import { Component, ErrorInfo, ReactNode } from "react";
import { getAwsRum } from "../services/rum";

interface ErrorBoundaryProps {
  children: ReactNode;
}

interface ErrorBoundaryState {
  hasError: boolean;
}

class ErrorBoundary extends Component<ErrorBoundaryProps, ErrorBoundaryState> {
  static getDerivedStateFromError(_: Error): ErrorBoundaryState {
    return { hasError: true };
  }

  constructor(props: ErrorBoundaryProps) {
    super(props);
    this.state = { hasError: false };
  }

  componentDidCatch(error: Error, _errorInfo: ErrorInfo) {
    getAwsRum()?.recordError(error);
  }

  render(): ReactNode {
    if (this.state.hasError) {
      return;
    }
    return this.props.children;
  }
}

export default ErrorBoundary;
