using System;
using System.IO;
using Constants;
using Data;
using Network.Auth;
using Sky.GenEx.Toast;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Bootstrap
{
    public class BootstrapManager : MonoBehaviour
    {
        private async void Start()
        {
            try
            {
                BootProcesses.ExtractDeviceId();
                await BootProcesses.AsyncLoadConfig();
                await AuthTokenManager.Instance.Initialize();
                SessionData.BindToTokenProvider(AuthTokenManager.Instance);
                DetermineSceneToLoad();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong, ToastDuration.Long);
            }
        }

        private async void DetermineSceneToLoad()
        {
            await Awaitable.WaitForSecondsAsync(3);
            LoadAppFirstScene();
        }

        private async void LoadAppFirstScene()
        {
            try
            {
                if (AuthTokenManager.Instance.IsRefreshTokenValid)
                {
                    await BootProcesses.StartNetworkProcesses();
                    LoadSceneByPath(BootstrapKeys.MAIN_SCENE_PATH);
                }
                else
                {
                    Debug.LogWarning("Refresh token is invalid, redirecting to login scene.");
                    LoadSceneByPath(BootstrapKeys.LOGIN_SCENE_PATH);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong, ToastDuration.Long,
                    ToastType.Error);
            }
        }

        private void LoadSceneByPath(string scenePath)
        {
            if (string.IsNullOrEmpty(scenePath))
            {
                Debug.LogWarning("Scene path is empty, loading default first scene.");
                scenePath = BootstrapKeys.LOGIN_SCENE_PATH;
            }

            var sceneName = Path.GetFileNameWithoutExtension(scenePath);

            var currentSceneName = SceneManager.GetActiveScene().name;

            if (string.Equals(currentSceneName, sceneName, StringComparison.OrdinalIgnoreCase))
            {
                Debug.Log($"Scene '{sceneName}' is already loaded. Skipping load.");
                return;
            }

            Debug.Log($"Loading scene: {sceneName}");
            SceneManager.LoadScene(sceneName);
        }
    }
}