using UnityEngine;

namespace Flag
{
    public class FlagManager : MonoBehaviour
    {
        [SerializeField] private GameObject flagPrefab;

        public GameObject Flag => currentFlag;

        private FlagFactory flagFactory;
        private GameObject currentFlag;
        
        void Start()
        {
            flagFactory = new FlagFactory(flagPrefab, transform);
        }

        public void GenerateFlag(Vector3 flagPosition)
        {
            var flag = flagFactory.GenerateFlag(flagPosition);
            DestroyFlag();
            currentFlag = flag;
        }
        
        
        public void DestroyFlag()
        {
            if (currentFlag != null)
            {
                Destroy(currentFlag);
            }
        }
    }
}