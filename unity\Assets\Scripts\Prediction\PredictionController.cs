using System.Collections.Generic;
using App.CameraSystem;
using DG.Tweening;
using Inputs;
using Sky.GenEx.ArtToolKit;
using Sky.GenEx.Utilities;
using TMPro;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

namespace Prediction
{
    public class PredictionController : MonoBehaviour
    {
        [SerializeField] private TextMeshProUGUI distanceText;
        [SerializeField] private LineDrawer lineToTeeDrawer;
        [SerializeField] private PredictionArea predictionArea;

        [SerializeField] private CameraDirector cameraDirector;
        [SerializeField] private PredictionManager predictionManager;
        [SerializeField] private PredictionDragAnimationHandler dragAnimationHandler;

        
        private MobileInputModel mobileInputModel;
        private GraphicRaycaster[] graphicsRaycasters;
        
        private Vector3 lastDragPosition;
        private Vector3 lastRayHitPoint;

        private bool isScreenBeingMoved = false;
        private bool isZoomBeingChanged = false;


        public void Start()
        {
            mobileInputModel = FindFirstObjectByType<MobileInputModel>();
            if (mobileInputModel == null)
            {
                Debug.LogError("No MobileInputModel found in the scene.");
            }
            
            graphicsRaycasters = FindObjectsByType<GraphicRaycaster>(FindObjectsInactive.Include, FindObjectsSortMode.None);
            if (graphicsRaycasters == null || graphicsRaycasters.Length == 0)
            {
                Debug.LogError("No GraphicRaycasters found in the scene.");
            }
        }

        public void OnDestroy()
        {
            mobileInputModel.OnTapMove -= OnScreenMoved;
            mobileInputModel.OnTapRelease -= OnScreenReleased;
        }

        public void StartPrediction()
        {
            CheckPinPosition();
            UpdateDistanceUI();
            
            mobileInputModel.OnTapMove += OnScreenMoved;
            mobileInputModel.OnTapRelease += OnScreenReleased;
        }

        public void StopPrediction()
        {
            if (mobileInputModel != null)
            {
                mobileInputModel.OnTapMove -= OnScreenMoved;
                mobileInputModel.OnTapRelease -= OnScreenReleased;
            }

            if (isScreenBeingMoved)
            {
                dragAnimationHandler.StopDragging(false);
            }
        }
        
        #region Input Handlers

        private void OnScreenMoved(Vector2 position)
        {
            if (IsPointerPositionOverUI(position))
            {
                return;
            }

            if (!isScreenBeingMoved)
            {
                isScreenBeingMoved = true;
                dragAnimationHandler.StartDragging();
            }
            
            if (lastDragPosition != Vector3.zero)
            {
                Vector3 currentTouchPosition = position;
                
                Vector3 difference = cameraDirector.GetScreenToWorldPosition(new Vector3(currentTouchPosition.x, currentTouchPosition.y, 0)) -
                                     cameraDirector.GetScreenToWorldPosition(new Vector3(lastDragPosition.x, lastDragPosition.y, 0));
                
                predictionManager.MoveCameraFocusByAmount(new Vector3(-difference.x, 0, -difference.z));
            }
            
            lastDragPosition = position;
            CheckPinPosition();
            UpdateDistanceUI();
        }

        private void OnScreenReleased(Vector2 position)
        {  
            if (IsPointerPositionOverUI(position))
            {
                return;
            }

            if (isScreenBeingMoved)
            {
                dragAnimationHandler.StopDragging(true);
                isScreenBeingMoved = false;
            }
            
            lastDragPosition = Vector3.zero;
            
            CheckPinPosition();
            UpdateDistanceUI();
        }
        
        #endregion
        
        private bool IsPointerPositionOverUI(Vector2 screenPosition)
        {
            var pointerEventData = new PointerEventData(EventSystem.current)
            {
                position = screenPosition
            };
            
            foreach (var raycaster in graphicsRaycasters)
            {
                var raycastResults = new List<RaycastResult>();
                raycaster.Raycast(pointerEventData, raycastResults);
                var hit =  raycastResults.Count > 0 ? raycastResults[0] : new RaycastResult();
                
                if (hit.gameObject != null && hit.gameObject.layer == LayerMask.NameToLayer("UI")) 
                {
                    return true;
                }
            }
            return false;
        }
        
        private void CheckPinPosition()
        {
            var ray = cameraDirector.GetScreenPointToRay(new Vector2(Screen.width / 2, Screen.height / 2));

            if (Physics.Raycast(ray, out var hit, 1000f, LayerMask.GetMask("BettingSurface")))
            {
                lastRayHitPoint = hit.point;
            }
            
            predictionManager.SetPrediction(lastRayHitPoint);
        }
        
        private void UpdateDistanceUI()
        {
            var teePosition = predictionManager.CurrentPredictionWindowData.HoleDetails.TeePosition;
            lineToTeeDrawer.CreateLine(teePosition, lastRayHitPoint);

            var terrainType = predictionArea.WorldToTerrain(lastRayHitPoint);
            
            var distanceMetres = Vector3.Distance(teePosition, lastRayHitPoint);
            var distanceYards = (int)UnitsUtilities.ConvertMetresToYards(distanceMetres);
            distanceText.text = terrainType + " - " + distanceYards + " yds";
        }
    }
}
