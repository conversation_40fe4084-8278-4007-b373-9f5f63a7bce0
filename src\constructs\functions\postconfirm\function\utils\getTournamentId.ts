import { sendGraphQLRequest } from "@/utils/graphql";

type GetTournamentId = {
  apiUrl: string;
  apiId: string;
};

export const getTournamentId = async (
  props: GetTournamentId,
): Promise<string> => {
  const { apiId, apiUrl } = props;
  const query = {
    query: `
        query GetCurrentTournament {
          getCurrentTournament
        }
     `,
    operationName: "GetCurrentTournament",
  };

  const response = await sendGraphQLRequest({
    body: query,
    apiUrl,
    apiId,
  });

  return response.data.getCurrentTournament;
};
