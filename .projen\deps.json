{"dependencies": [{"name": "@aws-cdk/aws-cognito-identitypool-alpha", "version": "2.181.1-alpha.0", "type": "build"}, {"name": "@aws-crypto/sha256-js", "type": "build"}, {"name": "@aws-sdk/credential-provider-node", "type": "build"}, {"name": "@aws-solutions-constructs/aws-cloudfront-s3", "type": "build"}, {"name": "@aws-solutions-constructs/aws-wafwebacl-cloudfront", "type": "build"}, {"name": "@comcast/gee-aws-cdk-shared", "type": "build"}, {"name": "@smithy/protocol-http", "type": "build"}, {"name": "@smithy/signature-v4", "type": "build"}, {"name": "@types/async-retry", "type": "build"}, {"name": "@types/aws-lambda", "type": "build"}, {"name": "@types/jest", "type": "build"}, {"name": "@types/node", "version": "^22", "type": "build"}, {"name": "@typescript-eslint/eslint-plugin", "version": "^8", "type": "build"}, {"name": "@typescript-eslint/parser", "version": "^8", "type": "build"}, {"name": "aws-cdk", "version": "^2.1005.0", "type": "build"}, {"name": "aws-sdk-client-mock", "type": "build"}, {"name": "aws-sdk-client-mock-jest", "type": "build"}, {"name": "cdk-iam-actions", "type": "build"}, {"name": "esbuild", "type": "build"}, {"name": "eslint-config-prettier", "version": "^8", "type": "build"}, {"name": "eslint-import-resolver-typescript", "type": "build"}, {"name": "eslint-plugin-import", "type": "build"}, {"name": "eslint-plugin-prettier", "type": "build"}, {"name": "eslint", "version": "^8", "type": "build"}, {"name": "husky", "type": "build"}, {"name": "jest", "type": "build"}, {"name": "jest-extended", "type": "build"}, {"name": "jest-junit", "version": "^16", "type": "build"}, {"name": "json-schema-to-ts", "type": "build"}, {"name": "lint-staged", "type": "build"}, {"name": "prettier", "type": "build"}, {"name": "projen", "version": "^0.91.6", "type": "build"}, {"name": "ts-jest", "type": "build"}, {"name": "ts-node", "type": "build"}, {"name": "tsconfig-paths", "type": "build"}, {"name": "typescript", "type": "build"}, {"name": "uuid", "type": "build"}, {"name": "@aws-cdk/integ-runner", "version": "latest", "type": "devenv"}, {"name": "@aws-cdk/integ-tests-alpha", "version": "latest", "type": "devenv"}, {"name": "@aws-lambda-powertools/logger", "version": "^1.4.1", "type": "runtime"}, {"name": "@aws-lambda-powertools/parameters", "type": "runtime"}, {"name": "@aws-sdk/client-cognito-identity-provider", "type": "runtime"}, {"name": "@aws-sdk/client-dynamodb", "type": "runtime"}, {"name": "@middy/core", "type": "runtime"}, {"name": "@middy/validator", "type": "runtime"}, {"name": "@sky-uk/coins-o11y-utils", "type": "runtime"}, {"name": "@sky-uk/coins-utilities", "type": "runtime"}, {"name": "ajv", "type": "runtime"}, {"name": "async-retry", "type": "runtime"}, {"name": "aws-cdk-lib", "version": "2.187.0", "type": "runtime"}, {"name": "aws-lambda", "type": "runtime"}, {"name": "constructs", "version": "^10.0.5", "type": "runtime"}, {"name": "fakefilter", "type": "runtime"}, {"name": "profane-words", "type": "runtime"}], "//": "~~ Generated by projen. To modify, edit .projenrc.ts and run \"bunx projen\"."}