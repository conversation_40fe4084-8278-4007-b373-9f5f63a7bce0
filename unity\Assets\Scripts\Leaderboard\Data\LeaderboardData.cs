using System;
using Network.DataContracts;

namespace Leaderboard.Data
{
    public class LeaderboardEntryData
    {
        public int Rank { get; }
        public int Score { get; }
        public string UserName { get; }

        public LeaderboardEntryData(LeaderboardEntryDataContract dataContract)
        {
            Rank = dataContract.Rank;
            Score = dataContract.Score;
            UserName = dataContract.UserName;
        }
    }

    public class LeaderboardData
    {
        public LeaderboardEntryData[] Prev { get; }
        public LeaderboardEntryData Current { get; }
        public LeaderboardEntryData[] Next { get; }

        public LeaderboardData(LeaderboardDataContract dataContract)
        {
            Current = new LeaderboardEntryData(dataContract.LeaderboardByUserId.Current);
            Prev = Array.ConvertAll(dataContract.LeaderboardByUserId.Prev, entry => new LeaderboardEntryData(entry));
            Next = Array.ConvertAll(dataContract.LeaderboardByUserId.Next, entry => new LeaderboardEntryData(entry));
        }
    }
}