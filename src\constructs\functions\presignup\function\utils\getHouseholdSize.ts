import { DynamoDBClient, GetItemCommand } from "@aws-sdk/client-dynamodb";
import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";

const dynamoDbClient = new DynamoDBClient();

type GetHouseholdSizeProps = {
  tableName: string;
  deviceId: string;
};

export const getHouseholdSize = async (
  props: GetHouseholdSizeProps,
): Promise<number> => {
  try {
    const { tableName, deviceId } = props;
    const response = await dynamoDbClient.send(
      new GetItemCommand({
        TableName: tableName,
        Key: {
          pk: { S: deviceId },
          signInAlias: { S: "META#COUNTER" },
        },
      }),
    );

    const userCountItem = response.Item?.userCount?.N ?? 0;
    const householdSize = Number(userCountItem);

    logger.info("Household", { deviceId, householdSize });

    return householdSize;
  } catch (error: any) {
    logger.error("Get household size failed", { error });
    throw new Error("Get household size failed");
  }
};
