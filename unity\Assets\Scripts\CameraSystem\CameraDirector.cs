using App.CameraSystem.StateMachine;
using App.CameraSystem.StateMachine.Data;
using App.CameraSystem.StateMachine.Types;
using BallManager;
using UnityEngine;

namespace App.CameraSystem
{
    [RequireComponent(typeof(ICameraSystem))]
    public class CameraDirector : MonoBehaviour, ICameraDirector
    {
        [SerializeField] private GolfBallManager golfBallManager;
        private ICameraSystem cameraSystem;
        private TrackableTargets trackableTargets;
        private bool trackingInProgress = false;

        private const float BallFinishedCameraHoldTime = 5f; // Time from ball finishing moving to camera reset

        private void Start()
        {
            cameraSystem = GetComponent<ICameraSystem>();
            if (golfBallManager == null)
            {
                Debug.LogError("GolfBallManager is not assigned in CameraDirector.");
            }
        }

        public void StartPredictionCamera(PredictionPayload predictionPayload)
        {
            trackingInProgress = true;
            cameraSystem.ChangeState(CameraStateType.Prediction, predictionPayload);
        }

        public void PrepareShotTracking(Transform holeTransform, Vector3? predictionLocation = null)
        {
            if (holeTransform == null)
            {
                Debug.LogError("HoleTransform is null. Cannot start tracking shot.");
                return;
            }

            if (trackingInProgress)
            {
                Debug.LogWarning("Tracking is already in progress. Resetting camera before starting new tracking.");
                ClearTracking();
            }

            trackableTargets = new TrackableTargets
            {
                Ball = golfBallManager.Ball,
                HoleTransform = holeTransform,
                PredictionLocation = predictionLocation
            };

            trackingInProgress = true;

            golfBallManager.StartedMoving += OnStrokeHit;

            cameraSystem.ChangeState(CameraStateType.BeforeShot, trackableTargets);
        }
        
        public Vector3 GetScreenToWorldPosition(Vector3 screenPosition)
        {
            if (cameraSystem == null)
            {
                Debug.LogError("CameraSystem is not initialized.");
                return Vector3.zero;
            }

            return cameraSystem.Brain.OutputCamera.ScreenToWorldPoint(screenPosition);
        }

        public Ray GetScreenPointToRay(Vector3 screenPosition)
        {
            if (cameraSystem == null)
            {
                Debug.LogError("CameraSystem is not initialized.");
                return default;
            }

            Ray ray = cameraSystem.Brain.OutputCamera.ScreenPointToRay(screenPosition);

#if UNITY_EDITOR
            Debug.DrawRay(ray.origin, ray.direction * 100, Color.red, 0.2f);
#endif
            return ray;
        }

        private void OnStrokeHit()
        {
            golfBallManager.StartedMoving -= OnStrokeHit;
            golfBallManager.BallLanded += OnBallLanded;
            if (golfBallManager.Ball.HasTrajectory)
            {
                cameraSystem.ChangeState(CameraStateType.BallInAir, trackableTargets.Ball);
            }
            else
            {
                cameraSystem.ChangeState(CameraStateType.MidPuttShot, trackableTargets);
            }
        }

        private void OnBallLanded()
        {
            golfBallManager.BallLanded -= OnBallLanded;
            golfBallManager.FinishedMoving += OnBallFinishedMoving;
            cameraSystem.ChangeState(CameraStateType.BallLanded, trackableTargets);
        }

        private void OnBallFinishedMoving() => Invoke(nameof(ResetCamera), BallFinishedCameraHoldTime);

        public void ResetCamera()
        {
            Debug.Log("Stopping tracking shot.");
            cameraSystem.ChangeState(CameraStateType.StartingView);
            ClearTracking();
        }

        private void ClearTracking()
        {
            if (golfBallManager != null)
            {
                golfBallManager.StartedMoving -= OnStrokeHit;
                golfBallManager.BallLanded -= OnBallLanded;
                golfBallManager.FinishedMoving -= OnBallFinishedMoving;
            }
            CancelInvoke(nameof(ResetCamera));
            trackingInProgress = false;
            trackableTargets = null;
        }
    }
}
