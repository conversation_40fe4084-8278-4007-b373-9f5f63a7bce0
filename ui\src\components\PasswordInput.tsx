import {
  ForwardedRef,
  InputHTMLAttributes,
  useEffect,
  useRef,
  useState,
} from "react";
import { EyeIcon } from "./icons";
import { TextInput } from "./TextInput";

const passwordRequirements = [
  {
    label: "8 characters",
    check: (password: string) => password.length >= 8,
  },
  {
    label: "1 uppercase letter",
    check: (password: string) => /[A-Z]/.test(password),
  },
  {
    label: "1 lowercase letter",
    check: (password: string) => /[a-z]/.test(password),
  },
  {
    label: "1 number",
    check: (password: string) => /\d/.test(password),
  },
  {
    label: "1 symbol",
    check: (password: string) =>
      /[$*.[\]{}()?\"!@#%&/\\,><':;|_~`=+\-]/.test(password),
  },
];

export const PasswordInput = ({
  validate = false,
  ...props
}: {
  validate?: boolean;
  error?: string | boolean;
} & InputHTMLAttributes<HTMLInputElement>) => {
  const ref = useRef<HTMLInputElement>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [requirementsMet, setRequirementsMet] = useState(
    Object.fromEntries(passwordRequirements.map((_v, i) => [i, false])),
  );

  useEffect(() => {
    if (!validate) return;
    if (Object.values(requirementsMet).includes(false)) {
      ref.current?.setCustomValidity("Password does not meet requirements");
    } else {
      ref.current?.setCustomValidity("");
    }
  }, [requirementsMet]);

  return (
    <div className="relative">
      <TextInput
        label="Password"
        type={showPassword ? "text" : "password"}
        ref={ref}
        required
        icon={
          <EyeIcon
            className="w-5 h-5 right-5 absolute top-1/2 transform -translate-y-1/2 cursor-pointer"
            onClick={() => setShowPassword((state) => !state)}
            disabled={showPassword}
          />
        }
        {...props}
      />
      {validate && (
        <div className="pt-5 pb-2">
          <div className="text-white font-medium text-base">
            Minimum requirements
          </div>
          <div className="text-white/80 text-sm">
            {passwordRequirements.map((requirement, index) => (
              <Requirement
                key={index}
                target={ref}
                {...requirement}
                onCheck={(v) =>
                  setRequirementsMet((state) => ({ ...state, [index]: v }))
                }
              />
            ))}
          </div>
        </div>
      )}
    </div>
  );
};

const Requirement = ({
  label,
  check,
  target,
  onCheck,
}: {
  label: string;
  check: (password: string) => boolean;
  onCheck: (v: boolean) => void;
  target?: ForwardedRef<HTMLInputElement>;
}) => {
  const [passed, setPassed] = useState(false);

  const performCheck = () => {
    if (target && "current" in target) {
      const password = target?.current?.value || "";
      const v = check(password);
      setPassed(v);
      onCheck(v);
    } else {
      setPassed(false);
      onCheck(false);
    }
  };

  useEffect(() => {
    if (target && "current" in target) {
      target.current?.addEventListener("input", performCheck);
      return () => {
        target.current?.removeEventListener("input", performCheck);
      };
    }
    return;
  }, [target]);

  return (
    <div className="flex gap-2">
      <div className={`${passed ? "text-gold" : "text-white/30"}`}>✓</div>
      <div>{label}</div>
    </div>
  );
};
