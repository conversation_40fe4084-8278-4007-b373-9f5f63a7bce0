using UnityEngine;
using UnityEngine.UI;

namespace Headshots
{
    public class BulkHeadshotTester : MonoBehaviour
    {
        [SerializeField] private Image[] images;
        [SerializeField] private int[] golferIds;

        public void FetchHeadshots()
        {
            var min = Mathf.Min(images.Length, golferIds.Length);
            for (int i = 0; i < min; i++)
            {
                var golferId = golferIds[i];
                if (golferId > 0)
                {
                    var imageIndex = i;
                    var headshotRequest = HeadshotService.GetHeadshot(golferId);
                    var awaiter = headshotRequest.GetAwaiter();
                    awaiter.OnCompleted(() =>
                    {
                        var headshot = awaiter.GetResult();
                        if (headshot?.ProfilePhoto != null)
                        {
                            images[imageIndex].sprite = headshot.ProfilePhoto;
                        }
                        else
                        {
                            Debug.LogWarning($"No headshot found for golfer ID: {golferId}");
                        }
                    });
                }
                else
                {
                    Debug.LogError($"Invalid golfer ID: {golferIds[i]}");
                }
            }
        }

        /// <summary>
        /// Clears images from UI. Does NOT remove images from the HeadshotService cache.
        /// </summary>
        public void ClearImages()
        {
            foreach (var image in images)
            {
                image.sprite = null;
            }
        }
    }
}