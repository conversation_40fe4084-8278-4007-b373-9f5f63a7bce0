using System;
using System.Collections;
using System.Linq;
using App.Constants;
using BallManager.StrokeData;
using UnityEngine;

namespace BallManager.Ball
{
    public class BallMovement : MonoBehaviour
    {
        public event Action StartedMoving;
        public event Action Landed;
        public event Action FinishedMoving;
        
        [SerializeField] private Rigidbody physicsRigidbody;
        [SerializeField] private SphereCollider sphereCollider;
        
        private const float RadarPointDuration = 0.03f;
        private const float FallingTime = 5f;
        private const float RollingTime = 2f;
        private const float RangeAllowed = 0.1f;
        private const float RangeOfHeightToCheck = 1f;
        private const float HeightFromGround = 0.1f;

        private IStrokeData strokeData;

        private bool isRadaring;
        private bool isFalling; 
        private bool isRolling;
        
        private float timeSinceLastRadarPoint;
        private int currentRadarPointIndex;
        private Vector3 previousPosition;
        private float rollingTimeRemaining;
        
        #region Setup
        
        public void Initialize(IStrokeData strokeData)
        {
            this.strokeData = strokeData;
            
            rollingTimeRemaining = RollingTime;
        }
        
        private float? GetNonRadarStartPoint()
        {
            var position = transform.position;
            var startPoint = new Vector3(position.x, position.y + RangeOfHeightToCheck, position.z);
            var endPoint = new Vector3(position.x, position.y - RangeOfHeightToCheck, position.z);
            
            if (Physics.Linecast(startPoint, endPoint, out var hitPoint))
            {
                return hitPoint.point.y + HeightFromGround;
            }
            return null;
        }
        
        #endregion
        
        #region Unity Functions
        
        private void FixedUpdate()
        {
            if (isRadaring)
            {
                timeSinceLastRadarPoint += Time.fixedDeltaTime;
                if (IsFutureCollision() & currentRadarPointIndex >= strokeData.Radar.Points.Count()/2)
                {
                    isRadaring = false;
                    OnCompleteRadar();
                }
                else
                {
                    MoveBallRadar();
                }
            }
            
            if (isRolling)
            {
                if (IsBallAtFinalPosition())
                {
                    OnMovementFinished();
                }
                else
                {
                    AddForceTowardsFinalPosition();
                }
            }
        }

        private void OnCollisionEnter(Collision other)
        {
            if (isFalling)
            {
                isFalling = false;
                Landed?.Invoke();
                isRolling = true;
            }
        }
        
        #endregion
        
        #region Start Shot
        
        public void StartShot()
        {
            StartedMoving?.Invoke();
            EnableCollisionsAndPhysics(false);

            if (strokeData.Radar.IsValid)
            {
                isRadaring = true;
            }
            else
            {
                StartNonRadarStroke();
            }
        }
        
        private void StartNonRadarStroke()
        {
            var newY = GetNonRadarStartPoint() ?? strokeData.BallStartingPosition.y;
            transform.position = new Vector3(transform.position.x, newY, transform.position.z);

            BeginFalling(Vector3.zero);
        }
        
        #endregion
        
        #region Radar
        
        private void MoveBallRadar()
        {
            previousPosition = transform.localPosition;
            if (timeSinceLastRadarPoint > RadarPointDuration)
            {
                if (currentRadarPointIndex >= strokeData.Radar.Points.Count() - 1)
                {
                    isRadaring = false;
                    OnCompleteRadar();
                    return;
                }

                timeSinceLastRadarPoint -= RadarPointDuration;
                currentRadarPointIndex += 1;
            }
            
            float interpolationRatio = timeSinceLastRadarPoint / RadarPointDuration;
            var nextPoint = strokeData.Radar.Points[currentRadarPointIndex];
            var newPoint = transform.localPosition + (nextPoint - transform.localPosition) * interpolationRatio;
            transform.localPosition = newPoint;
        }
        
        private bool IsFutureCollision()
        {
            var pastDirection = transform.localPosition - previousPosition;
            var endPoint = transform.localPosition + pastDirection * 10;
            return Physics.Linecast(transform.localPosition, endPoint, out var hitPoint);
        }
        
        private void OnCompleteRadar()
        {
            BeginFalling(strokeData.Radar.FinalSpeed);
        }
        
        #endregion
        
        #region Falling
        
        private void BeginFalling(Vector3 initialFallingSpeed)
        {
            EnableCollisionsAndPhysics(true);
            
            physicsRigidbody.linearVelocity = initialFallingSpeed;
            
            StartCoroutine(FallingTimer());
        }
        
        private IEnumerator FallingTimer()
        {
            yield return new WaitForSeconds(FallingTime);
            if (isFalling)
            {
                //If after FallingTime seconds there's been no collision, just go to final position
                Landed?.Invoke();
                OnMovementFinished();
            }
        }
        
        #endregion
        
        #region Rolling
        
        private void AddForceTowardsFinalPosition()
        {
            var displacement = strokeData.To.Coordinate - transform.localPosition;
            var timeRatio = (RollingTime - rollingTimeRemaining) / RollingTime;
                    
            var targetRollingVelocity = displacement / rollingTimeRemaining;
            var velocityChange =  (targetRollingVelocity - physicsRigidbody.linearVelocity) * timeRatio;
            var force = physicsRigidbody.mass * velocityChange / Time.deltaTime; 
            
            physicsRigidbody.AddForce(force);
            rollingTimeRemaining -= Time.deltaTime;
        }
        
        private bool IsBallAtFinalPosition()
        {
            var position = transform.localPosition;
            var ballAtHole = !(strokeData.To.Coordinate.x - RangeAllowed > position.x
                               || strokeData.To.Coordinate.x + RangeAllowed < position.x
                               || strokeData.To.Coordinate.z - RangeAllowed > position.z
                               || strokeData.To.Coordinate.z + RangeAllowed < position.z);

            return (ballAtHole || rollingTimeRemaining <= 0
                               || strokeData.ToLocationShort == AppConstants.BallLocationWater);
        }
        
        #endregion

        #region Finish Movement
        
        private void OnMovementFinished()
        {
            isRolling = false;
            EnableCollisionsAndPhysics(false);
            
            transform.localPosition = strokeData.To.Coordinate;
            FinishedMoving?.Invoke();
            enabled = false;
        }
        
        #endregion
        
        private void EnableCollisionsAndPhysics(bool value)
        {
            sphereCollider.enabled = value;
            sphereCollider.isTrigger = !value;
            physicsRigidbody.isKinematic = !value;
            isFalling = value;
        }
    }
}