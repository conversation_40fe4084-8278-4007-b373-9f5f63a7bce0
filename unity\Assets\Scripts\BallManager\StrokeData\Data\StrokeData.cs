﻿using App.Constants;
using BallManager.StrokeData.DataContracts;
using UnityEngine;

namespace BallManager.StrokeData.Data
{
    public class StrokeData : IStrokeData
    {
        public GolferData GolferData { get; }
        public int RoundNumber { get; }
        public int StrokeNumber { get; }
        public int StrokeId { get; }
        public HoleDetailsData HoleDetails { get; }
        public CoordinateData From { get; }
        public CoordinateData To { get; }
        public string FromLocationShort { get; }
        public string ToLocationShort { get; }
        public RadarData Radar { get; }
        public bool IsValid { get; }
        public Vector3 BallStartingPosition { get; }

        public StrokeData (StrokeDataContract strokeDataContract)
        {
            StrokeId = int.Parse(strokeDataContract.StrokeId);
            RoundNumber = strokeDataContract.RoundNumber;
            StrokeNumber = strokeDataContract.StrokeNumber;
            
            FromLocationShort = strokeDataContract.From.LocationShort;
            ToLocationShort = strokeDataContract.To.LocationShort;
            
            GolferData = new GolferData(strokeDataContract.GolferDetails);
            
            HoleDetails = new HoleDetailsData(strokeDataContract.HoleDetails);
            
            From = new CoordinateData(strokeDataContract.From);
            To = new CoordinateData(strokeDataContract.To);
            Radar = new RadarData(strokeDataContract, HoleDetails);
            
            IsValid = CheckIfValidStroke(From.IsValid, To.IsValid);
            BallStartingPosition = SetStartingPosition(Radar, From);
        }

        private bool CheckIfValidStroke(bool isFromValid, bool isToValid)
        {
            var isCoordinatesValid = isFromValid && isToValid;
            var isFromZero = From.Coordinate is {x: 0, y: 0, z: 0};
            var isLocationPenalty = FromLocationShort == AppConstants.BallLocationWater || FromLocationShort == AppConstants.BallLocationOther;
            
            return isCoordinatesValid && !isFromZero && !isLocationPenalty;
        }
        
        private static Vector3 SetStartingPosition(RadarData radar, CoordinateData from)
        {
            return radar.IsValid ? radar.Points[0] : from.Coordinate;
        }
    }
}