import * as path from "path";
import * as cloudfront from "aws-cdk-lib/aws-cloudfront";
import * as logs from "aws-cdk-lib/aws-logs";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as s3deploy from "aws-cdk-lib/aws-s3-deployment";
import * as cdk from "aws-cdk-lib/core";
import { Construct } from "constructs";

type WebappDeploymentStackProps = cdk.StackProps &
  Readonly<{
    stage: string;
    webAppStackName: string;
  }>;

export class WebappDeploymentStack extends cdk.Stack {
  constructor(scope: Construct, id: string, props: WebappDeploymentStackProps) {
    super(scope, id, props);

    const { stage, webAppStackName } = props;

    const bucketName = cdk.Fn.importValue(
      `${stage}-${webAppStackName}-WebappBucketName`,
    );
    const distributionId = cdk.Fn.importValue(
      `${stage}-${webAppStackName}-WebappDistributionId`,
    );
    const domainName = cdk.Fn.importValue(
      `${stage}-${webAppStackName}-WebappDomainName`,
    );

    const bucket = s3.Bucket.fromBucketName(
      this,
      "WebappBucketName",
      bucketName,
    );

    const distribution = cloudfront.Distribution.fromDistributionAttributes(
      this,
      "WebappDistribution",
      {
        distributionId,
        domainName,
      },
    );

    new s3deploy.BucketDeployment(this, "GolfWebappDeployment", {
      sources: [
        s3deploy.Source.asset(path.join(__dirname, "../../UnityBuildOutput")),
      ],
      destinationBucket: bucket,
      distribution,
      distributionPaths: ["/*"],
      logRetention: logs.RetentionDays.ONE_DAY,
      memoryLimit: 1024,
      ephemeralStorageSize: cdk.Size.gibibytes(1),
    });
  }
}
