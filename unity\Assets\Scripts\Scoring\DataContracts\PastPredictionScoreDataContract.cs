using Newtonsoft.Json;

namespace Scoring.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class PastPredictionScoreDataContract
    {
        [JsonProperty("createdTimestamp")]
        public string CreatedTimestamp;
        [<PERSON>sonProperty("golferId")]
        public int GolferId;
        [JsonProperty("holeNumber")]
        public int HoleNumber;
        [JsonProperty("score")]
        public int? Score;
        [JsonProperty("golferName")]
        public string GolferName;
    }
}