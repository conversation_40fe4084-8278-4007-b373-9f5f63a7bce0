using System;
using App.CameraSystem;
using App.CameraSystem.StateMachine.Data;
using BallManager;
using BallManager.StrokeData.Data;
using Data;
using Network.Services;
using Prediction;
using Scoring.Data;
using Testing.PredictionWindow;
using TMPro;
using UnityEngine;

namespace Scoring
{
    public class ScoreManager : MonoBehaviour
    {
        private const string ScoreSuffix = "<size=50%> PTS</size>";

        [Header("UI Elements")]
        [SerializeField] private Canvas canvas;
        [SerializeField] private GameObject visuals;
        [SerializeField] private TMP_Text scoreText;
        [SerializeField] private RectTransform playerLocationIcon;
        [SerializeField] private RectTransform userLocationIcon;

        [Header("Dependencies")]
        [SerializeField] private PredictionManager predictionManager;
        [SerializeField] private CameraDirector cameraDirector;
        [SerializeField] private GolfBallManager golfBallManager;

        [Header("Testing")]
        [SerializeField] private PredictionScoreTester predictionScoreTester;

        public Action OnContinueButtonPressed;

        private PredictionScoreData currentScoreData;
        private PredictionResultPayload predictionResultPayload;

        private void Start()
        {
            visuals.SetActive(false);
        }

        public async void GetScore(string tournamentId, StrokeData currentStrokeData, string username)
        {
            if (predictionScoreTester != null && predictionScoreTester.UseMockData)
            {
                currentScoreData = PredictionScoreService.GetMockScore(predictionScoreTester.MockDataInput, predictionScoreTester.MockScore);
                Debug.Log("Using mock score data");
                return;
            }

            var queryParams = new PredictionScoreDataInput(
                tournamentId,
                currentStrokeData.RoundNumber,
                currentStrokeData.HoleDetails.Number,
                currentStrokeData.StrokeNumber,
                currentStrokeData.GolferData.PlayerId,
                SessionData.UserId
            );
            var scoreRequest = await PredictionScoreService.GetPredictionScore(queryParams);
            if (scoreRequest.IsSuccess)
            {
                currentScoreData = scoreRequest.Value;
            }
            else
            {
                Debug.LogError($"Failed to get score: {scoreRequest.Error.Message}");
            }
        }

        public void ShowScore()
        {
            visuals.SetActive(true);
            if (currentScoreData == null || currentScoreData.Score == null)
            {
                Debug.LogWarning("No score data available to show.");
                scoreText.text = "No score";
                return;
            }
            scoreText.text = currentScoreData.Score.ToString() + ScoreSuffix;

            predictionResultPayload = new PredictionResultPayload();
            predictionResultPayload.TargetPointA = golfBallManager.Ball.Transform.position;

            var prediction = predictionManager.CurrentPrediction;
            prediction.y = predictionResultPayload.TargetPointA.y; // Ensure the y-coordinate matches the ball's y-coordinate
            predictionResultPayload.TargetPointB = prediction;

            cameraDirector.ShowPredictionResultCamera(predictionResultPayload);

            UpdateLocationIcons(predictionResultPayload.TargetPointA, predictionResultPayload.TargetPointB);
        }

        private void Update()
        {
            if (predictionResultPayload != null)
            {
                UpdateLocationIcons(predictionResultPayload.TargetPointA, predictionResultPayload.TargetPointB);
            }
        }

        private void UpdateLocationIcons(Vector3 playerLocation, Vector3 userLocation)
        {
            userLocationIcon.anchoredPosition = GetScreenPosition(userLocation);
            playerLocationIcon.anchoredPosition = GetScreenPosition(playerLocation);
        }

        private Vector2 GetScreenPosition(Vector3 worldPosition)
        {
            // Get screen position (pixels, bottom-left origin)
            Vector2 screenPoint = cameraDirector.GetWorldToScreenPosition(worldPosition);
            Vector2 localPoint;
            // Convert to local position in the canvas
            RectTransformUtility.ScreenPointToLocalPointInRectangle(
                userLocationIcon.parent as RectTransform,
                screenPoint,
                canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : canvas.worldCamera,
                out localPoint
            );
            return localPoint;
        }

        public void HideScore()
        {
            predictionResultPayload = null;
            visuals.SetActive(false);
            scoreText.text = string.Empty;
        }

        public void ContinueButtonPressed()
        {
            OnContinueButtonPressed?.Invoke();
        }
    }
}