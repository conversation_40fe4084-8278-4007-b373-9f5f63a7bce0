#!/usr/bin/env node

const fs = require("fs");
const path = require("path");
const projectRoot = path.join(__dirname, "../../");
const unityConfigPath = path.join(projectRoot, "UnityBuildOutput", "StreamingAssets", "config.json");
const webAppOutputsPath = path.join(projectRoot, "dist", "app-web-outputs.json");
const unityConfig = require(unityConfigPath);
const webAppOutputs = require(webAppOutputsPath);
const stackName = `${process.env.STAGE}-golf-app-web`;

unityConfig.aws_urls = {
  ...unityConfig.aws_urls,
    "graphql_url": webAppOutputs[stackName].GolfApiUrl,
    "ws_url": webAppOutputs[stackName].GolfApiWsUrl,
    "leaderboard_url": webAppOutputs[stackName].LeaderboardApiUrl,
};

unityConfig.app_ids.client_id = webAppOutputs[stackName].UserPoolClientId;

fs.writeFileSync(unityConfigPath, JSON.stringify(unityConfig, null, 2));
