import { PostConfirmationTriggerEvent } from "aws-lambda";
import { baseHandler } from "./baseHandler";
import { addUserToLeaderboard } from "./utils/addUserToLeaderboard";
import { getTournamentId } from "./utils/getTournamentId";
import { incrementHouseholdSize } from "./utils/incrementHouseholdSize";
import { publishSignUpSuccess } from "./utils/publishSignUpSuccess";

jest.mock("./utils/incrementHouseholdSize");
jest.mock("./utils/publishSignUpSuccess");
jest.mock("./utils/addUserToLeaderboard");
jest.mock("./utils/getTournamentId");
jest.mock("@sky-uk/coins-utilities", () => ({
  getEnvironmentVariable: jest.fn((key) => {
    const env: Record<string, string> = {
      LEADERBOARD_API_URL: "https://api.example.com/graphql",
      LEADERBOARD_API_ID: "api-123",
      TOURNAMENT_ID: "********",
      GOLF_API_URL: "https://api.example.com/graphql",
      GOLF_API_ID: "api-123",
      HOUSEHOLD_TABLE_NAME: "TestTable",
      MAX_HOUSEHOLD_SIZE: "5",
      APP_VARIANT: "onsite",
    };
    return env[key] || "";
  }),
}));
jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
jest.mock("@sky-uk/coins-utilities");

describe("onsite - baseHandler", () => {
  const event = {
    triggerSource: "PostConfirmation_ConfirmSignUp",
    userName: "testuser",
    request: {
      userAttributes: {
        "custom:deviceId": "device-1",
        email: "<EMAIL>",
        sub: "user-1",
      },
    },
  } as unknown as PostConfirmationTriggerEvent;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should not add user to leaderboard when trigger is PostConfirmation_ConfirmForgotPassword", async () => {
    (getTournamentId as jest.Mock).mockResolvedValue("********");
    (addUserToLeaderboard as jest.Mock).mockResolvedValue(undefined);
    const forgotPasswordEvent = {
      ...event,
      triggerSource: "PostConfirmation_ConfirmForgotPassword",
    } as PostConfirmationTriggerEvent;
    const response = await baseHandler(forgotPasswordEvent);
    expect(addUserToLeaderboard).not.toHaveBeenCalled();
    expect(response).toEqual(forgotPasswordEvent);
  });

  it("should add user to leaderboard when event trigger is PostConfirmation_ConfirmSignUp", async () => {
    (getTournamentId as jest.Mock).mockResolvedValue("********");
    (addUserToLeaderboard as jest.Mock).mockResolvedValue(undefined);
    const result = await baseHandler(event);
    expect(addUserToLeaderboard).toHaveBeenCalled();
    expect(result).toBe(event);
  });

  it("should throw if userName is missing", async () => {
    const missingUserName = { ...event, userName: "" };
    await expect(baseHandler(missingUserName)).rejects.toThrow(
      ": Missing username",
    );
  });

  it("should not throw if deviceId is missing", async () => {
    const missingDeviceId = {
      ...event,
      request: {
        userAttributes: {
          ...event.request.userAttributes,
          "custom:deviceId": "",
        },
      },
    };
    const result = await baseHandler(missingDeviceId);
    expect(result).toBe(missingDeviceId);
  });

  it("should throw if email is missing", async () => {
    const missingEmail = {
      ...event,
      request: {
        userAttributes: { ...event.request.userAttributes, email: "" },
      },
    };
    await expect(baseHandler(missingEmail)).rejects.toThrow(": Missing email");
  });

  it("should throw if userId is missing", async () => {
    const missingUserId = {
      ...event,
      request: {
        userAttributes: { ...event.request.userAttributes, sub: "" },
      },
    };
    await expect(baseHandler(missingUserId)).rejects.toThrow(
      ": Missing userId",
    );
  });

  it("should not incrementHouseholdSize and publishSignUpSuccess", async () => {
    (incrementHouseholdSize as jest.Mock).mockResolvedValue(undefined);
    (publishSignUpSuccess as jest.Mock).mockResolvedValue(undefined);
    const result = await baseHandler(event);
    expect(incrementHouseholdSize).not.toHaveBeenCalled();
    expect(publishSignUpSuccess).not.toHaveBeenCalled();
    expect(result).toBe(event);
  });
});
