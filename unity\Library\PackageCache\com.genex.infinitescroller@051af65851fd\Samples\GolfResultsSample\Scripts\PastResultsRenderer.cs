using System;
using UnityEngine;

namespace Sky.GenEx.InfiniteScroller.Samples.GolfResultsSample
{
    public class PastResultsRenderer : MonoBehaviour
    {
        
        [SerializeField]
        private Scroller scroller;
        
        private PastPredictionScoresData scoresData;

        public event Action OnTotalRefreshRequested;
        public event Action<string> OnMoreItemsRequested;

        private void Awake()
        {
            scroller.OnFill -= OnFillResult;
            scroller.OnFill += OnFillResult;
            
            scroller.OnHeight -= OnRowHeight;
            scroller.OnHeight += OnRowHeight;
            
            scroller.OnPull -= OnPullItem;
            scroller.OnPull += OnPullItem;
            
            scroller.OnLoadMore -= OnLoadMore;
            scroller.OnLoadMore += OnLoadMore;
        }

        private void OnDestroy()
        {
            scroller.OnFill -= OnFillResult;
            scroller.OnHeight -= OnRowHeight;
            scroller.OnPull -= OnPullItem;
            scroller.OnLoadMore -= OnLoadMore;
        }

        public void RefreshResults(PastPredictionScoresData newScoresData)
        {
            scoresData = newScoresData;
            scroller.InitData(scoresData.Items.Count, !string.IsNullOrEmpty(scoresData.NextToken));
        }

        public void AddResults(PastPredictionScoresData addedScoresData)
        {
            scoresData.Items.AddRange(addedScoresData.Items);
            scoresData.NextToken = addedScoresData.NextToken;
            
            scroller.ApplyDataTo(scoresData.Items.Count, addedScoresData.Items.Count, ScrollerDirection.Bottom, !string.IsNullOrEmpty(scoresData.NextToken));
        }
        
        private void OnFillResult(int index, GameObject row)
        {
            var scoreData = scoresData.Items[index];
            PastResultRow pastResultRow = row.GetComponent<PastResultRow>();
            pastResultRow.Refresh(scoreData);
        }

        private int OnRowHeight(int index)
        {
            var itemRectTransform = scroller.Prefab.GetComponent<RectTransform>();
            return Mathf.CeilToInt(itemRectTransform.rect.size.y);
        }
        
        private void OnPullItem(ScrollerDirection direction) {
            if (direction == ScrollerDirection.Top) {
                OnTotalRefreshRequested?.Invoke();
            }
        }

        private void OnLoadMore()
        {
            OnMoreItemsRequested?.Invoke(scoresData.NextToken);
        }
    }
}