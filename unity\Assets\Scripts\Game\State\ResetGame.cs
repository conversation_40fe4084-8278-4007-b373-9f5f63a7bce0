using App.CameraSystem;
using UnityEngine;

namespace Game.State
{
    public class ResetGame : BaseGameState
    {
        [SerializeField] private CameraDirector cameraDirector;
        
        public override async void Enter()
        {
            cameraDirector.ResetCamera();
            predictionManager.ResetPredictionSystem();
            scoreManager.HideScore();
            strokeManager.ResetStrokeSystem();
            tabMenuManager.SetHeaderVisible(true);
            
            //wait for the next frame otherwise state machine breaks from changing states too fast
            await Awaitable.NextFrameAsync();
            LeaveState();
        }

        public override void Exit()
        {
        }
        
        private void LeaveState()
        {
            if (predictionWindowManager.IsPredictionWindowOpen)
            {
                onFinished?.Invoke(GameStateType.PredictionWindowOpen);
                return;
            }

            onFinished?.Invoke(GameStateType.WaitingForPredictionWindow);
        }
    }
}
