import { publishSignUpSuccess } from "./publishSignUpSuccess";

jest.mock("@/utils/graphql", () => ({
  sendGraphQLRequest: jest.fn(),
}));

import { sendGraphQLRequest } from "@/utils/graphql";

describe("publishSignUpSuccess", () => {
  const props = {
    userId: "user-1",
    deviceId: "device-1",
    apiUrl: "https://api.example.com/graphql",
    apiId: "api-123",
  };

  it("should call sendGraphQLRequest with correct mutation and variables", async () => {
    await publishSignUpSuccess(props);
    expect(sendGraphQLRequest).toHaveBeenCalledWith(
      expect.objectContaining({
        apiUrl: props.apiUrl,
        apiId: props.apiId,
        body: expect.objectContaining({
          operationName: "PublishSignUpSuccess",
          variables: {
            input: {
              deviceId: props.deviceId,
              playerId: props.userId,
            },
          },
        }),
      }),
    );
  });
});
