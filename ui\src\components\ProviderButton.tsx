import { Button } from "./Button";
import { signInWithRedirect, signOut } from "aws-amplify/auth";

type ProviderButtonProps = {
  provider: "Google" | "Apple";
  disabled?: boolean;
};

export const ProviderButton = ({ provider, ...rest }: ProviderButtonProps) => {
  const openProviderLogin = async () => {
    console.log(`Opening login for ${provider}`);
    try {
      await signInWithRedirect({
        provider,
      });
    } catch (err) {
      const { name: errorType } = err as any;
      console.warn(errorType, err);
      // handle already authenticated error gracefully
      if (errorType === "UserAlreadyAuthenticatedException") {
        await signOut();
        void openProviderLogin();
      }
    }
  };

  return (
    <Button
      label={`Continue with ${provider}`}
      icon={`/images/${provider.toLowerCase()}-icon.png`}
      onClick={openProviderLogin}
      className="!bg-white/15 text-white"
      {...rest}
    />
  );
};
