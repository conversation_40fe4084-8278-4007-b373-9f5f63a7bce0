using UnityEditor;

namespace UI
{
    /// <summary>
    /// UITouchable Inspector to hide unused fields
    /// </summary>
    [CustomEditor(typeof(UITouchable))]
    [CanEditMultipleObjects]
    public class UITouchableEditor : UnityEditor.Editor
    {
        private SerializedProperty raycastPadding;

        private void OnEnable()
        {
            raycastPadding = serializedObject.FindProperty("m_RaycastPadding");
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            EditorGUILayout.PropertyField(raycastPadding);
            serializedObject.ApplyModifiedProperties();
        }
    }
}