## What
 
This section is for describing what the pull request (PR) does. It typically includes a brief summary of the changes and the associated JIRA ticket number.
 
## Why
 
Here, you explain the reason for the changes. This section should justify why the PR is necessary and what problem it solves or improvement it brings.
 
## How
 
This part details the implementation approach. It should give a high-level overview of how the changes were made, including any important technical decisions or trade-offs.
 
## Test
 
[Notes]
\
\
[Results]
\
\
[Screenshots (if appropriate)]
 
## Checklist
- [ ] I have self-reviewed this PR
- [ ] I have added tests that prove the feature works or the fix is effective
- [ ] This PR meets the acceptance criteria in the JIRA ticket