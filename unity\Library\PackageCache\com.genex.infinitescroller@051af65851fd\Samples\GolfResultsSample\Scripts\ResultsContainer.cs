    using System;
using System.Collections.Generic;
using UnityEngine;
using Random = UnityEngine.Random;

namespace Sky.GenEx.InfiniteScroller.Samples.GolfResultsSample
{
    public class ResultsContainer : MonoBehaviour
    {
        private const int InitialLimit = 20;
        private const int LoadMoreAmount = 10;
        
        public enum ResultStatus
        {
            Loading,
            NoResults,
            ShowResults,
            Refreshing
        }
        
        [SerializeField]
        private PastResultsRenderer pastResultsRenderer;
        
        [SerializeField]
        private RectTransform noResultsTransform;
        
        [SerializeField]
        private RectTransform loadingTransform;
        
        [SerializeField]
        private RectTransform refreshingTransform;
        
        private bool initialized = false;
        private bool refreshing = false;
        private bool addingMoreResults = false;

        private void Start()
        {
            pastResultsRenderer.OnMoreItemsRequested += OnMoreResultsRequested;
            pastResultsRenderer.OnTotalRefreshRequested += OnTotalRefreshRequested;
            
            if (!initialized)
            {
                SetResultStatus(ResultStatus.Loading);
                RefreshPastPredictionScores();
            }
        }
        
        private void OnTotalRefreshRequested()
        {
            if (!refreshing)
            {
                SetResultStatus(ResultStatus.Refreshing);
                RefreshPastPredictionScores();
            }
        }

        private void OnMoreResultsRequested(string nextToken)
        {
            if (!addingMoreResults)
                AddPastPredictionScores(nextToken);
        }

        private void SetResultStatus(ResultStatus status)
        {
            loadingTransform?.gameObject.SetActive(status == ResultStatus.Loading);
            noResultsTransform?.gameObject.SetActive(status == ResultStatus.NoResults);
            pastResultsRenderer?.gameObject.SetActive(status == ResultStatus.ShowResults || status == ResultStatus.Refreshing);
            refreshingTransform?.gameObject.SetActive(status == ResultStatus.Refreshing);
        }

        private async void RefreshPastPredictionScores()
        {
            try
            {
                refreshing = true;
                await Awaitable.WaitForSecondsAsync(1);
                RefreshPastPredictionScoresWithMockData();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                SetResultStatus(ResultStatus.NoResults);
            }
            finally
            {
                refreshing = false;
            }
        }

        private async void AddPastPredictionScores(string nextToken)
        {
            try
            {
                addingMoreResults = true;
                await Awaitable.WaitForSecondsAsync(1);
                AddPredictionScoresWithMockData(nextToken);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
            finally
            {
                addingMoreResults = false;
            }
        }

        private void AddPredictionScoresWithMockData(string nextToken)
        {
            PastPredictionScoresData scoresData = CreateMockData(LoadMoreAmount, "test");
            pastResultsRenderer.AddResults(scoresData);
        }

        private void RefreshPastPredictionScoresWithMockData()
        {
            PastPredictionScoresData scoresData = CreateMockData(InitialLimit, "test");
            
            SetResultStatus(ResultStatus.ShowResults);
            pastResultsRenderer.RefreshResults(scoresData);
            initialized = true;
        }

        private PastPredictionScoresData CreateMockData(int mockCount, string nextToken)
        {
            string[] names = new[] { "Stewart Cink", "Austin Smotherman", "Danny Willet", "Sepp Straka", "Robby Shelton" };
            int[] ids = new[] { 20229, 19803, 22371, 29936, 23108, 28775, 32448, 33141, 27129, 24502 };
            List<PastPredictionScoreData> scores = new List<PastPredictionScoreData>(mockCount);

            for (int i = 0; i < mockCount; i++)
            {
                var randomDate = DateTime.FromOADate(Random.Range(1, 10000));
                var randomGolfId = ids[Random.Range(0, ids.Length)];
                var randomHoleNumber = Random.Range(1, 12);
                var randomScore = Random.Range(1, 1000);
                var randomName = names[Random.Range(0, names.Length)];
                scores.Add(new (randomDate, randomGolfId, randomHoleNumber, randomScore, randomName));
            }
            
            return new PastPredictionScoresData(scores, nextToken);
        }
    }
}