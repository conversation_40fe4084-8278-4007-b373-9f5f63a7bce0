{"dependencies": [{"name": "@testing-library/jest-dom", "type": "build"}, {"name": "@testing-library/react", "type": "build"}, {"name": "@testing-library/user-event", "type": "build"}, {"name": "@types/jest", "type": "build"}, {"name": "@types/js-cookie", "type": "build"}, {"name": "@types/node", "version": "ts4.0", "type": "build"}, {"name": "@types/react", "type": "build"}, {"name": "@types/react-dom", "type": "build"}, {"name": "@typescript-eslint/eslint-plugin", "version": "^8", "type": "build"}, {"name": "@typescript-eslint/parser", "version": "^8", "type": "build"}, {"name": "autoprefixer", "type": "build"}, {"name": "eslint-config-prettier", "version": "^8", "type": "build"}, {"name": "eslint-import-resolver-typescript", "type": "build"}, {"name": "eslint-plugin-import", "type": "build"}, {"name": "eslint-plugin-prettier", "type": "build"}, {"name": "eslint", "version": "^8", "type": "build"}, {"name": "postcss", "type": "build"}, {"name": "prettier", "type": "build"}, {"name": "react-scripts", "version": "^5", "type": "build"}, {"name": "tailwindcss", "type": "build"}, {"name": "typescript", "version": "^4.0.3", "type": "build"}, {"name": "@types/express-serve-static-core", "version": "4.17.30", "type": "override"}, {"name": "@types/express", "version": "4.17.13", "type": "override"}, {"name": "@react-spring/web", "type": "runtime"}, {"name": "aws-amplify", "type": "runtime"}, {"name": "aws-rum-web", "type": "runtime"}, {"name": "js-cookie", "type": "runtime"}, {"name": "no-profanity", "type": "runtime"}, {"name": "react", "type": "runtime"}, {"name": "react-dom", "type": "runtime"}, {"name": "react-error-boundary", "type": "runtime"}, {"name": "react-router", "type": "runtime"}, {"name": "react-unity-webgl", "type": "runtime"}, {"name": "web-vitals", "type": "runtime"}], "//": "~~ Generated by projen. To modify, edit .projenrc.js and run \"bunx projen\"."}