using System;
using Network.DataContract;
using Network.Queries;
using Network.Response;
using UnityEngine;

namespace Network.Services
{
    public static class TournamentService
    {
        public static async Awaitable<Result<string>> GetCurrentTournamentId()
        {
            var query = new GetCurrentTournamentQuery();
            try
            {
                var response =
                    await AwsClient.Golf.QueryAsync<TournamentDataContract>(query.QueryString, query.Name);

                return response.IsSuccess
                    ? Result<string>.Success(response.Data.TournamentId)
                    : Result<string>.Failure(new ServiceError(-1, response.Errors[0]?.Message ?? "Unknown error"));
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return Result<string>.Failure(new ServiceError(-1, ex.Message));
            }
        }
    }
}
