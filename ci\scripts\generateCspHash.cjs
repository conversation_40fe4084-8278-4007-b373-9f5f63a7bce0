#!/usr/bin/env bun
import { readFileSync, writeFileSync } from "fs";
import { createHash } from "crypto";

// Path to Unity's generated index.html
const html = readFileSync("./UnityBuildOutput/index.html", "utf8");

// Extract inline script between first <script> and </script> tags
const scriptMatch = html.match(/<script>([\s\S]*?)<\/script>/);

if (!scriptMatch || !scriptMatch[1]) {
  console.error("❌ Inline script not found in index.html");
  process.exit(1);
}

const inlineScript = scriptMatch[1];

// Compute SHA256 hash
const hash = createHash("sha256")
  .update(inlineScript, "utf8")
  .digest("base64");

// Write CSP-compatible hash to file
const cspHash = `sha256-${hash}`;
writeFileSync("./UnityBuildOutput/inline-script-csp-hash.txt", cspHash);

console.log(`✅ CSP Hash generated: ${cspHash}`);
