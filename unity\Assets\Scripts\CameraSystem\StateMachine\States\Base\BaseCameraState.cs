using App.CameraSystem.StateMachine.Types;
using Cinemachine;
using UnityEngine;

namespace App.CameraSystem.StateMachine.States.Base
{
	[RequireComponent(typeof(CinemachineVirtualCamera))]
	public abstract class BaseCameraState : MonoBehaviour
	{
		[SerializeField] private CameraStateType baseCameraState;
		
		private IStateChanger stateChanger;
		
		protected CinemachineVirtualCamera StateCamera { get; private set; }
		
		public CameraStateType State => baseCameraState;
		
		public void DoInitialize(IStateChanger stateChanger)
		{
			this.stateChanger = stateChanger;
			StateCamera = GetComponent<CinemachineVirtualCamera>();
			Initialize();
		}

		protected virtual void Initialize()
		{
		}
		
		public abstract void Enter();
		public abstract void Exit();

		protected void ChangeState(CameraStateType nextState)
		{
			Debug.Log("Camera changing without payload to: " + nextState);
			stateChanger.ChangeState(nextState);
		}
		
		protected void ChangeState<T>(CameraStateType nextState,T payload)
		{
			Debug.Log("Camera changing with payload to: " + nextState);
			stateChanger.ChangeState(nextState,payload);
		}
	}
}
