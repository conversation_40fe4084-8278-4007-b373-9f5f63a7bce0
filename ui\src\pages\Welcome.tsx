import { useNavigate } from "react-router";
import { <PERSON><PERSON>, <PERSON><PERSON>, GameTheGreenIcon } from "../components";
import { RecordPageView } from "../services/rum";

export function Welcome() {
  const navigate = useNavigate();

  RecordPageView("Welcome");
  return (
    <div className="bg-[url('/public/images/sawgrass-hero.png')] bg-cover bg-bottom h-full">
      <div
        className="flex flex-col h-full"
        style={{
          background:
            "linear-gradient(0deg, rgba(15, 23, 59, 0.60) 71.83%, #0F173B 109.21%), linear-gradient(180deg, rgba(15, 23, 59, 0.00) 52.76%, rgba(15, 23, 59, 0.60) 66.24%, var(--CB-Blue-800, #0F173B) 85.24%), linear-gradient(0deg, var(--cb-blue-50900, rgba(12, 17, 33, 0.50)) 0%, var(--cb-blue-50900, rgba(12, 17, 33, 0.50)) 100%)",
        }}
      >
        <Header divider={false} />
        <div className="flex flex-col flex-grow justify-evenly">
          <GameTheGreenIcon className="w-80 self-center h-min" />
          <div className="flex flex-col gap-4 px-4 font-medium">
            <Button
              label="Let's get started"
              onClick={() => navigate("/ftue")}
            />
            <div className="text-white text-center">
              Already have an account?{" "}
              <a className="text-gold underline cursor-pointer" href="/login">
                Sign in
              </a>
            </div>
            <div className="py-3 px-22 text-white text-center text-sm font-normal flex flex-col items-center gap-1">
              <a className="font-bold" href="/rules">
                Rules of the Game
              </a>
              <a
                className="font-bold"
                href="https://www.xfinity.com/privacy/policy"
              >
                Privacy Policy
              </a>
              <a
                className="font-bold"
                href="https://business.comcast.com/terms/web"
              >
                Terms & conditions
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
