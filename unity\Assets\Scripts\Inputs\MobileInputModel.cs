using System;
using UnityEngine;

namespace Inputs
{
    public class MobileInputModel : MonoBehaviour
    {
        public event Action<Vector2> OnTapPress;
        public event Action<Vector2> OnTapMove;
        public event Action<Vector2> OnTapRelease;
        public event Action<Vector2, Vector2> OnPinchPress;
        public event Action<Vector2, Vector2> OnPinchMove;
        public event Action<Vector2, Vector2> OnPinchRelease;

        protected void Update()
        {
            // 2 touches = pinch input
            if (Input.touchCount == 2)
            {
                var touch0 = Input.GetTouch(0);
                var touch1 = Input.GetTouch(1);
                
                if (touch0.phase == TouchPhase.Began || touch1.phase == TouchPhase.Began)
                {
                    HandlePinchPress(touch0.position, touch1.position);
                }
                else if (touch0.phase == TouchPhase.Moved || touch1.phase == TouchPhase.Moved)
                {
                    HandlePinchMove(touch0.position, touch1.position);
                }
                else if (touch0.phase == TouchPhase.Ended)
                {
                    HandlePinchRelease(touch1.position, touch0.position);
                }
                else if (touch1.phase == TouchPhase.Ended)
                {
                    HandlePinchRelease(touch0.position, touch1.position);
                }
                return;
            }

            // 1 touch = touch input
            if (Input.touchCount == 1)
            {
                var touch0 = Input.GetTouch(0);
                
                if (touch0.phase == TouchPhase.Began)
                {
                    HandleTouchPress(touch0.position);
                }
                else if (touch0.phase == TouchPhase.Moved)
                {
                    HandleTouchMove(touch0.position);
                }
                else if (touch0.phase == TouchPhase.Ended)
                {
                    HandleTouchRelease(touch0.position);
                }
                return;
            }

            if (Input.GetMouseButtonDown(0)) HandleTouchPress(Input.mousePosition);
            else if (Input.GetMouseButtonUp(0)) HandleTouchRelease(Input.mousePosition);
            else if (Input.GetMouseButton(0)) HandleTouchMove(Input.mousePosition);
        }

        private void HandlePinchPress(Vector2 position1, Vector2 position2)
        {
            OnPinchPress?.Invoke(position1, position2);
        }

        private void HandlePinchMove(Vector2 position1, Vector2 position2)
        {
            OnPinchMove?.Invoke(position1, position2);
        }

        private void HandlePinchRelease(Vector2 heldPosition, Vector2 releasedPosition)
        {
            OnPinchRelease?.Invoke(heldPosition, releasedPosition);
        }

        private void HandleTouchPress(Vector2 position)
        {
            OnTapPress?.Invoke(position);
        }

        private void HandleTouchMove(Vector2 position)
        {
            OnTapMove?.Invoke(position);
        }

        private void HandleTouchRelease(Vector2 position)
        {
            OnTapRelease?.Invoke(position);
        }
    }
}