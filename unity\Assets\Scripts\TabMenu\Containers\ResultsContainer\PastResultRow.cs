using System;
using Headshots;
using Scoring.Data;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace TabMenu.Containers
{
    public class PastResultRow : MonoBehaviour
    {
        [SerializeField]
        private Image golferHeadshot;
        
        [SerializeField]
        private TextMeshProUGUI golferNameText;

        [SerializeField]
        private string holeNumberTextTemplate = "Hole {0}";

        [SerializeField]
        private TextMeshProUGUI holeNumberText;

        [SerializeField]
        public string holeTimeFormat = "ddd, HH:mm";

        [SerializeField]
        private TextMeshProUGUI holeTimeText;
        
        [SerializeField]
        private string scoreTextTemplate = "+{0}pts";
        
        [SerializeField]
        private string noScoreText = "Score Pending...";

        [SerializeField]
        private TextMeshProUGUI scoreText;

        public void Refresh(PastPredictionScoreData data)
        {
            RefreshGolferHeadshot(data.GolferId);
            RefreshGolferName(data.GolferName);
            RefreshHoleNumber(data.HoleNumber);
            RefreshHoleTime(data.CreatedTimestamp);
            RefreshScore(data.Score);
        }

        private async void RefreshGolferHeadshot(int golferId)
        {
            if (golferHeadshot == null)
                return;

            golferHeadshot.gameObject.SetActive(false);
            var headshot = await HeadshotService.GetHeadshot(golferId);

            if (headshot?.ProfilePhoto != null)
            {
                golferHeadshot.gameObject.SetActive(true);
                golferHeadshot.sprite = headshot.ProfilePhoto;
            }
        }

        private void RefreshGolferName(string golferName)
        {
            if (golferNameText == null)
                return;

            golferNameText.text = golferName;
        }

        private void RefreshHoleNumber(int holeNumber)
        {
            if (holeNumberText == null)
                return;

            holeNumberText.text = string.Format(holeNumberTextTemplate, holeNumber);
        }

        private void RefreshHoleTime(DateTime holeTime)
        {
            if (holeTimeText == null)
                return;

            holeTimeText.text = holeTime.ToString(holeTimeFormat);
        }

        private void RefreshScore(int? score)
        {
            if (scoreText == null)
                return;
            
            
            if (score.HasValue)
                scoreText.text = string.Format(scoreTextTemplate, score);
            else 
                scoreText.text = noScoreText;
        }
    }
}