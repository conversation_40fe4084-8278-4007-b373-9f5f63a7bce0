using System;
using Network.Response;
using UnityEngine;
using UnityEngine.Networking;

namespace Network.Services
{
    public static class ImageFetchService
    {
        public static async Awaitable<Sprite> GetImage(string url)
        {
            try
            {
                var result = await FetchImage(url);
                if (!result.IsSuccess)
                {
                    throw new Exception(result.Error?.Message ?? "Error fetching image.");
                }
                var spriteBytes = result.Value;
                return ConvertBytesToSprite(spriteBytes);
            }
            catch (Exception ex)
            {
                Debug.LogError($"Error fetching image: {ex.Message}");
                return null;
            }
        }

        private static async Awaitable<Result<byte[]>> FetchImage(string url)
        {
            try
            {
                var request = new UnityWebRequest(url);
                request.SetRequestHeader("x-requested-with", "application/json");
                request.timeout = 30;
                request.downloadHandler = new DownloadHandlerBuffer();
                await request.SendWebRequest();
                var response = request.downloadHandler.data;
                return Result<byte[]>.Success(response);
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return Result<byte[]>.Failure(new ServiceError(-1, ex.Message));
            }
        }

        private static Sprite ConvertBytesToSprite(byte[] data)
        {
            var texture = new Texture2D(1, 1);
            texture.LoadImage(data);
            return Sprite.Create(texture, new Rect(0, 0, texture.width, texture.height), new Vector2(0.5f, 0.5f));
        }
    }
}