import { SVGProps } from "react";

export const FlagIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg width="100%" height="100%" viewBox="0 0 20 24" fill="none" {...props}>
      <path
        d="M1.6857 10.817C2.68625 11.3946 3.64685 12.1389 4.42799 13.1169C4.53515 13.2507 4.59778 13.4164 4.59757 13.5889C4.58806 15.472 4.12528 17.1118 3.1236 18.4299C3.06241 18.511 2.97861 18.5882 2.94458 18.6901C4.50311 18.4599 5.64379 17.7596 6.53066 16.9847C6.9875 16.5853 7.41659 16.2 7.93356 15.8642C8.44588 15.5295 9.00042 15.2101 9.65491 14.9419C11.1043 14.3479 12.5127 14.1761 13.7907 14.2234C14.1993 14.2383 14.5986 14.2739 14.9868 14.3241C15.1897 14.351 15.3577 14.4951 15.4107 14.6931L15.9634 16.7557C15.9634 16.7557 16.1425 17.4241 16.3659 18.258C16.6541 19.3334 16.5528 19.0842 16.6967 19.6211C16.7344 19.7619 16.8646 19.858 17.0095 19.854C17.7373 19.8348 18.113 19.7391 18.7086 19.4369C18.8431 19.3694 18.9097 19.2155 18.8707 19.0701L18.6157 18.1185C18.6091 18.0938 18.6137 18.0677 18.6246 18.0465C18.6205 18.0559 18.6164 18.0653 18.6142 18.0758L18.6853 18.212C18.6762 18.2029 18.6693 18.1898 18.666 18.1774L18.5715 17.8246C18.5715 17.8246 18.5645 17.7983 18.5504 17.7457L18.5321 17.6776L17.994 15.6693C17.994 15.6693 15.4009 5.9546 14.9096 4.28195C14.8692 4.14353 14.7283 4.06352 14.5883 4.09774C14.1123 4.21532 13.0572 4.4798 12.5789 4.61957C12.4404 4.65998 12.3608 4.80236 12.3951 4.94245L12.5221 5.44738C12.5491 5.55456 12.4638 5.65702 12.353 5.65189C11.4313 5.60367 10.4596 5.63517 9.43572 5.79344C8.30173 5.96794 7.01677 6.38355 6.0994 6.84661C5.41745 7.19185 4.84231 7.62623 4.34992 8.05998C3.39688 8.89743 2.47788 10.0724 0.605799 10.234C0.551755 10.2551 1.3655 10.6308 1.68332 10.8143L1.6857 10.817ZM8.18612 8.08016C9.68077 7.5868 11.6943 7.49503 12.395 7.50461C12.4098 7.50398 12.4391 7.51436 12.4452 7.53097C12.4777 7.63999 12.5899 8.07096 12.6119 8.15297C12.6144 8.16225 12.6149 8.1704 12.6139 8.17897C12.5727 8.43378 11.9455 12.3132 11.8469 12.799C11.8404 12.8306 11.8015 12.8526 11.7852 12.8537C11.2566 12.9108 10.7905 13.0157 10.5928 13.0637C10.5463 13.0762 10.5037 13.0346 10.5111 12.9878C10.6216 12.3545 11.2116 8.96763 11.2584 8.62841C11.2604 8.61128 11.2401 8.60346 11.2284 8.60327C10.5323 8.66044 10.0051 8.73536 9.53549 8.88276C9.52311 8.88607 9.49816 8.90437 9.50118 8.9218C9.52412 9.03837 9.65789 9.50667 9.70742 9.67913C9.71696 9.71472 9.69698 9.75158 9.66181 9.76267L8.73152 10.0352C8.69593 10.0447 8.65752 10.0251 8.64799 9.98954C8.56156 9.67937 8.20626 8.35336 8.14655 8.13056C8.13785 8.09807 8.17147 8.0874 8.18695 8.08325L8.18612 8.08016ZM5.35043 9.5597C5.34711 9.54732 5.3504 9.53484 5.36112 9.52533C5.47884 9.43243 6.21573 8.86517 7.29512 8.42503C7.3175 8.41572 7.36765 8.41721 7.37678 8.45125C7.51876 8.96875 8.6227 13.0825 8.75346 13.6262C8.76383 13.6649 8.73487 13.6992 8.71868 13.7069C8.44374 13.8319 7.87229 14.1625 7.72143 14.2494C7.70298 14.2609 7.67872 14.2509 7.67292 14.2292C7.56347 13.8207 6.67591 10.4898 6.55933 10.0732C6.55353 10.0516 6.52813 10.0435 6.50968 10.055L5.6762 10.6084C5.65775 10.62 5.63235 10.6118 5.62613 10.5886L5.35002 9.55815L5.35043 9.5597Z"
        fill="white"
      />
    </svg>
  );
};
