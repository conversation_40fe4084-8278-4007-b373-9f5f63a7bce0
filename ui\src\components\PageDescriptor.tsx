export const PageDescriptor = ({
  heading,
  description,
  className,
}: {
  heading: string;
  description?: string;
  className?: string;
}) => {
  return (
    <div className={`flex flex-col gap-2 ${className}`}>
      <h1 className="text-gold drop-shadow-[0_0_4px_rgba(0,0,0,0.1)] text-2xl font-medium">
        {heading}
      </h1>
      {description && (
        <h2 className="text-white/70 text-base text-balance">{description}</h2>
      )}
    </div>
  );
};
