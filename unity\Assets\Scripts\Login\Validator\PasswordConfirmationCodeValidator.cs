using System.Text.RegularExpressions;

namespace Login.Validator
{
    public class PasswordConfirmationCodeValidator: InputValidator
    {
        private const string ConfirmationCodePattern = @"[0-9]+$";
        protected override bool IsValidTextFormat(string inputText)
        {
            if (string.IsNullOrWhiteSpace(inputText))
                return false;

            return Regex.IsMatch(inputText, ConfirmationCodePattern);
        }
    }
}