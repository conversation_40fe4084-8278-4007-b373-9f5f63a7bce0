using Network.Services;
using PredictionWindow.Data;
using UnityEngine;

namespace Game.State
{
    public class PredictionWindowOpen : BaseGameState
    {
        public override void Enter()
        {
            predictionManager.PredictionWindowOpened(predictionWindowManager.CurrentPredictionWindowData);
            
            playContainer.onStartPredictionButtonPressed += StartPrediction;
            predictionWindowManager.PredictionWindowTimer.OnTimerEnd += ClosePredictionWindow;
            PredictionWindowService.PredictionWindowReceived += OnPredictionWindowReceived;
        }

        public override void Exit()
        {
            playContainer.onStartPredictionButtonPressed -= StartPrediction;
            predictionWindowManager.PredictionWindowTimer.OnTimerEnd -= ClosePredictionWindow;
            PredictionWindowService.PredictionWindowReceived -= OnPredictionWindowReceived;
        }
        
        private void StartPrediction()
        {
            predictionManager.StartPrediction();
            onFinished?.Invoke(GameStateType.PlacingPrediction);
        }

        private void OnPredictionWindowReceived(PredictionWindowData predictionWindowData)
        {
            if (predictionWindowData.Status != PredictionWindowStatus.CLOSED)
            {
                Debug.Log("Prediction Window is not closing, ignoring.");
                return;
            }

            ClosePredictionWindow();
        }
        
        private void ClosePredictionWindow()
        {
            predictionWindowManager.ClosePredictionWindow();
            onFinished?.Invoke(GameStateType.ResetGame);
        }
    }
}
