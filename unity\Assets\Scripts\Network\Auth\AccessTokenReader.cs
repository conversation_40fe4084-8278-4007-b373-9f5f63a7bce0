using System;
using System.Text;
using Network.Payload;
using UnityEngine;

namespace Network.Auth
{
    public static class AccessTokenReader
    {
        private const int AccessTokenSectionCount = 3;
        
        /// <summary>
        /// Tries to read parameters from the AccessToken, it assumes it's a JWT token
        /// </summary>
        public static AccessTokenPayload ReadAccessToken(string accessToken)
        {
            try
            {
                string[] splitToken = accessToken.Split(".");

                if (splitToken.Length != 3)
                {
                    throw new Exception($"Invalid access token section count (got {splitToken.Length}, expected {AccessTokenSectionCount})");
                }
                
                string payloadString = splitToken[1];
                
                // Pad Base64 string if it's not multiple of 4
                int remainder = payloadString.Length % 4;
                if (remainder > 0)
                {
                    int missingChars = 4 - remainder;
                    payloadString += new string('=', missingChars);
                }

                byte[] bytes = Convert.FromBase64String(payloadString);
                string dataStr = Encoding.UTF8.GetString(bytes);

                return JsonUtility.FromJson<AccessTokenPayload>(dataStr);
                
            }
            catch (Exception e)
            {
                Debug.LogError("Error trying to read AccessToken: " + e.Message);
                Debug.LogException(e);
            }
            
            return null;
        }
    }
}