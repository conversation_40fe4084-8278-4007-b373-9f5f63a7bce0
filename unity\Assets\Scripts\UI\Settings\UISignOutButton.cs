using Network.Auth;
using Network.Services;
using Sky.GenEx.Toast;
using UnityEngine;
using UnityEngine.SceneManagement;
using UnityEngine.UI;

namespace UI.Settings
{
    public class UISignOutButton : MonoBehaviour
    {
        [SerializeField]
        private Button button;
        
        public void OnSignOutPressed()
        {
            if (button.interactable)
                SignOutAsync();
        }

        private async void SignOutAsync()
        {
            try
            {
                button.interactable = false;
                string accessToken = AuthTokenManager.Instance.AccessToken;
                if (!string.IsNullOrEmpty(accessToken))
                {
                    var result = await AuthService.Logout(accessToken);
                    if (result.IsSuccess)
                    {
                        AuthTokenManager.Instance.ClearTokens();
                        SceneManager.LoadScene("Login");
                    }
                    else 
                    {
                        if (result.Error != null)
                        {
                            Debug.LogError($"Logout failed: {result.Error.Message}. Please try again later.");
                            MessageToastController.Instance.ShowToast(result.Error.Message, ToastDuration.Long, ToastType.Error);
                        }
                        else
                        {
                            Debug.LogError("Logout failed: Unknown error");
                            MessageToastController.Instance.ShowToast("Logout failed: Unknown error. Please try again later.", ToastDuration.Long, ToastType.Error);
                        }
                    }
                }
            }
            finally
            {
                button.interactable = true;
            }
        }
    }
}