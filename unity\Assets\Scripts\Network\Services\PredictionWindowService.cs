using System;
using Network.Mutations;
using Network.Response;
using Network.Subscriptions;
using PredictionWindow.Data;
using PredictionWindow.DataContracts;
using Sky.GenEx.GraphQL.Core;
using Sky.GenEx.Utilities;
using UnityEngine;

namespace Network.Services
{
    public static class PredictionWindowService
    {
        public static event Action<PredictionWindowData> PredictionWindowReceived;

        private static IGraphQLSubscriptionHandler<PredictionWindowDataContractWrapper> subscriptionHandler;

        public static async void InitializeSubscription(string tournamentId)
        {
            Debug.Log("Initializing Prediction Window Subscription for tournament: " + tournamentId);
            var subscription = new PredictionWindowSubscription(tournamentId);
            try
            {
                subscriptionHandler = await AwsClient.Golf
                    .SubscribeAsync<PredictionWindowDataContractWrapper>(subscription.SubscriptionString,
                        subscription.Name);
                subscriptionHandler.OnData += OnPredictionWindowReceived;
                subscriptionHandler.OnError += OnPredictionWindowError;
                Debug.Log("Subscribed to prediction window updates for tournament: " + tournamentId);
            }
            catch (Exception e)
            {
                // TODO: Handle subscription errors
                Debug.LogException(e);
            }
        }

        public static void StopSubscription()
        {
            try
            {
                if (subscriptionHandler == null) return;
                subscriptionHandler.OnData -= OnPredictionWindowReceived;
                subscriptionHandler.OnError -= OnPredictionWindowError;
                subscriptionHandler.Unsubscribe();
                subscriptionHandler = null;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        public static async Awaitable<Result> AddHomePlayerPrediction(PredictionWindowData predictionWindow,
            Vector3 position)
        {
            var x = UnitsUtilities.ConvertMetresToFeet(position.x);
            var z = UnitsUtilities.ConvertMetresToFeet(position.z);
            var predictionToSend = new Vector3(x, z, 0f);

            var mutation = new AddHomePlayerPredictionMutation(predictionWindow, predictionToSend);

            try
            {
                await AwsClient.Golf.MutateAsync<AddHomePlayerPredictionMutationResponse>(
                    mutation.MutationString,
                    mutation.Name
                );
                return Result.Success();
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return Result.Failure(new ServiceError(-1, ex.Message));
            }
        }

        private static void OnPredictionWindowReceived(PredictionWindowDataContractWrapper result)
        {
            if (result == null)
            {
                Debug.LogError("Received null or invalid prediction window data.");
                return;
            }

            Debug.Log("Received prediction window data with status: " + result.OnNextHomePredictionWindow.Status);

            var predictionWindowData = new PredictionWindowData(result.OnNextHomePredictionWindow);
            PredictionWindowReceived?.Invoke(predictionWindowData);
        }

        private static void OnPredictionWindowError(GraphQLError[] graphQlErrors)
        {
            foreach (var graphQlError in graphQlErrors)
            {
                Debug.LogError($"Error in prediction window subscription: {graphQlError.Message}");
            }
        }

        /// <summary>
        /// For testing purposes only. Simulates receiving a prediction window update.
        /// </summary>
        /// <param name="predictionWindow"></param>
        public static void SimulatePredictionWindowReceived(PredictionWindowDataContract predictionWindow)
        {
            var wrapper = new PredictionWindowDataContractWrapper
            {
                OnNextHomePredictionWindow = predictionWindow
            };
            OnPredictionWindowReceived(wrapper);
        }
    }
}