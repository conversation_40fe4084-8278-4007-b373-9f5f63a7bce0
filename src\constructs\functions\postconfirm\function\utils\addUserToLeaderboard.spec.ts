import { addUserToLeaderboard } from "./addUserToLeaderboard";
import { sendGraphQLRequest } from "@/utils/graphql";

jest.mock("@/utils/graphql", () => ({
  sendGraphQLRequest: jest.fn(),
}));

describe("addUserToLeaderboard", () => {
  const props = {
    gameId: "test-game",
    userId: "user-123",
    apiUrl: "https://api.example.com/graphql",
    apiId: "api-123",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should add user to leaderboard", async () => {
    await addUserToLeaderboard(props);
    expect(sendGraphQLRequest).toHaveBeenCalledWith(
      expect.objectContaining({
        apiUrl: props.apiUrl,
        apiId: props.apiId,
        body: expect.objectContaining({
          operationName: "AddScore",
          variables: {
            gameId: props.gameId,
            userId: props.userId,
            score: 0,
          },
        }),
      }),
    );
  });
});
