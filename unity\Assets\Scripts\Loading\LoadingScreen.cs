using System;
using Data;
using UnityEngine;
using UnityEngine.UI;

namespace Loading
{
    public class LoadingScreen : MonoBehaviour
    {
        [SerializeField] private GameObject carouselPrefab;
        [SerializeField] private GameObject mainPrefab;
        [SerializeField] private GameObject worldPrefab;

        [SerializeField] private Image loadingImage;

        [SerializeField] private float minLoadingTime = 3f;

        private bool showFTUE = false;

        private async void Start()
        {
            try
            {
                showFTUE = SessionData.ShowFTUE;
                await WaitAndHideLoading();
            }
            catch (Exception e)
            {
                Debug.LogError($"Error during loading: {e.Message}");
            }
        }

        private async Awaitable WaitAndHideLoading()
        {
            var time = Time.realtimeSinceStartup;
            loadingImage.fillAmount = 0f;

            if (showFTUE)
            {
                await InstantiateAsync(carouselPrefab, null, Vector3.zero, Quaternion.identity);
                SessionData.ToggleFTUE(false);
            }

            await InstantiateAsync(mainPrefab, null, Vector3.zero, Quaternion.identity);
            await InstantiateAsync(worldPrefab, null, Vector3.zero, Quaternion.identity);

            float elapsed = 0f;
            while (elapsed < minLoadingTime)
            {
                elapsed = Time.realtimeSinceStartup - time;
                loadingImage.fillAmount = Mathf.Clamp01(elapsed / minLoadingTime);
                await Awaitable.NextFrameAsync();
            }

            gameObject.SetActive(false);
        }
    }
}
