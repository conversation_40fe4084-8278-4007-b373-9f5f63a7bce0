import { signOut } from "aws-amplify/auth";
import { useEffect, useState, useCallback, useContext } from "react";
import { useNavigate } from "react-router";
import { Unity as UnityWindow, useUnityContext } from "react-unity-webgl";
import { Loader } from "../components";
import { AuthContext } from "../components/ProtectedRoute";
import { RecordPageView } from "../services/rum";

export function Unity() {
  const [isReady, setIsReady] = useState(false);
  const navigate = useNavigate();
  const authContext = useContext(AuthContext);

  const [actualLoadingProgression, setActualLoadingProgression] = useState(0);
  const {
    unityProvider,
    isLoaded,
    sendMessage,
    loadingProgression,
    addEventListener,
    removeEventListener,
    unload,
  } = useUnityContext({
    loaderUrl: "build/Build/build.loader.js",
    dataUrl: "build/Build/build.data.unityweb",
    frameworkUrl: "build/Build/build.framework.js.unityweb",
    codeUrl: "build/Build/build.wasm.unityweb",
    streamingAssetsUrl: "build/StreamingAssets",
    companyName: "Comcast",
    productName: "Game The Green",
    productVersion: "1.0.2",
  });

  useEffect(() => {
    console.log("Unity Loader Progression:", loadingProgression);
    setActualLoadingProgression(loadingProgression * 0.75);
  }, [loadingProgression, isLoaded]);

  const sendTokenToUnity = useCallback(() => {
    if (!authContext?.tokens?.accessToken) return;
    console.log(
      "Sending token to unity",
      authContext.tokens.accessToken.toString(),
    );
    sendMessage(
      "AuthService",
      "ReceiveAuthToken",
      authContext.tokens.accessToken.toString(),
    );
  }, [sendMessage, authContext]);

  const handleRequestToken = useCallback(() => {
    console.log("Requested token from Unity");
    sendTokenToUnity();
  }, [sendTokenToUnity]);

  useEffect(() => {
    // whenever the access token changes, send it to Unity
    sendTokenToUnity();
  }, [authContext?.tokens?.accessToken, sendTokenToUnity]);

  useEffect(() => {
    addEventListener("RequestToken", handleRequestToken);
    return () => {
      removeEventListener("RequestToken", handleRequestToken);
    };
  }, [addEventListener, removeEventListener, handleRequestToken]);

  const handleSignOut = useCallback(() => {
    console.log("Requested sign out from Unity");
    void signOut().then(async () => {
      try {
        await unload();
      } catch (err) {
        console.warn(err);
      } finally {
        void navigate("/welcome", { replace: true });
        location.reload();
      }
    });
  }, []);

  useEffect(() => {
    addEventListener("SignOut", handleSignOut);
    return () => {
      removeEventListener("SignOut", handleSignOut);
    };
  }, [addEventListener, removeEventListener, handleSignOut]);

  const handleUnityCustomLoadProgress = useCallback((progress: unknown) => {
    console.log("Unity emitted loaded event with progress:", progress);
    setActualLoadingProgression(0.75 + (progress as number) * 0.25);
    setIsReady(typeof progress == "number" && progress >= 1);
  }, []);

  useEffect(() => {
    addEventListener("UnityLoadProgress", handleUnityCustomLoadProgress);
    return () => {
      removeEventListener("UnityLoadProgress", handleUnityCustomLoadProgress);
    };
  }, [addEventListener, removeEventListener, handleUnityCustomLoadProgress]);

  const handleBrowserRequest = useCallback((link: unknown) => {
    console.log("Unity requesting to open: ", link);
    if (typeof link === "string") {
      window.open(link, "_blank");
    }
  }, []);

  useEffect(() => {
    addEventListener("UnityOpenLink", handleBrowserRequest);
    return () => {
      removeEventListener("UnityOpenLink", handleBrowserRequest);
    };
  }, [addEventListener, removeEventListener, handleBrowserRequest]);

  const reloadUnity = useCallback(() => {
    if (document.visibilityState !== "visible") return;
    sendMessage("AppSettings", "Reload");
  }, [sendMessage]);

  useEffect(() => {
    if (!isLoaded) return;
    let userInfo;
    if (authContext?.tokens?.idToken) {
      const idToken = authContext.tokens.idToken.payload;
      userInfo = {
        user_id: idToken.sub,
        username: idToken.preferred_username ?? idToken["cognito:username"],
        given_name: idToken.given_name,
        family_name: idToken.family_name,
      };
    }
    sendMessage(
      "AppSettings",
      "LoadJavascriptSettings",
      JSON.stringify({
        userInfo,
      }),
    );
    console.log("Sending settings to Unity");
    document.addEventListener("visibilitychange", reloadUnity);
    return () => {
      document.removeEventListener("visibilitychange", reloadUnity);
    };
  }, [isLoaded, sendMessage]);

  const [devicePixelRatio, setDevicePixelRatio] = useState<number | null>(null);
  useEffect(() => {
    setDevicePixelRatio(window.devicePixelRatio);
  }, []);

  RecordPageView("Unity");
  return (
    <div className="relative h-full w-full">
      {devicePixelRatio && (
        <UnityWindow
          id="unity-canvas"
          className="absolute h-full w-full"
          unityProvider={unityProvider}
          devicePixelRatio={devicePixelRatio}
          style={{ visibility: isLoaded ? "visible" : "hidden" }}
        />
      )}
      {!isReady && <Loader progress={actualLoadingProgression} />}
    </div>
  );
}
