using System.Text.RegularExpressions;

namespace Login.Validator
{
    public class EmailValidator : InputValidator
    {
        private const string EmailPattern = @"^[^@\s]+@[^@\s]+\.[^@\s]+$";
        protected override bool IsValidTextFormat(string inputText)
        {
            if (string.IsNullOrWhiteSpace(inputText))
                return false;

            return Regex.IsMatch(inputText, EmailPattern);
        }
    }
}
