<!DOCTYPE html>
            <html>
                <head>
                    <title>PlayerPrefsEditor-Manual</title>
	                <meta http-equiv="X-UA-Compatible" content="IE=edge"></meta>
	                <meta http-equiv="content-type" content="text/html; charset=utf-8">
                    <style type="text/css">

body {
  font-family: Helvetica, arial, sans-serif;
  font-size: 14px;
  line-height: 1.6;
  padding-top: 10px;
  padding-bottom: 10px;
  background-color: white;
  padding: 30px;
  color: #333;
}

body &gt; *:first-child {
  margin-top: 0 !important;
}

body &gt; *:last-child {
  margin-bottom: 0 !important;
}

a {
  color: #4183C4;
  text-decoration: none;
}

a.absent {
  color: #cc0000;
}

a.anchor {
  display: block;
  padding-left: 30px;
  margin-left: -30px;
  cursor: pointer;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
}

h1, h2, h3, h4, h5, h6 {
  margin: 20px 0 10px;
  padding: 0;
  font-weight: bold;
  -webkit-font-smoothing: antialiased;
  cursor: text;
  position: relative;
}

h2:first-child, h1:first-child, h1:first-child + h2, h3:first-child, h4:first-child, h5:first-child, h6:first-child {
  margin-top: 0;
  padding-top: 0;
}

h1:hover a.anchor, h2:hover a.anchor, h3:hover a.anchor, h4:hover a.anchor, h5:hover a.anchor, h6:hover a.anchor {
  text-decoration: none;
}

h1 tt, h1 code {
  font-size: inherit;
}

h2 tt, h2 code {
  font-size: inherit;
}

h3 tt, h3 code {
  font-size: inherit;
}

h4 tt, h4 code {
  font-size: inherit;
}

h5 tt, h5 code {
  font-size: inherit;
}

h6 tt, h6 code {
  font-size: inherit;
}

h1 {
  font-size: 28px;
  color: black;
}

h2 {
  font-size: 24px;
  border-bottom: 1px solid #cccccc;
  color: black;
}

h3 {
  font-size: 18px;
}

h4 {
  font-size: 16px;
}

h5 {
  font-size: 14px;
}

h6 {
  color: #777777;
  font-size: 14px;
}

p, blockquote, ul, ol, dl, li, table, pre {
  margin: 15px 0;
}

/*
hr {
  border: 0 none;
  color: #cccccc;
  height: 4px;
  padding: 0;
}
*/
body &gt; h2:first-child {
  margin-top: 0;
  padding-top: 0;
}

body &gt; h1:first-child {
  margin-top: 0;
  padding-top: 0;
}

body &gt; h1:first-child + h2 {
  margin-top: 0;
  padding-top: 0;
}

body &gt; h3:first-child, body &gt; h4:first-child, body &gt; h5:first-child, body &gt; h6:first-child {
  margin-top: 0;
  padding-top: 0;
}

a:first-child h1, a:first-child h2, a:first-child h3, a:first-child h4, a:first-child h5, a:first-child h6 {
  margin-top: 0;
  padding-top: 0;
}

h1 p, h2 p, h3 p, h4 p, h5 p, h6 p {
  margin-top: 0;
}

li p.first {
  display: inline-block;
}

ul, ol {
  padding-left: 30px;
}

ul :first-child, ol :first-child {
  margin-top: 0;
}

ul :last-child, ol :last-child {
  margin-bottom: 0;
}

dl {
  padding: 0;
}

dl dt {
  font-size: 14px;
  font-weight: bold;
  font-style: italic;
  padding: 0;
  margin: 15px 0 5px;
}

dl dt:first-child {
  padding: 0;
}

dl dt &gt; :first-child {
  margin-top: 0;
}

dl dt &gt; :last-child {
  margin-bottom: 0;
}

dl dd {
  margin: 0 0 15px;
  padding: 0 15px;
}

dl dd &gt; :first-child {
  margin-top: 0;
}

dl dd &gt; :last-child {
  margin-bottom: 0;
}

blockquote {
  border-left: 4px solid #dddddd;
  padding: 0 15px;
  color: #777777;
}

blockquote &gt; :first-child {
  margin-top: 0;
}

blockquote &gt; :last-child {
  margin-bottom: 0;
}

table {
  padding: 0;
}
table tr {
  border-top: 1px solid #cccccc;
  background-color: white;
  margin: 0;
  padding: 0;
}

table tr:nth-child(2n) {
  background-color: #f8f8f8;
}

table tr th {
  font-weight: bold;
  border: 1px solid #cccccc;
  text-align: left;
  margin: 0;
  padding: 6px 13px;
}

table tr td {
  border: 1px solid #cccccc;
  text-align: left;
  margin: 0;
  padding: 6px 13px;
}

table tr th :first-child, table tr td :first-child {
  margin-top: 0;
}

table tr th :last-child, table tr td :last-child {
  margin-bottom: 0;
}

img {
  max-width: 100%;
}

span.frame {
  display: block;
  overflow: hidden;
}

span.frame &gt; span {
  border: 1px solid #dddddd;
  display: block;
  float: left;
  overflow: hidden;
  margin: 13px 0 0;
  padding: 7px;
  width: auto;
}

span.frame span img {
  display: block;
  float: left;
}

span.frame span span {
  clear: both;
  color: #333333;
  display: block;
  padding: 5px 0 0;
}

span.align-center {
  display: block;
  overflow: hidden;
  clear: both;
}

span.align-center &gt; span {
  display: block;
  overflow: hidden;
  margin: 13px auto 0;
  text-align: center;
}

span.align-center span img {
  margin: 0 auto;
  text-align: center;
}

span.align-right {
  display: block;
  overflow: hidden;
  clear: both;
}

span.align-right &gt; span {
  display: block;
  overflow: hidden;
  margin: 13px 0 0;
  text-align: right;
}

span.align-right span img {
  margin: 0;
  text-align: right;
}

span.float-left {
  display: block;
  margin-right: 13px;
  overflow: hidden;
  float: left;
}

span.float-left span {
  margin: 13px 0 0;
}

span.float-right {
  display: block;
  margin-left: 13px;
  overflow: hidden;
  float: right;
}

span.float-right &gt; span {
  display: block;
  overflow: hidden;
  margin: 13px auto 0;
  text-align: right;
}

code, tt {
  margin: 0 2px;
  padding: 0 5px;
  white-space: nowrap;
  border: 1px solid #eaeaea;
  background-color: #f8f8f8;
  border-radius: 3px;
}

pre code {
  margin: 0;
  padding: 0;
  white-space: pre;
  border: none;
  background: transparent;
}

.highlight pre {
  background-color: #f8f8f8;
  border: 1px solid #cccccc;
  font-size: 13px;
  line-height: 19px;
  overflow: auto;
  padding: 6px 10px;
  border-radius: 3px;
}

pre {
  background-color: #f8f8f8;
  border: 1px solid #cccccc;
  font-size: 13px;
  line-height: 19px;
  overflow: auto;
  padding: 6px 10px;
  border-radius: 3px;
}

pre code, pre tt {
  background-color: transparent;
  border: none;
}
                    </style>
                </head>
                <body style="">
                <h1 id="0">PlayerPrefs Editor for Unity 3D</h1>
<p id="2">Tool extension for the Unity Editor that enables easy access to the player preferences over a simple UI. Allows to view, add, remove and modify entries on the development machine.</p>
<h2 id="4">Support</h2>
<p id="6"><a href="https://github.com/Dysman/bgTools-playerPrefsEditor">GitHub</a> | <a href="https://www.bgtools.de/post/playerprefseditor/">Website</a> | <a href="mailto:<EMAIL>">Mail</a> | <a href="https://discord.gg/8rcPZrD">Discord</a></p>
<h2 id="8">Features</h2>
<ul id="10">
<li id="10">Add, remove and edit PlayerPrefs</li>
<li id="11">Intuitive visual editor</li>
<li id="12">Works with standard Unity PlayerPrefs</li>
<li id="13">Monitors changes from code</li>
<li id="14">Supports all editors (Windows, Linux, MacOS)</li>
<li id="15">Lightweight dockable for full integration in your workflow</li>
<li id="16">Supports both skins (Personal, Professional)</li>
</ul>
<h2 id="18">Usage</h2>
<p id="20">The PlayerPrefs Editor is located in the top menu at Tools/BG Tools/PlayerPrefs Editor. It's a standard dockable window, so place it wherever it helps to be productive.</p>
<p id="22"><img src="./Images/bgtools_ppe_manual_layout.png" width="100%" style="max-width:800px" alt="PlayerPrefs editor window layout" /></p>
<p id="24">The PlayerPrefs Editor window displays:</p>
<ul id="25">
<li id="25">(A) Filter field</li>
<li id="26">(B) Sort mode</li>
<li id="27">(C) Toggle 'System changes monitoring' behavior</li>
<li id="28">(D) Refresh data</li>
<li id="29">(E) Delete all data</li>
<li id="30">(F) Operating system and path to PlayerPrefs data</li>
<li id="31">(G) PlayerPrefs data list (Key, Type, Value)</li>
<li id="32">(H) Add/Remove a PlayerPrefs entry</li>
<li id="33">(I) Toggle visibility of system defined PlayerPrefs</li>
</ul>
<h3 id="35">Modify Entries</h3>
<p id="37">The PlayerPrefs Editor allow to add, remove and edit PlayerPrefs data.</p>
<p id="39"><strong>Add a new entry</strong></p>
<p id="41">Press the '+' button and select the type of the new PlayerPref entry. It's not possible to modify this type later. Add the key for the new entry in the upcoming dialog field. Additionally the dialog will provide feedback when it detected unintended overrides or invalid characters.</p>
<p id="43"><strong>Remove a existing entry</strong></p>
<p id="45">Select the entry from the list that you want to delete. Press the '-' button to remove this entry. Confirm the warning dialog to finish the operation.</p>
<p id="47"><strong>Modify a existing entry</strong></p>
<p id="49">To change a value of a existing entry do this directly in the value field in the PlayerPref list.</p>
<h3 id="51">Sort &amp; Filter</h3>
<p id="53"><strong>Sorting</strong></p>
<p id="55">Circle trought the sorting funtions by pressing the (B) button in the toolbar.</p>
<p id="57">Following sorting function are aviliable for the PlayerPref entries:</p>
<ul id="58">
<li id="58">None</li>
<li id="59">Ascending</li>
<li id="60">Descending</li>
</ul>
<p id="62"><strong>Filtering</strong></p>
<p id="64">Enter a text into the the search field (A) in the toolbar to filter the PlayerPrefs data list (G).</p>
<p id="66"><img src="./Images/bgtools_ppe_manual_filterModes.png" width="50%" style="max-width:350px" alt="PlayerPrefs editor searchfield modes" /></p>
<p id="68">Additionally select the mode for filtering by pressing on the magnifying glass icon in the search field. Choose between filtering the existing PlayPrefs by key or value. The current search target will be shown in the searchfield if no search string is present.</p>
<h3 id="70">Monitoring system changes</h3>
<p id="72">The plugin can monitor changes at runtime automatically and keep the view up-to-date. This detection is active by default, but it can be turned off over the (C) button in the toolbar.</p>
<h2 id="74">Samples</h2>
<p id="76">This package includes two samples for testing purposes:</p>
<p id="78"><strong>Test Value Menu</strong></p>
<blockquote id="79">
<p id="79">Adds new entries into the top menu Tools/BG Tools/PlayerPrefs Test Values. This allows easily to add text values to the PlayerPrefs of the current project.</p>
</blockquote>
<p id="81"><strong>Sample Scene</strong></p>
<blockquote id="82">
<p id="82">Simple UI that manipulates PlayerPrefs entries on runtime.</p>
</blockquote>
<h2 id="84">Technical details</h2>
<h3 id="85">Requirements</h3>
<p id="87">This version of PlayerPrefs Editor is compatible with the following versions of the Unity Editor:</p>
<blockquote id="89">
<p id="89">2019.4 and later (recommended)<br />
Windows, MacOS, Linux</p>
</blockquote>
<h3 id="92">Limitations MacOS</h3>
<p id="94">Due to technical on MacOS it take time to update the persistent file that stores the PlayerPrefs. To avoid inconsistent data the plugin will show a loading screen until the data can be fully refreshed. Sorry for the inconvenience.</p>
<p id="96">Keep in mind that it's possible to deactivate the automatic refresh in the settings.</p>

                </body>
            <html>
            