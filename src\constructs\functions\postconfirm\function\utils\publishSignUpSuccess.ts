import { sendGraphQLRequest } from "@/utils/graphql";

type PublishSignUpSuccessProps = {
  userId: string;
  deviceId: string;
  apiUrl: string;
  apiId: string;
};

export const publishSignUpSuccess = async (
  props: PublishSignUpSuccessProps,
) => {
  const { userId, deviceId, apiUrl, apiId } = props;

  const input = {
    deviceId,
    playerId: userId,
  };

  const mutation = {
    query: `
      mutation PublishSignUpSuccess($input: PublishSignUpSuccessInput!) {
        publishSignUpSuccess(input: $input) {
          deviceId
          playerId
        }
      }
    `,
    operationName: "PublishSignUpSuccess",
    variables: {
      input,
    },
  };

  await sendGraphQLRequest({
    body: mutation,
    apiUrl,
    apiId,
  });
};
