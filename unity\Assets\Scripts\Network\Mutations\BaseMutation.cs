namespace Network.Mutations
{
    // Structure for mutations in the network layer.
    // Will contain Name and MutationString properties in derived classes.
    public abstract class BaseMutation
    {
        /// <summary>
        /// The name of the mutation, used for logging and debugging.
        /// </summary>
        public abstract string Name { get; }

        /// <summary>
        /// The GraphQL mutation string to be executed.
        /// </summary>
        public abstract string MutationString { get; }
    }
}