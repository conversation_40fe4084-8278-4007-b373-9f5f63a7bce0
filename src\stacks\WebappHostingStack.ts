import { CloudFrontToS3 } from "@aws-solutions-constructs/aws-cloudfront-s3";
import { WafwebaclToCloudFront } from "@aws-solutions-constructs/aws-wafwebacl-cloudfront";
import { isDeploymentStageProdOrStaging } from "@comcast/gee-aws-cdk-shared";
import { getEnvironmentVariable } from "@sky-uk/coins-utilities";
import * as cdk from "aws-cdk-lib";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as cloudfront from "aws-cdk-lib/aws-cloudfront";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as wafv2 from "aws-cdk-lib/aws-wafv2";
import { actions } from "cdk-iam-actions";
import { Construct } from "constructs";

type WebappHostingStackProps = cdk.NestedStackProps &
  Readonly<{
    stage: string;
    leaderboardApiUrl: string;
    golfApiUrl: string;
    certificate?: acm.ICertificate;
    domainName?: string;
    golfApiWsUrl: string;
    rumProxyUrl: string;
    webappRegion: string;
  }>;

const bucketProps: s3.BucketProps = {
  autoDeleteObjects: true,
  removalPolicy: cdk.RemovalPolicy.DESTROY,
};

const loggingBucketLifecycleRules: s3.LifecycleRule[] = [
  {
    expiration: cdk.Duration.days(30),
  },
];

export class WebappHostingStack extends cdk.NestedStack {
  readonly originBucket: s3.IBucket;

  readonly distribution: cloudfront.Distribution;

  readonly domainName: string;
  readonly leaderboardApiUrl: string;
  readonly golfApiUrl: string;
  readonly golfApiWsUrl: string;
  readonly rumProxyUrl: string;
  readonly webappRegion: string;

  constructor(scope: Construct, id: string, props: WebappHostingStackProps) {
    super(scope, id, props);

    this.leaderboardApiUrl = props.leaderboardApiUrl;
    this.golfApiUrl = props.golfApiUrl;
    this.golfApiWsUrl = props.golfApiWsUrl;
    this.rumProxyUrl = props.rumProxyUrl;
    this.webappRegion = props.webappRegion;

    const { certificate, domainName, stage } = props;

    const cloudFrontToS3 = new CloudFrontToS3(this, "GolfWebApp", {
      cloudFrontDistributionProps: {
        geoRestriction: cloudfront.GeoRestriction.allowlist("US", "GB"),
        errorResponses: [
          {
            // redirect 403 to index.html to fix SPA routing
            httpStatus: 403,
            responseHttpStatus: 200,
            responsePagePath: "/index.html",
          },
        ],
        certificate,
        domainNames: domainName ? [domainName, `www.${domainName}`] : undefined,
      },
      insertHttpSecurityHeaders: false,
      responseHeadersPolicyProps: this.getResponseHeadersPolicyProps(stage),
      logS3AccessLogs: true,
      logCloudFrontAccessLog: true,
      bucketProps,
      loggingBucketProps: {
        ...bucketProps,
        lifecycleRules: loggingBucketLifecycleRules,
      },
      cloudFrontLoggingBucketProps: {
        ...bucketProps,
        lifecycleRules: loggingBucketLifecycleRules,
      },
      cloudFrontLoggingBucketAccessLogBucketProps: {
        ...bucketProps,
        lifecycleRules: loggingBucketLifecycleRules,
      },
    });

    const distribution = cloudFrontToS3.cloudFrontWebDistribution;

    this.setupResponseHeaders(distribution);
    this.setupWaf(distribution, stage);

    this.originBucket = cloudFrontToS3.s3Bucket!;
    this.distribution = distribution;
    this.domainName = distribution.domainName;
  }

  private setupResponseHeaders(distribution: cloudfront.Distribution) {
    const responseHeaderFn = this.getResponseHeaderFn();
    const cfnD = distribution.node.defaultChild as cloudfront.CfnDistribution;

    cfnD.addOverride(
      "Properties.DistributionConfig.DefaultCacheBehavior.FunctionAssociations",
      [
        {
          EventType: "viewer-response",
          FunctionARN: responseHeaderFn.functionArn,
        },
      ],
    );
  }

  private getResponseHeaderFn(): cloudfront.Function {
    return new cloudfront.Function(
      this,
      "UnityWebContentEncodingResponseHeaderFn",
      {
        comment:
          "Sets content headers for unity build artifacts based on file extension",
        code: cloudfront.FunctionCode.fromInline(`
          function handler(event) { 
            var response = event.response; 
            var headers = response.headers; 
            var uri = event.request.uri;
            if (uri.endsWith('.unityweb')) {
              headers['content-encoding'] = {value: 'br'};
            }
            if (uri.includes('.wasm')) {
              headers['content-type'] = {value: 'application/wasm'};
            } else if (uri.includes('.js')) {
              headers['content-type'] = {value: 'application/javascript'};
            }
            return response; 
          }
      `),
      },
    );
  }

  private getResponseHeadersPolicyProps(
    stage: string,
  ): cloudfront.ResponseHeadersPolicyProps {
    const inlineScriptCspHash = getEnvironmentVariable(
      "INLINE_SCRIPT_CSP_HASH",
    );

    const oauth2TokenUrl = `${
      stage === "prod"
        ? "https://auth.gamethegreen.com"
        : `https://*.auth.${this.webappRegion}.amazoncognito.com`
    }/oauth2/token`;

    const additionalConnectSrc =
      `${this.leaderboardApiUrl} ` +
      `${this.golfApiUrl} ` +
      `${this.golfApiWsUrl} ` +
      `${this.rumProxyUrl} ` +
      "https://pga-tour-res.cloudinary.com " +
      `https://cognito-idp.${this.webappRegion}.amazonaws.com ` +
      `https://cognito-identity.${this.webappRegion}.amazonaws.com ` +
      `${oauth2TokenUrl} ` +
      "https://d19fy1jjvzj792.cloudfront.net " +
      "https://config.uca.cloud.unity3d.com " +
      "https://cdp.cloud.unity3d.com " +
      "https://perf-events.cloud.unity3d.com " +
      "blob:";

    const securityHeadersBehavior: cloudfront.ResponseSecurityHeadersBehavior =
      {
        contentSecurityPolicy: {
          override: true,
          contentSecurityPolicy:
            "default-src 'none'; " +
            "img-src 'self'; " +
            `script-src 'self' 'wasm-unsafe-eval' blob: '${inlineScriptCspHash}'; ` +
            "style-src 'self' fonts.googleapis.com; " +
            "object-src 'self' blob:; " +
            "frame-ancestors 'none'; " +
            "base-uri 'none'; " +
            "form-action 'none'; " +
            `connect-src 'self' ${additionalConnectSrc}; ` +
            "font-src 'self' fonts.gstatic.com; " +
            "manifest-src 'self';",
        },
        strictTransportSecurity: {
          override: true,
          accessControlMaxAge: cdk.Duration.days(365),
          preload: true,
          includeSubdomains: true,
        },
        xssProtection: {
          override: true,
          protection: true,
          modeBlock: true,
        },
        frameOptions: {
          override: true,
          frameOption: cloudfront.HeadersFrameOption.DENY,
        },
        contentTypeOptions: {
          override: true,
        },
      };
    return {
      serverTimingSamplingRate: 10,
      securityHeadersBehavior,
      removeHeaders: ["Server"],
      customHeadersBehavior: {
        customHeaders: [
          {
            header: "x-robots-tag",
            value: "noindex, nofollow",
            override: true,
          },
        ],
      },
    };
  }

  private setupWaf(distribution: cloudfront.Distribution, stage: string) {
    const { webacl } = new WafwebaclToCloudFront(this, "GolfWebappWaf", {
      existingCloudFrontWebDistribution: distribution,
      webaclProps: {
        visibilityConfig: {
          cloudWatchMetricsEnabled: true,
          metricName: "GolfWebappWaf",
          sampledRequestsEnabled: true,
        },
      },
    });

    if (isDeploymentStageProdOrStaging(stage)) {
      const wafLogGroup = new logs.LogGroup(this, "GolfWafLogGroup", {
        retention: logs.RetentionDays.ONE_MONTH,
        logGroupName: `aws-waf-logs-${webacl.attrId}`,
      });

      wafLogGroup.node.addDependency(webacl);

      wafLogGroup.addToResourcePolicy(
        new iam.PolicyStatement({
          effect: iam.Effect.ALLOW,
          actions: [
            actions.Logs.CREATE_LOG_STREAM,
            actions.Logs.PUT_LOG_EVENTS,
          ],
          principals: [new iam.ServicePrincipal("waf.amazonaws.com")],
          resources: [`${wafLogGroup.logGroupArn}`],
        }),
      );

      new wafv2.CfnLoggingConfiguration(this, "GolfWafLoggingConfig", {
        resourceArn: webacl.attrArn,
        logDestinationConfigs: [wafLogGroup.logGroupArn],
      });
    }
  }
}
