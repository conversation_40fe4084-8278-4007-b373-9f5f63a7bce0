﻿using System.Collections.Generic;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEngine;

namespace Login.Validator
{
    public class PasswordValidator : InputValidator
    {
        private const string SpecialCharacterPattern = @"[!@#$%^&*(),.?""{}|<>[\]\\/~`+=_\-]";

        [SerializeField] private int minLength = 8;
        [SerializeField] private int maxLength = 128;

        private void Start()
        {
            if (textInputField != null && !ignoreValidation)
            {
                textInputField.onValueChanged.AddListener(OnPasswordChanged);
            }
        }

        private List<string> GetUnmetRequirements(string input)
        {
            var unmet = new List<string>();

            if (input.Length < minLength)
                unmet.Add($"Password must be at least {minLength} characters long.");
            if (!Regex.IsMatch(input, "[A-Z]"))
                unmet.Add("Password must contain at least one uppercase letter.");
            if (!Regex.IsMatch(input, "[a-z]"))
                unmet.Add("Password must contain at least one lowercase letter.");
            if (!Regex.IsMatch(input, @"\d"))
                unmet.Add("Password must contain at least one numeric digit.");
            if (!Regex.IsMatch(input, SpecialCharacterPattern))
                unmet.Add("Password must contain at least one special character.");
            if (input.Length > maxLength)
                unmet.Add($"Password must be no more than {maxLength} characters long.");
            if (input.Any(char.IsWhiteSpace))
                unmet.Add("Password must not contain spaces or whitespace characters.");
            if (input.Any(char.IsControl))
                unmet.Add("Password must not contain control characters.");
            if (Regex.IsMatch(input, @"\p{Cs}"))
                unmet.Add("Password must not contain emojis.");

            return unmet;
        }

        private void OnPasswordChanged(string input)
        {
            var unmet = GetUnmetRequirements(input);

            if (unmet.Count == 0)
                validationResultText.text = "";
            else
                validationResultText.text = string.Join("\n", unmet);
        }

        protected override bool IsValidTextFormat(string inputText)
        {
            var unmet = GetUnmetRequirements(inputText);

            if (unmet.Count > 0)
            {
                invalidFormatMessage = string.Join("\n", unmet);
                return false;
            }

            return true;
        }
    }
}
