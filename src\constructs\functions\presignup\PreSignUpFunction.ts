import path from "path";
import { BaseNodejsFunction } from "@comcast/gee-aws-cdk-shared";
import { Construct } from "constructs";

type PreSignUpFunctionProps = Readonly<{
  householdTableName: string;
  maxHouseholdSize: string;
  appVariant: string;
}>;
export class PreSignUpFunction extends BaseNodejsFunction {
  constructor(scope: Construct, id: string, props: PreSignUpFunctionProps) {
    super(scope, id, {
      entry: path.join(__dirname, "function/index.ts"),
      environment: {
        HOUSEHOLD_TABLE_NAME: props.householdTableName,
        MAX_HOUSEHOLD_SIZE: props.maxHouseholdSize,
        APP_VARIANT: props.appVariant,
      },
    });
  }
}
