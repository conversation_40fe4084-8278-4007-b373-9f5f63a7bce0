using Network.Response;
using System;
using Leaderboard.Data;
using Network.DataContracts;
using Network.Queries;
using UnityEngine;
using Data;

namespace Network.Services
{
    public static class LeaderBoardService
    {
        private const int leaderboardQueryLimit = 10;

        public static async Awaitable<Result<LeaderboardData>> GetLeaderboard()
        {
            var tournamentId = SessionData.TournamentId;
            var userId = SessionData.UserId;
            var query = new GetLeaderboardQuery(tournamentId, userId, leaderboardQueryLimit);
            try
            {
                var response = await AwsClient.Leaderboard.QueryAsync<LeaderboardDataContract>(query.QueryString, query.Name);

                if (response.IsSuccess)
                {
                    if (response.Data?.LeaderboardByUserId?.Current == null &&
                        response.Data?.LeaderboardByUserId?.Prev == null &&
                        response.Data?.LeaderboardByUserId?.Next == null)
                    {
                        return Result<LeaderboardData>.Failure(new ServiceError(-1, "Leaderboard data is null"));
                    }

                    var leaderboardData = new LeaderboardData(response.Data);
                    Debug.Log($"Leaderboard data received: Rank {leaderboardData.Current.Rank}, Score {leaderboardData.Current.Score}");
                    return Result<LeaderboardData>.Success(leaderboardData);
                }
                else
                {
                    return Result<LeaderboardData>.Failure(new ServiceError(-1, response.Errors[0]?.Message ?? "Unknown error"));
                }
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return Result<LeaderboardData>.Failure(new ServiceError(-1, ex.Message));
            }
        }
    }
}