using System;
using Bootstrap;
using Constants;
using Login.State;
using Sky.GenEx.Toast;
using UnityEngine;
using UnityEngine.SceneManagement;

namespace Login
{
    [Serializable]
    public struct LoginState
    {
        public AuthState StateType;
        public BaseLoginState StateObject;
    }


    public class LoginManager : MonoBehaviour
    {
        [SerializeField] private LoginState[] LoginStates;


        private LoginState currentState;

        private void Start()
        {
            foreach (var loginState in LoginStates)
            {
                if (loginState.StateObject == null)
                {
                    Debug.LogError($"LoginState {loginState.StateType} is not assigned in the inspector.");
                    return;
                }

                loginState.StateObject.SetupState(ChangeStateTo);
            }

            ChangeStateTo(AuthState.Welcome);
        }


        private async void LoadMain()
        {
            try
            {
                await BootProcesses.StartNetworkProcesses();
                Debug.Log("[LoginManager] Loading main scene...");
                SceneManager.LoadScene(BootstrapKeys.MAIN_SCENE_PATH);
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong, ToastDuration.Long,
                    ToastType.Error);
            }
        }

        private void ChangeStateTo(AuthState state)
        {
            currentState.StateObject?.Exit();

            if (state == AuthState.Authenticated)
            {
                LoadMain();
                return;
            }

            foreach (var loginState in LoginStates)
            {
                if (loginState.StateType == state)
                {
                    currentState = loginState;
                    currentState.StateObject.Enter();
                    return;
                }
            }

            Debug.LogError($"State {state} not found in LoginStates.");
        }
    }
}