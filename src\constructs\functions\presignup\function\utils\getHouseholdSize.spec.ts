import { getHouseholdSize } from "./getHouseholdSize";
import { DynamoDBClient, GetItemCommand } from "@aws-sdk/client-dynamodb";
import { mockClient } from "aws-sdk-client-mock";

jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
const ddbMock = mockClient(DynamoDBClient);

describe("getHouseholdSize", () => {
  const props = {
    tableName: "TestTable",
    deviceId: "device-123",
  };

  beforeEach(() => {
    ddbMock.reset();
  });

  it("should return the household size when userCount exists", async () => {
    ddbMock.on(GetItemCommand).resolves({
      Item: { userCount: { N: "4" } },
    });
    const result = await getHouseholdSize(props);
    expect(result).toBe(4);
  });

  it("should return 0 when userCount does not exist", async () => {
    ddbMock.on(GetItemCommand).resolves({ Item: {} });
    const result = await getHouseholdSize(props);
    expect(result).toBe(0);
  });

  it("should throw and log on error", async () => {
    ddbMock.on(GetItemCommand).rejects(new Error("fail"));
    await expect(getHouseholdSize(props)).rejects.toThrow(
      "Get household size failed",
    );
  });
});
