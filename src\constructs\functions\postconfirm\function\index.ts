import { injectLambdaContext } from "@aws-lambda-powertools/logger";
import middy from "@middy/core";
import validator from "@middy/validator";
import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";
import Ajv from "ajv";
import { baseHandler } from "./baseHandler";
import { inputSchema } from "./schemas";

const ajv = new Ajv();

export const handler = middy(baseHandler)
  .use(injectLambdaContext(logger.getInstance(), { logEvent: true }))
  .use(
    validator({
      eventSchema: ajv.compile(inputSchema),
    }),
  );
