import { isDeploymentStageProdOrStaging } from "@comcast/gee-aws-cdk-shared";
import { NestedStack, RemovalPolicy, SecretValue } from "aws-cdk-lib";
import * as cdk from "aws-cdk-lib";
import { GraphqlApi } from "aws-cdk-lib/aws-appsync";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import {
  OAuthScope,
  ProviderAttribute,
  UserPool,
  UserPoolClient,
  UserPoolClientIdentityProvider,
  UserPoolIdentityProviderGoogle,
  UserPoolOperation,
} from "aws-cdk-lib/aws-cognito";
import * as dynamodb from "aws-cdk-lib/aws-dynamodb";
import { Construct } from "constructs";
import { PostConfirmFunction } from "@/constructs/functions/postconfirm/PostConfirmFunction";
import { PreSignUpFunction } from "@/constructs/functions/presignup/PreSignUpFunction";

type AuthStackProps = Readonly<{
  stage: string;
  appVariant: string;
  domainName: string;
  leaderboardApiUrl: string;
  leaderboardApiId: string;
  golfApiUrl: string;
  golfApiId: string;
  maxHouseholdSize: string;
  certificate?: acm.ICertificate;
}>;

export class AuthStack extends NestedStack {
  readonly userPoolId: string;
  readonly userPoolClientId: string;
  readonly userPoolDomain: string;
  readonly householdTableArn: string;

  constructor(scope: Construct, id: string, props: AuthStackProps) {
    super(scope, id);

    const {
      stage,
      appVariant,
      domainName,
      leaderboardApiId,
      leaderboardApiUrl,
      golfApiUrl,
      golfApiId,
      maxHouseholdSize,
    } = props;

    const householdTable = new dynamodb.Table(this, "HouseholdTable", {
      partitionKey: {
        name: "pk",
        type: dynamodb.AttributeType.STRING,
      },
      sortKey: { name: "signInAlias", type: dynamodb.AttributeType.STRING },
      pointInTimeRecovery: isDeploymentStageProdOrStaging(stage),
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
    });

    householdTable.addGlobalSecondaryIndex({
      indexName: "SignInAliasIndex",
      partitionKey: {
        name: "signInAlias",
        type: dynamodb.AttributeType.STRING,
      },
      projectionType: dynamodb.ProjectionType.KEYS_ONLY,
    });

    this.householdTableArn = householdTable.tableArn;

    const preSignUpFunc = new PreSignUpFunction(this, PreSignUpFunction.name, {
      householdTableName: householdTable.tableName,
      maxHouseholdSize,
      appVariant,
    });

    const postConfirmFunc = new PostConfirmFunction(
      this,
      PostConfirmFunction.name,
      {
        leaderboardApiUrl,
        leaderboardApiId,
        golfApiUrl,
        golfApiId,
        householdTableName: householdTable.tableName,
        maxHouseholdSize,
        appVariant,
      },
    );

    householdTable.grantReadWriteData(preSignUpFunc);
    householdTable.grantReadWriteData(postConfirmFunc);

    const golfApi = GraphqlApi.fromGraphqlApiAttributes(this, "GolfAPI", {
      graphqlApiId: golfApiId,
      graphqlApiArn: `arn:aws:appsync:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:apis/${golfApiId}`,
    });

    const leaderboardApi = GraphqlApi.fromGraphqlApiAttributes(
      this,
      "LeaderboardAPI",
      {
        graphqlApiId: leaderboardApiId,
        graphqlApiArn: `arn:aws:appsync:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:apis/${leaderboardApiId}`,
      },
    );

    golfApi.grantMutation(postConfirmFunc);
    golfApi.grantQuery(postConfirmFunc);
    leaderboardApi.grantMutation(postConfirmFunc);

    const googleClientId = SecretValue.secretsManager("/auth/social/google", {
      jsonField: "clientId",
    });
    const googleClientSecret = SecretValue.secretsManager(
      "/auth/social/google",
      {
        jsonField: "clientSecret",
      },
    );

    const userPool = new UserPool(this, "UserPool", {
      removalPolicy: RemovalPolicy.DESTROY,
      userPoolName: `${props?.stage}-golf-auth-user-pool`,
      selfSignUpEnabled: true,
      autoVerify: { email: true },
      signInAliases: {
        username: true,
        email: true,
      },
      customAttributes: {
        deviceId: new cdk.aws_cognito.StringAttribute({
          minLen: 1,
          maxLen: 128,
          mutable: true,
        }),
      },
      standardAttributes: {
        email: { required: true, mutable: true },
      },
    });

    this.userPoolId = userPool.userPoolId;

    // NOTE: Disabling certificate until we have a domain
    // const userPoolDomain = userPool.addDomain("UserPoolDomain", {
    //   ...(isDeploymentStageProd(stage) && certificate
    //     ? {
    //         customDomain: {
    //           domainName: `auth.${domainName}`,
    //           certificate,
    //         },
    //       }
    //     : {
    //         cognitoDomain: {
    //           domainPrefix: `${this.node.addr}`,
    //         },
    //       }),
    // });
    const userPoolDomain = userPool.addDomain("UserPoolDomain", {
      cognitoDomain: {
        domainPrefix: `${this.node.addr}`,
      },
    });

    // NOTE: Disabling certificate until we have a domain
    // this.userPoolDomain = isDeploymentStageProd(stage)
    //   ? `auth.${domainName}`
    //   : `${userPoolDomain.domainName}.auth.${cdk.Aws.REGION}.amazoncognito.com`;
    this.userPoolDomain = `${userPoolDomain.domainName}.auth.${cdk.Aws.REGION}.amazoncognito.com`;

    const googleProvider = new UserPoolIdentityProviderGoogle(this, "Google", {
      userPool,
      clientId: googleClientId.toString(),
      clientSecretValue: googleClientSecret,
      scopes: ["email", "openid", "profile"],
      attributeMapping: {
        email: ProviderAttribute.GOOGLE_EMAIL,
        givenName: ProviderAttribute.GOOGLE_GIVEN_NAME,
        familyName: ProviderAttribute.GOOGLE_FAMILY_NAME,
      },
    });

    const callbackAndLogoutUrls = [
      `https://${domainName}/`,
      // NOTE: Disabling certificate until we have a domain
      // ...(isDeploymentStageProd(stage) ? [`https://www.${domainName}`] : []),
    ];
    const userPoolClient = new UserPoolClient(this, "UserPoolClient", {
      userPool,
      generateSecret: false,
      authFlows: {
        userPassword: true,
      },
      idTokenValidity: cdk.Duration.hours(1),
      accessTokenValidity: cdk.Duration.hours(1),
      refreshTokenValidity: cdk.Duration.days(5),
      oAuth: {
        flows: {
          authorizationCodeGrant: true,
        },
        callbackUrls: callbackAndLogoutUrls,
        logoutUrls: callbackAndLogoutUrls,
        scopes: [
          OAuthScope.EMAIL,
          OAuthScope.OPENID,
          OAuthScope.PROFILE,
          OAuthScope.COGNITO_ADMIN,
        ],
      },
      supportedIdentityProviders: [UserPoolClientIdentityProvider.GOOGLE],
    });

    this.userPoolClientId = userPoolClient.userPoolClientId;

    userPoolClient.node.addDependency(googleProvider);

    userPool.addTrigger(UserPoolOperation.PRE_SIGN_UP, preSignUpFunc);
    userPool.addTrigger(UserPoolOperation.POST_CONFIRMATION, postConfirmFunc);
  }
}
