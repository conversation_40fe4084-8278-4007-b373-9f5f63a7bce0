import {
  confirmResetPassword,
  getCurrentUser,
  resetPassword,
  signIn,
  signOut,
} from "aws-amplify/auth";
import { getAwsRum } from "./rum";

// attempt user sign in and redirect to app on success
export const attemptSignIn = async (username: string, password: string) => {
  try {
    await signIn({ username, password });
    sessionStorage.removeItem("successfullyResetPassword");
    const { userId } = await getCurrentUser();
    if (userId) {
      getAwsRum().addSessionAttributes({ userId });
      getAwsRum()?.recordEvent("login", {
        userId,
        status: "success",
      });
    }
    window.location.href = "/";
  } catch (err) {
    const { name: errorType } = err as any;
    console.warn(errorType, err);
    if (errorType === "UserAlreadyAuthenticatedException") {
      await signOut();
      await attemptSignIn(username, password);
      return;
    }
    throw errorType;
  }
};

// send a reset password code to the user's email
export const sendResetPasswordCode: (
  email: string,
) => Promise<{ success: boolean; type?: string; message?: string }> = async (
  email,
) => {
  try {
    const output = await resetPassword({ username: email });
    return {
      success:
        output.nextStep.resetPasswordStep ===
        "CONFIRM_RESET_PASSWORD_WITH_CODE",
    };
  } catch (err) {
    const { name: errorType } = err as any;
    console.warn(errorType, err);
    switch (errorType) {
      case "UserNotFoundException":
      case "InvalidParameterException":
        return {
          success: false,
          type: "credentials",
          message: "Email not recognised",
        };
      case "LimitExceededException":
        return {
          success: false,
          type: "limit",
          message:
            "You have exceeded the limit for attempting to change your password. Please try again later.",
        };
      default:
        return {
          success: false,
          type: "unknown",
          message: "An unknown error has occurred - please try again.",
        };
    }
  }
};

// confirm the new password for a user with the reset code
export const confirmNewPassword: (
  email: string,
  code: string,
  newPassword: string,
) => Promise<{ success: boolean; type?: string; message?: string }> = async (
  email: string,
  code: string,
  newPassword: string,
) => {
  try {
    await confirmResetPassword({
      username: email,
      confirmationCode: code,
      newPassword,
    });
    sessionStorage.setItem("successfullyResetPassword", "true");
    window.location.href = "/login";
    return { success: true };
  } catch (err) {
    const { name: errorType } = err as any;
    console.warn(errorType, err);
    if (errorType === "CodeMismatchException") {
      return {
        success: false,
        type: "code",
        message: "Invalid verification code provided",
      };
    } else if (errorType === "LimitExceededException") {
      return {
        success: false,
        type: "limit",
        message:
          "You have exceeded the limit for attempting to change your password. Please try again later.",
      };
    }
    return {
      success: false,
      type: "unknown",
      message: "An unknown error has occurred - please try again.",
    };
  }
};
