﻿using Newtonsoft.Json;

namespace Network.Auth
{
    public class AuthenticationResult
    {
        [JsonProperty("AccessToken")]
        public string AccessToken { get; set; }

        [JsonProperty("ExpiresIn")]
        public int ExpiresIn { get; set; }

        [JsonProperty("IdToken")]
        public string IdToken { get; set; }

        [JsonProperty("RefreshToken")]
        public string RefreshToken { get; set; }

        [JsonProperty("TokenType")]
        public string TokenType { get; set; }
    }
}
