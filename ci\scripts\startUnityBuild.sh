set -euo pipefail

if [ "$UNITY_BUILD_REQUIRED" != "true" ]; then
    echo "No Unity build required, fetching existing build artifacts from S3..."
    aws s3 cp s3://genex-golf-unity-build/${BUILD_HASH} ./UnityBuildOutput --recursive || {
    echo "Failed to fetch existing build artifacts from S3. Exiting..."
    exit 1
    }
    echo "Existing build artifacts fetched successfully."
    export BUILD_SUCCESSFUL=true
    exit 0
fi

echo "==> Run Unity build script"
echo "Waiting for Unity container to start..."
timeout=30
elapsed=0


while [ "$(docker inspect -f '{{.State.Running}}' unity-build 2>/dev/null)" != "true" ]; do
    if [ $elapsed -ge $timeout ]; then
        echo "Error: Timeout while waiting for Unity container to start."
        docker logs unity-build
        exit 1
    fi
    echo "Waiting... ($elapsed/$timeout seconds)"
    sleep 1
    elapsed=$((elapsed + 1))
done
echo "Container is running. Starting Unity build..."


echo "==> Configuring Git inside container for private Unity packages"
docker exec unity-build git config --global url."https://innovation-ai-aws-ci:${GH_TOKEN}@github.com/".insteadOf "https://github.com/"
echo "==> Git configured."
docker exec unity-build chmod +x workspace/ci/scripts/docker/buildUnity.sh
docker exec unity-build bash -c "workspace/ci/scripts/docker/buildUnity.sh -b $BUILD_TARGET"
echo "==> Build complete"

mkdir -p ./UnityBuildOutput

if [ -d "unity/Assets/StreamingAssets" ]; then
    mv unity/Assets/StreamingAssets ./UnityBuildOutput
fi

touch ./UnityBuildOutput/unity-build-${APP_VERSION}.txt ## Temp for testing

if [ "$BUILD_TARGET" = "android" ]; then
    echo "==> Verify Android build"
    ls ./unity/Build
    if ! ls ./unity/Build/*.apk 1> /dev/null 2>&1; then
        echo "Unable to find build artefacts, assuming build failed, exiting..."
        exit 1
    fi
    mv ./unity/Build/*.apk ./UnityBuildOutput
elif [ "$BUILD_TARGET" = "webgl" ]; then
    echo "==> Verify WebGL build"
    ls ./unity/Build/WebGL/Build

    if ! ls ./unity/Build/WebGL/Build/*.wasm.unityweb 1> /dev/null 2>&1; then
        echo "Unable to find build artefacts, assuming build failed, exiting..."
        exit 1
    fi
    mv ./unity/Build/WebGL/Build ./UnityBuildOutput
    mv ./unity/Build/WebGL/index.html ./UnityBuildOutput
    mv ./unity/Build/WebGL/TemplateData/ ./UnityBuildOutput 2>/dev/null
else
    echo "Unknown or unsupported build target: $BUILD_TARGET"
    exit 1
fi
echo "Build successful"
export BUILD_SUCCESSFUL=true

echo "==> Uploading build artifacts to S3"
aws s3 cp ./UnityBuildOutput s3://genex-golf-unity-build/${BUILD_HASH} --recursive