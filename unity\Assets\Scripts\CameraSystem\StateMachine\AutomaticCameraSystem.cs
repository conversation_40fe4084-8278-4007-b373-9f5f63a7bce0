using System;
using System.Collections.Generic;
using System.Linq;
using App.CameraSystem.StateMachine.States;
using App.CameraSystem.StateMachine.States.Base;
using App.CameraSystem.StateMachine.Types;
using Cinemachine;
using UnityEngine;

namespace App.CameraSystem.StateMachine
{
    public class AutomaticCameraSystem : MonoBehaviour, IStateChanger, ICameraSystem
    {
        [SerializeField] private CameraStateType initialState;
        [SerializeField] private Animator statesAnimator;
        [SerializeField] private CinemachineStateDrivenCamera cineMachineStateDrivenCamera;
        [SerializeField] private CinemachineBrain cinemachineBrain;
        
        private readonly Dictionary<CameraStateType,BaseCameraState> states = new();
        
        private CameraStateType currentState;

        public CinemachineBrain Brain => cinemachineBrain;
        public event Action<CameraStateType> StateChanged;

        private void Awake()
        {
            InitializeStates();
            EnterState(initialState);
        }

        public void ChangeState(CameraStateType nextState)
        {
            ExitState(currentState);
            EnterState(nextState);
        }

        public void ChangeState<T>(CameraStateType nextState, T parameter)
        {
            ExitState(currentState);
            
            var selectedState = states[nextState].GetComponent<ICameraStateWithPayload<T>>();
           
            if (selectedState == null)
            {
                Debug.LogError($"{nextState} does not implement the ICameraStateWithPayload interface in order to pass the payload" );
            }
            
            selectedState?.SetPayload(parameter);
            
            EnterState(nextState);
        }

        private void EnterState(CameraStateType state)
        {
            statesAnimator.SetTrigger(state.ToString());
            Debug.Log("Entering state: " + state);
            states[state].Enter();

            currentState = state;
            
            StateChanged?.Invoke(currentState);
        }
        
        private void ExitState(CameraStateType state)
        {
            statesAnimator.ResetTrigger(state.ToString());
            states[state].Exit();
        }

        private void InitializeStates()
        {
            var cameraStates = cineMachineStateDrivenCamera.ChildCameras.ToList()
                .Select(camera => camera.GetComponent<BaseCameraState>());

            foreach (var cameraState in cameraStates)
            {
                states.Add(cameraState.State,cameraState);
                cameraState.DoInitialize(this);
            }
        }
    }
}
