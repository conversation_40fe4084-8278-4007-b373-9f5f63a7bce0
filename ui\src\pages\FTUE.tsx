import { signIn, signUp, updateUserAttribute } from "aws-amplify/auth";
import { isProfane } from "no-profanity";
import { useRef, useState } from "react";
import {
  Header,
  PageDescriptor,
  TextInput,
  PasswordInput,
  Button,
} from "../components";
import { attemptSignIn } from "../services/auth";
import { RecordPageView } from "../services/rum";
import { getFormValue } from "../services/textUtils";

export function FTUE() {
  // disabling social sign in for now
  // const location = useLocation();
  // const params = new URLSearchParams(location.search);
  const usingSocial = false;

  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<{
    type?: string;
    message?: string;
  } | null>(null);

  const formRef = useRef<HTMLFormElement>(null);

  const parseErrorMessage = (err: any) => {
    const { name: errorType, message } = err as any;
    console.warn(errorType, err);
    if (
      errorType === "UsernameExistsException" ||
      errorType === "NotAuthorizedException"
    ) {
      setError({ type: "username", message: "Username already exists" });
      return false;
    } else if (message?.includes?.("ProfanityFoundException")) {
      setError({ type: "username", message: "Profanity found in username" });
      return false;
    } else if (message?.includes?.("Username cannot be of email format")) {
      // code for this is InvalidParameterException which isn't helpful enough to target to a parameter
      setError({
        type: "username",
        message: "Username cannot be of email format",
      });
      return false;
    } else if (errorType === "InvalidPasswordException") {
      setError({
        type: "password",
        message: "Password does not meet requirements",
      });
      return false;
    }
    return true;
  };

  const onSubmit = async () => {
    setLoading(true);

    formRef.current?.classList.add("validated");
    if (!formRef.current || !formRef.current!.checkValidity()) {
      setLoading(false);
      return;
    }

    const username = getFormValue(formRef, "Username");

    // handle social sign in assigning their username
    if (usingSocial) {
      // check if username is already taken
      try {
        // attempt sign in with an invalid password to check if username already exists
        // UserNotFoundException -> username not taken, we can set preferred_username for this account
        // NotAuthorizedException -> username already exists, we can't set preferred_username for this account
        await signIn({
          username,
          password: "invalidPassword",
        });
      } catch (err) {
        const nonBreaking = parseErrorMessage(err);
        if (!nonBreaking) {
          // if error is breaking, don't attempt to update preferred_username
          setLoading(false);
          return;
        }
      }

      try {
        const output = await updateUserAttribute({
          userAttribute: {
            attributeKey: "preferred_username",
            value: username,
          },
        });
        if (output.isUpdated) {
          window.location.href = "/";
        } else {
          setError({
            type: "username",
            message:
              "There was an error updating your username, please try again.",
          });
        }
      } catch (err) {
        parseErrorMessage(err);
      } finally {
        setLoading(false);
      }
      return;
    }

    const given_name = getFormValue(formRef, "First name");
    const family_name = getFormValue(formRef, "Last name");
    const email = getFormValue(formRef, "Email");
    const password = getFormValue(formRef, "Password");
    try {
      if (isProfane(username)) {
        throw new Error("ProfanityFoundException: Username contains profanity");
      }
      await signUp({
        username,
        password,
        options: {
          userAttributes: {
            email,
            given_name,
            family_name,
            username,
            preferred_username: username,
          },
        },
      });
      await attemptSignIn(username, password);
    } catch (err) {
      parseErrorMessage(err);
    } finally {
      setLoading(false);
    }
  };

  RecordPageView("FTUE");
  return (
    <div className="h-full w-full flex flex-col">
      <Header />
      <div className="flex flex-col gap-6 px-4 py-6 overflow-y-auto flex-grow">
        <PageDescriptor
          heading="Let's get started"
          description={`Create a display name ${!usingSocial ? "and password" : ""}`}
        />
        <form
          className="flex flex-col gap-5 flex-grow"
          noValidate
          ref={formRef}
        >
          {!usingSocial && (
            <>
              <TextInput label="First name" required pattern=".*\S+.*" />
              <TextInput label="Last name" required pattern=".*\S+.*" />
              <TextInput label="Email" required type="email" />
            </>
          )}
          <TextInput
            label="Username"
            required
            pattern=".*\S+.*"
            error={error?.type === "username" ? error.message : undefined}
            onChange={() => setError(null)}
          />
          {!usingSocial && (
            <PasswordInput
              validate
              error={error?.type === "password" ? error.message : undefined}
            />
          )}

          <div className="text-white text-sm text-balance text-center break-words mt-auto">
            By entering your email address and clicking "Start playing", you
            agree to receive marketing and sales emails from Comcast Business
            and PGA TOUR. You agree to Comcast’s and PGA TOUR’s respective
            privacy policies, available at:{" "}
            <a
              className="font-bold"
              href="https://www.xfinity.com/privacy/policy"
            >
              https://www.xfinity.com/privacy/policy
            </a>{" "}
            and{" "}
            <a
              className="font-bold"
              href="https://www.pgatour.com/company/privacy-policy"
            >
              https://www.pgatour.com/company/privacy-policy
            </a>
            .
          </div>
          <Button onClick={onSubmit} label="Start playing" disabled={loading} />
        </form>
      </div>
    </div>
  );
}
