using App.CameraSystem.StateMachine.Data;
using App.CameraSystem.StateMachine.States.Base;
using BallManager.Ball;
using Cinemachine;
using Configuration;
using UnityEngine;

namespace App.CameraSystem.StateMachine.States
{
    public class BallLandedCamera : BaseCameraState, ICameraStateWithPayload<TrackableTargets>
    {
        [SerializeField] private CinemachineTargetGroup targetGroup;
        [SerializeField] private float targetGroupRadius = 0.5f;
        [SerializeField] private float targetGroupWeight = 1f;
        [SerializeField] private Transform predictionTargetPoint;

        private Vector3 offset;

        private IGolfBall ball;
        private Vector3 predictionLocation;
        private CinemachineTransposer cameraTransposer;

        protected override void Initialize()
        {
            base.Initialize();

            cameraTransposer = StateCamera.GetComponent<CinemachineTransposer>();
        }

        public void SetPayload(TrackableTargets targets)
        {
            if (targets.Ball == null)
            {
                Debug.LogError("BallLandedCamera: Ball is null in the provided TrackableTargets.");
                return;
            }
            if (targets.PredictionLocation == null)
            {
                Debug.LogWarning("BallLandedCamera: PredictionLocation is null in the provided TrackableTargets and will not be included in camera framing.");
            }

            ball = targets.Ball;
            predictionLocation = targets.PredictionLocation ?? Vector3.zero;
            predictionTargetPoint.position = predictionLocation;
        }

        public override void Enter()
        {
            offset = ConfigurationManager.Configuration.ConfigurableValues.CameraOffsets.CameraLandedOffset;
            SetCameraTarget();
        }

        public override void Exit()
        {
            targetGroup.m_Targets = new[] { new CinemachineTargetGroup.Target() };
        }

        private void SetCameraTarget()
        {
            StateCamera.LookAt = targetGroup.gameObject.transform;
            StateCamera.Follow = targetGroup.gameObject.transform;
        

            var targetBall = new CinemachineTargetGroup.Target
            {
                target = ball.Transform,
                weight = targetGroupWeight,
                radius = targetGroupRadius
            };
            
            if (Vector3.zero.Equals(predictionLocation))
            {
                targetGroup.m_Targets = new[] { targetBall };
            }
            else
            {
                var pinnedObject = new CinemachineTargetGroup.Target
                {
                    target = predictionTargetPoint,
                    weight = targetGroupWeight,
                    radius = targetGroupRadius
                };
                targetGroup.m_Targets = new[] { targetBall, pinnedObject };
            }

            if (cameraTransposer != null)
            {
                cameraTransposer.m_FollowOffset = offset;
            }
        }
    }
}