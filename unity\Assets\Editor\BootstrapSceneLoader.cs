using System;
using Bootstrap;
using UnityEditor;
using UnityEditor.SceneManagement;
using UnityEngine;

namespace Editor
{
    /// <summary>
    /// Automatically loads the Bootstrap scene when entering Play mode,
    /// and restores the original scene when exiting Play mode.
    /// </summary>
    [InitializeOnLoad]
    public static class BootstrapSceneLoader
    {
        static BootstrapSceneLoader()
        {
            // Don't initialize during batch mode (CI/CD builds)
            if (Application.isBatchMode)
                return;
                
            EditorApplication.playModeStateChanged += OnPlayModeStateChanged;
        }

        private static void OnPlayModeStateChanged(PlayModeStateChange state)
        {
            switch (state)
            {
                case PlayModeStateChange.ExitingEditMode:
                    HandleExitingEditMode();
                    break;
                case PlayModeStateChange.ExitingPlayMode:
                    HandleExitingPlayMode();
                    break;
            }
        }

        private static void HandleExitingEditMode()
        {
            // Don't run during batch mode (CI/CD builds)
            if (Application.isBatchMode)
                return;
                
            if (!EditorPrefs.GetBool(BootstrapKeys.BOOTSTRAP_ENABLED_KEY, true))
                return;

            var currentScene = EditorSceneManager.GetActiveScene();
            if (currentScene.path == BootstrapKeys.BOOTSTRAP_SCENE_PATH) return;
            EditorPrefs.SetString(BootstrapKeys.PREVIOUS_SCENE_KEY, currentScene.path);

            if (currentScene.isDirty)
            {
                bool autoSave = EditorPrefs.GetBool(BootstrapKeys.AUTO_SAVE_KEY, true);
                if (autoSave)
                {
                    EditorSceneManager.SaveScene(currentScene);
                }
                else
                {
                    var saveScene = EditorUtility.DisplayDialog(
                        "Unsaved Changes",
                        $"Scene '{currentScene.name}' has unsaved changes. Do you want to save before switching to Bootstrap?",
                        "Save", "Don't Save");

                    if (saveScene)
                    {
                        EditorSceneManager.SaveScene(currentScene);
                    }
                }
            }

            try
            {
                EditorSceneManager.OpenScene(BootstrapKeys.BOOTSTRAP_SCENE_PATH);
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to load Bootstrap scene: {e.Message}");
                EditorPrefs.DeleteKey(BootstrapKeys.PREVIOUS_SCENE_KEY);
            }
        }


        private static void HandleExitingPlayMode()
        {
            if (!EditorPrefs.GetBool(BootstrapKeys.BOOTSTRAP_ENABLED_KEY, true))
                return;

            // Restore the previous scene when exiting Play mode
            string previousScenePath = EditorPrefs.GetString(BootstrapKeys.PREVIOUS_SCENE_KEY, "");

            if (!string.IsNullOrEmpty(previousScenePath) && previousScenePath != BootstrapKeys.BOOTSTRAP_SCENE_PATH)
            {
                // Use a more robust approach to ensure we're in Edit mode before restoring the scene
                RestoreSceneWhenInEditMode(previousScenePath);
            }
        }

        private static void RestoreSceneWhenInEditMode(string scenePath)
        {
            // Use EditorApplication.delayCall with proper Edit mode checking
            EditorApplication.delayCall += () =>
            {
                // If still not in Edit mode, try again on the next frame
                if (EditorApplication.isPlaying || EditorApplication.isPlayingOrWillChangePlaymode)
                {
                    RestoreSceneWhenInEditMode(scenePath);
                    return;
                }

                try
                {
                    // Double-check the scene exists before trying to load it
                    if (System.IO.File.Exists(scenePath))
                    {
                        EditorSceneManager.OpenScene(scenePath);
                        Debug.Log($"Restored previous scene: {scenePath}");
                    }
                    else
                    {
                        Debug.LogWarning($"Previous scene file not found: {scenePath}");
                    }
                }
                catch (Exception e)
                {
                    Debug.LogError($"Failed to restore previous scene '{scenePath}': {e.Message}");
                }
                finally
                {
                    // Clean up the stored scene path
                    EditorPrefs.DeleteKey(BootstrapKeys.PREVIOUS_SCENE_KEY);
                }
            };
        }

        /// <summary>
        /// Menu item to manually load the Bootstrap scene
        /// </summary>
        [MenuItem("Tools/Bootstrap Scene Loader/Load Bootstrap Scene")]
        private static void LoadBootstrapScene()
        {
            if (EditorApplication.isPlaying)
            {
                EditorUtility.DisplayDialog("Cannot Load Scene", "Cannot load scene while in Play mode.", "OK");
                return;
            }

            try
            {
                EditorSceneManager.OpenScene(BootstrapKeys.BOOTSTRAP_SCENE_PATH);
                Debug.Log("Manually loaded Bootstrap scene");
            }
            catch (Exception e)
            {
                Debug.LogError($"Failed to manually load Bootstrap scene: {e.Message}");
                EditorUtility.DisplayDialog("Error", $"Failed to load Bootstrap scene: {e.Message}", "OK");
            }
        }

        /// <summary>
        /// Menu item to manually toggle the Bootstrap scene loader functionality
        /// </summary>
        [MenuItem("Tools/Bootstrap Scene Loader/Toggle Bootstrap Enabled")]
        private static void ToggleBootstrapLoader()
        {
            var isEnabled = EditorPrefs.GetBool(BootstrapKeys.BOOTSTRAP_ENABLED_KEY, true);
            EditorPrefs.SetBool(BootstrapKeys.BOOTSTRAP_ENABLED_KEY, !isEnabled);

            var status = !isEnabled ? "Enabled" : "Disabled";
            Debug.Log($"Bootstrap Scene Loader {status}");
        }

        /// <summary>
        /// Menu item to check if the Bootstrap scene loader is enabled and add the checkmark
        /// </summary>
        [MenuItem("Tools/Bootstrap Scene Loader/Toggle Bootstrap Enabled", true)]
        private static bool ToggleBootstrapLoaderValidate()
        {
            var isEnabled = EditorPrefs.GetBool(BootstrapKeys.BOOTSTRAP_ENABLED_KEY, true);
            Menu.SetChecked("Tools/Bootstrap Scene Loader/Toggle Bootstrap Enabled", isEnabled);
            return true;
        }

        // Add menu item to toggle auto-save
        [MenuItem("Tools/Bootstrap Scene Loader/Toggle Auto-Save")]
        private static void ToggleAutoSave()
        {
            var isEnabled = EditorPrefs.GetBool(BootstrapKeys.AUTO_SAVE_KEY, true);
            EditorPrefs.SetBool(BootstrapKeys.AUTO_SAVE_KEY, !isEnabled);

            var status = !isEnabled ? "Enabled" : "Disabled";
            Debug.Log($"Bootstrap Scene Loader Auto-Save {status}");
        }

        // Add menu validation for checkmark
        [MenuItem("Tools/Bootstrap Scene Loader/Toggle Auto-Save", true)]
        private static bool ToggleAutoSaveValidate()
        {
            var isEnabled = EditorPrefs.GetBool(BootstrapKeys.AUTO_SAVE_KEY, true);
            Menu.SetChecked("Tools/Bootstrap Scene Loader/Toggle Auto-Save", isEnabled);
            return true;
        }

    }
}