#!/bin/bash
set -euo pipefail

DIRS=("unity/Assets" "unity/Packages" "unity/ProjectSettings")
# ls Assets

for dir in "${DIRS[@]}"; do
    if [ ! -d "$dir" ]; then
        echo "Directory not found: $dir" >&2
        exit 1
    fi
done

# Ensure consistent locale for sorting
export LC_ALL=C

# Create a content-only hash from sorted file list and content
find "${DIRS[@]}" -type f ! -name '.DS_Store' -print0 \
  | sort -z \
  | while IFS= read -r -d '' file; do
      hash=$(cat "$file" | tr -d '\r' | shasum -a 1 | awk '{print $1}')
      echo "$hash $file"
    done \
  | shasum -a 1 \
  | awk '{ print $1 }'
