using System.Collections.Generic;
using DG.Tweening;
using Sky.GenEx.ArtToolKit;
using UnityEngine;
using UnityEngine.UI;

namespace Prediction
{
    public class PredictionDragAnimationHandler : MonoBehaviour
    {
        [SerializeField] private Transform predictionPinUI;
        [SerializeField] private List<CanvasGroupAnimator> hideOnDragObjectsNotPredictionSpecific;
        [SerializeField] private List<CanvasGroupAnimator> hideOnDragObjectsPredictionOnly;
        [SerializeField] private Image bottomBarBackground;

        private float predictionPinUiOriginYPosition;

        private List<CanvasGroupAnimator> hideOnDragAllObjects;

        public void Start()
        {
            predictionPinUiOriginYPosition = predictionPinUI.position.y;
            hideOnDragAllObjects = new List<CanvasGroupAnimator>();
            hideOnDragAllObjects.AddRange(hideOnDragObjectsNotPredictionSpecific);
            hideOnDragAllObjects.AddRange(hideOnDragObjectsPredictionOnly);
        }

        public void StartDragging()
        {
            predictionPinUI.DOMoveY(predictionPinUiOriginYPosition + 10, 0.25f).SetEase(Ease.InOutCubic);
            foreach (var obj in hideOnDragAllObjects)
            {
                obj.Close();
            }
            bottomBarBackground.raycastTarget = false;
        }

        public void StopDragging(bool isPredictionStillOpen)
        {
            predictionPinUI.DOMoveY(predictionPinUiOriginYPosition, 0.25f).SetEase(Ease.InOutCubic);
            foreach (var obj in hideOnDragObjectsNotPredictionSpecific)
            {
                obj.Open();
            }
            if (isPredictionStillOpen)
            {
                foreach (var obj in hideOnDragObjectsPredictionOnly)
                {
                    obj.Open();
                }
            }
            bottomBarBackground.raycastTarget = true;
        }
    }
}
