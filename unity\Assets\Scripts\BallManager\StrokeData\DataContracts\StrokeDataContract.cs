using JetBrains.Annotations;
using Newtonsoft.Json;

namespace BallManager.StrokeData.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class StrokeDataContract
    {
        [JsonProperty("golferDetails")]
        public GolferDataContract GolferDetails;

        [JsonProperty("roundNumber")]
        public int RoundNumber;

        [JsonProperty("strokeNumber")]
        public int StrokeNumber;
        
        [JsonProperty("strokeId")]
        public string StrokeId;

        [JsonProperty("holeDetails")]
        public HoleDetailsDataContract HoleDetails;

        [JsonProperty("from")]
        public CoordinateLocationDataContract From;

        [JsonProperty("to")]
        public CoordinateLocationDataContract To;

        [JsonProperty("trail")] 
        [CanBeNull]
        public TrajectoryDataContract Trajectory;

        public bool IsValid() {
            if (From == null || 
                To == null || 
                HoleDetails == null || 
                HoleDetails.IsValid() == false ||
                GolferDetails == null || 
                Trajectory == null ||
                Trajectory.IsValid() == false)
            {
                return false;
            }
            return true;
        }
    }
}
