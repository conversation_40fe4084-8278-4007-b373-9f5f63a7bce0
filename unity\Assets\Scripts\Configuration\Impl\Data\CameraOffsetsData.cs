using Configuration.Impl.DataContract;
using UnityEngine;

namespace Configuration.Impl.Data
{
    public class CameraOffsetsData
    {
        public Vector3 CameraStartingOffset { get; }
        public Vector3 CameraAirborneOffset { get; }
        public Vector3 CameraLandedOffset { get; }
        public Vector3 BettingCameraOffset { get; }

        public CameraOffsetsData(CameraOffsetsDataContract dataContract)
        {
            CameraStartingOffset = new Vector3(dataContract.CameraStartingOffset.X, dataContract.CameraStartingOffset.Y, dataContract.CameraStartingOffset.Z);
            CameraAirborneOffset = new Vector3(dataContract.CameraAirborneOffset.X, dataContract.CameraAirborneOffset.Y, dataContract.CameraAirborneOffset.Z);
            CameraLandedOffset = new Vector3(dataContract.CameraLandedOffset.X, dataContract.CameraLandedOffset.Y, dataContract.CameraLandedOffset.Z);
            BettingCameraOffset = new Vector3(dataContract.BettingCameraOffset.X, dataContract.BettingCameraOffset.Y, dataContract.BettingCameraOffset.Z);
        }
    }
}
