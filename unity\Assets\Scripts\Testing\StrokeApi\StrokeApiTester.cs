using System;
using Network.Services;
using Testing.PredictionWindow;
using UnityEngine;

namespace Testing.StrokeApi
{
    public class StrokeApiTester : MonoBehaviour
    {
        [Space(15)] 
        [Header("Stroke Data Options")]
        [SerializeField] private bool strokeData;
        [Space(5)] 
        [SerializeField] private StrokeTestParams strokeTestParams;
        

        private void Update()
        {
            if (strokeData)
            {
                strokeData = false;
                SendStroke();
            }
        }
        
        public void SendStroke()
        {
            StrokeService.SimulateStrokeReceived(StrokeApiTesterData.GetStrokeData(strokeTestParams).Data);
        }
        
        [Serializable]
        public class StrokeTestParams
        {
            [Header("Stroke")]
            [SerializeField] private int groupNumber = 10;
            [SerializeField] private int roundNumber = 30;
            
            [Header("Golfer")]
            [SerializeField] private int golferId = 20229;
            [SerializeField] private string golferName = "Brad";
            [SerializeField] private string country = "ENG";
            
            public int GroupNumber => groupNumber;
            public int RoundNumber => roundNumber;
            public int GolferId => golferId;
            public string GolferName => golferName;
            public string Country => country;
        }
    }
}