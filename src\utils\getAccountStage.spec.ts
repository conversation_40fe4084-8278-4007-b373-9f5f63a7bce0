import { getAccountStage } from "./getAccountStage";
import context from "../../cdk.context.json";

jest.mock("../../cdk.context.json", () => ({
  config: {
    main: {
      Account: "************", // Mocked account id
    },
  },
}));

describe("getAccountStage", () => {
  it("should return the provided stage if it is valid", () => {
    expect(getAccountStage("any-account-id", "main")).toBe("main");
    expect(getAccountStage("any-account-id", "dev")).toBe("dev");
    expect(getAccountStage("any-account-id", "staging")).toBe("staging");
    expect(getAccountStage("any-account-id", "prod")).toBe("prod");
  });

  it("should return 'main' if the account ID matches the main account in context", () => {
    const mainAccountId = context.config.main.Account;
    expect(getAccountStage(mainAccountId, "other-stage")).toBe("main");
  });

  it("should return 'dev' if the stage is not in the config and the account ID does not match the build account", () => {
    expect(getAccountStage("non-main-account-id", "invalid-stage")).toBe("dev");
  });
});
