﻿using UnityEngine.Networking;

namespace Network.Response
{
    public struct RequestResult
    {
        public bool Success { get; }
        public string Response { get; }
        public UnityWebRequest Request { get; }

        public RequestResult(bool success, string response, UnityWebRequest request)
        {
            Success = success;
            Response = response;
            Request = request;
        }
    }
}