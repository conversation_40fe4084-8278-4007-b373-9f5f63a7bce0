{"confirm": ["Confirm that your GitHub project has an Admin User connected to your AWS Account, e.g. innovation-ai-aws-ci", "If you need your CI build to trigger a CD pipeline, you have already deployed that CD pipeline and know the bucket name", "If you want to use the Confluence Release Notes Writer, that stack has already been deployed to this account - see https://github.com/sky-uk/releaseNotesWriter"], "ProjectName": "golf-app", "BuildLogRetention": "THREE_MONTHS", "IncludeEnvVars": "true", "EnvVars": "{\"REGION\":\"eu-west-1\",\"ACCOUNT_ID\":\"************\"}\n", "GitRepoOwner": "sky-uk", "GitRepoName": "btv-genex-golf-app", "GitCloneDepth": "1", "DeployBuildSpecPath": "ci/buildspec.yml", "TeardownBuildSpecPath": "ci/teardown.buildspec.yml", "BuildComputeType": "X_LARGE", "Image": "aws/codebuild/standard:7.0", "BuildTimeout": "90", "ParameterARNs": "arn:aws:ssm:eu-west-1:************:parameter/pipeline/artifactoryToken", "WriteParameterARNs": "", "SecretARNs": "arn:aws:secretsmanager:eu-west-1:************:secret:/pipeline/unity-licence-lrMt7M", "RoleARNs": "", "PrivilegedMode": "false", "CDKMode": "true", "CDKEnhancedMode": "true", "DeviceFarmProjectArn": "", "ConfluenceWriter": "false", "EnableBatchBuild": "false", "EnableMergeQueue": "false", "BuildSchedule": "", "SplitCIMainAccount": "", "FailureSnsTopic": "", "SendFailureSlackNotifications": "false", "Promote": true, "S3OutputBucket": "golf-app-cd-serverless-cdsourcesourcebucket9b42b84-q9gptwdbiwzh", "S3OutputArtifactName": "cd-release.zip", "CrossAccountRole": "arn:aws:iam::************:role/golf-app-cd-serverless-cdsourcecrossaccountrole68B6-hM8FKdy3J7vd", "TARGET_STACK_ID": "ci", "TAGS": "[\n  {\n    \"name\": \"project\",\n    \"value\": \"golf-app\"\n  },\n  {\n    \"name\": \"territory\",\n    \"value\": \"uk\"\n  },\n  {\n    \"name\": \"billing_team\",\n    \"value\": \"gpds\"\n  },\n  {\n    \"name\": \"service\",\n    \"value\": \"golf-app\"\n  }\n]\n"}