using BallManager.StrokeData.DataContracts;
using BallManager.StrokeData.Utils;
using UnityEngine;

namespace BallManager.StrokeData.Data
{
    public class RadarData
    {
        public bool IsValid => Points != null;
        public Vector3[] Points { get; }
        public Vector3 FinalSpeed { get; }
        
        private TrajectoryData Trajectory { get; }

        public RadarData(StrokeDataContract stroke, HoleDetailsData holeDetailsData)
        {
            Trajectory = SetTrajectory(stroke.Trajectory);
            Points = SetPoints(Trajectory, holeDetailsData);
            FinalSpeed = SetRadarSpeed(Points);
        }

        private static TrajectoryData SetTrajectory(TrajectoryDataContract trail)
        {
            return trail != null ? new TrajectoryData(trail) : null;
        }
        
        private static Vector3[] SetPoints(TrajectoryData trajectory, HoleDetailsData holeDetailsData)
        {
            if (trajectory == null)
            {
                return null;
            }
            
            return TrajectoryUtilities.CreateTrajectoryPoints(trajectory, holeDetailsData);
        }
        
        private static Vector3 SetRadarSpeed(Vector3[] radarPoints)
        {
            if (radarPoints == null)
            {
                return Vector3.zero;
            }

            return TrajectoryUtilities.CalculateFinalRadarSpeed(radarPoints);
        }
    }
}
