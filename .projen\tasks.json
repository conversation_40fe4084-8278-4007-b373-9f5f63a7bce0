{"tasks": {"build": {"name": "build", "description": "Full release build", "steps": [{"spawn": "default"}, {"spawn": "pre-compile"}, {"spawn": "compile"}, {"spawn": "post-compile"}, {"spawn": "test"}, {"spawn": "package"}]}, "bundle": {"name": "bundle", "description": "Prepare assets"}, "clobber": {"name": "clobber", "description": "hard resets to HEAD of origin and cleans the local repo", "env": {"BRANCH": "$(git branch --show-current)"}, "steps": [{"exec": "git checkout -b scratch", "name": "save current HEAD in \"scratch\" branch"}, {"exec": "git checkout $BRANCH"}, {"exec": "git fetch origin", "name": "fetch latest changes from origin"}, {"exec": "git reset --hard origin/$BRANCH", "name": "hard reset to origin commit"}, {"exec": "git clean -fdx", "name": "clean all untracked files"}, {"say": "ready to rock! (unpushed commits are under the \"scratch\" branch)"}], "condition": "git diff --exit-code > /dev/null"}, "compile": {"name": "compile", "description": "Only compile"}, "default": {"name": "default", "description": "Synthesize project files", "steps": [{"exec": "bun --tsconfig-override tsconfig.dev.json .projenrc.ts"}]}, "deploy": {"name": "deploy", "description": "Deploys your CDK app to the AWS cloud", "steps": [{"exec": "cdk deploy", "receiveArgs": true}]}, "deploy:all": {"name": "deploy:all", "description": "Deploy all stacks using deploy.sh", "steps": [{"exec": "sh ./ci/scripts/deploy.sh", "receiveArgs": true}]}, "destroy": {"name": "destroy", "description": "Destroys your cdk app in the AWS cloud", "steps": [{"exec": "cdk destroy", "receiveArgs": true}]}, "diff": {"name": "diff", "description": "Diffs the currently deployed app against your code", "steps": [{"exec": "cdk diff"}]}, "eject": {"name": "eject", "description": "<PERSON><PERSON><PERSON> projen from the project", "env": {"PROJEN_EJECTING": "true"}, "steps": [{"spawn": "default"}]}, "eslint": {"name": "eslint", "description": "Runs eslint against the codebase", "env": {"ESLINT_USE_FLAT_CONFIG": "false"}, "steps": [{"exec": "eslint --ext .ts,.tsx --fix --no-error-on-unmatched-pattern $@ src build-tools projenrc .projenrc.ts", "receiveArgs": true}]}, "install": {"name": "install", "description": "Install project dependencies and update lockfile (non-frozen)", "steps": [{"exec": "bun install"}]}, "install:ci": {"name": "install:ci", "description": "Install project dependencies using frozen lockfile", "steps": [{"exec": "bun install --frozen-lockfile"}]}, "integ": {"name": "integ", "description": "Run integration snapshot tests", "steps": [{"exec": "integ-runner $@ --language typescript", "receiveArgs": true}]}, "integ:update": {"name": "integ:update", "description": "Run and update integration snapshot tests", "steps": [{"exec": "integ-runner $@ --language typescript --update-on-failed", "receiveArgs": true}]}, "package": {"name": "package", "description": "Creates the distribution package"}, "post-compile": {"name": "post-compile", "description": "Runs after successful compilation", "steps": [{"spawn": "synth:silent"}]}, "post-upgrade": {"name": "post-upgrade", "description": "Runs after upgrading dependencies"}, "pre-compile": {"name": "pre-compile", "description": "Prepare the project for compilation"}, "synth": {"name": "synth", "description": "Synthesizes your cdk app into cdk.out", "steps": [{"exec": "cdk synth"}]}, "synth:silent": {"name": "synth:silent", "description": "Synthesizes your cdk app into cdk.out and suppresses the template in stdout (part of \"yarn build\")", "steps": [{"exec": "cdk synth -q"}]}, "test": {"name": "test", "description": "Run tests", "steps": [{"exec": "jest --passWithNoTests --updateSnapshot", "receiveArgs": true}, {"spawn": "eslint"}, {"spawn": "integ"}]}, "test:watch": {"name": "test:watch", "description": "Run jest in watch mode", "steps": [{"exec": "jest --watch"}]}, "upgrade": {"name": "upgrade", "description": "upgrade dependencies", "env": {"CI": "0"}, "steps": [{"exec": "bunx npm-check-updates@16 --upgrade --target=minor --peer --no-deprecated --dep=dev,peer,prod,optional --filter=@aws-crypto/sha256-js,@aws-sdk/credential-provider-node,@aws-solutions-constructs/aws-cloudfront-s3,@aws-solutions-constructs/aws-wafwebacl-cloudfront,@comcast/gee-aws-cdk-shared,@smithy/protocol-http,@smithy/signature-v4,@types/async-retry,@types/aws-lambda,@types/jest,aws-sdk-client-mock,aws-sdk-client-mock-jest,cdk-iam-actions,esbuild,eslint-import-resolver-typescript,eslint-plugin-import,eslint-plugin-prettier,husky,jest,jest-extended,json-schema-to-ts,lint-staged,prettier,ts-jest,ts-node,tsconfig-paths,typescript,uuid,@aws-lambda-powertools/parameters,@aws-sdk/client-cognito-identity-provider,@aws-sdk/client-dynamodb,@middy/core,@middy/validator,@sky-uk/coins-o11y-utils,@sky-uk/coins-utilities,ajv,async-retry,aws-lambda,fakefilter,profane-words"}, {"exec": "bun install"}, {"exec": "bun update @aws-cdk/aws-cognito-identitypool-alpha @aws-crypto/sha256-js @aws-sdk/credential-provider-node @aws-solutions-constructs/aws-cloudfront-s3 @aws-solutions-constructs/aws-wafwebacl-cloudfront @comcast/gee-aws-cdk-shared @smithy/protocol-http @smithy/signature-v4 @types/async-retry @types/aws-lambda @types/jest @types/node @typescript-eslint/eslint-plugin @typescript-eslint/parser aws-cdk aws-sdk-client-mock aws-sdk-client-mock-jest cdk-iam-actions esbuild eslint-config-prettier eslint-import-resolver-typescript eslint-plugin-import eslint-plugin-prettier eslint husky jest jest-extended jest-junit json-schema-to-ts lint-staged prettier projen ts-jest ts-node tsconfig-paths typescript uuid @aws-cdk/integ-runner @aws-cdk/integ-tests-alpha @aws-lambda-powertools/logger @aws-lambda-powertools/parameters @aws-sdk/client-cognito-identity-provider @aws-sdk/client-dynamodb @middy/core @middy/validator @sky-uk/coins-o11y-utils @sky-uk/coins-utilities ajv async-retry aws-cdk-lib aws-lambda constructs fakefilter profane-words"}, {"exec": "bunx projen"}, {"spawn": "post-upgrade"}]}, "watch": {"name": "watch", "description": "Watches changes in your source code and rebuilds and deploys to the current account", "steps": [{"exec": "cdk deploy --hotswap"}, {"exec": "cdk watch"}]}}, "env": {"PATH": "$(bun --eval \"console.log(process.env.PATH)\")"}, "//": "~~ Generated by projen. To modify, edit .projenrc.ts and run \"bunx projen\"."}