import path from "path";
import { BaseNodejsFunction } from "@comcast/gee-aws-cdk-shared";
import { Duration } from "aws-cdk-lib";
import { Construct } from "constructs";

type PostConfirmFunctionProps = Readonly<{
  leaderboardApiUrl: string;
  leaderboardApiId: string;
  golfApiUrl: string;
  golfApiId: string;
  householdTableName: string;
  maxHouseholdSize: string;
  appVariant: string;
}>;

export class PostConfirmFunction extends BaseNodejsFunction {
  constructor(scope: Construct, id: string, props: PostConfirmFunctionProps) {
    super(scope, id, {
      entry: path.join(__dirname, "function/index.ts"),
      timeout: Duration.seconds(15),
      environment: {
        LEADERBOARD_API_URL: props.leaderboardApiUrl,
        LEADERBOARD_API_ID: props.leaderboardApiId,
        GOLF_API_URL: props.golfApiUrl,
        GOLF_API_ID: props.golfApiId,
        HOUSEHOLD_TABLE_NAME: props.householdTableName,
        MAX_HOUSEHOLD_SIZE: props.maxHouseholdSize,
        APP_VARIANT: props.appVariant,
      },
    });
  }
}
