using Newtonsoft.Json;

namespace Configuration.Impl.DataContract
{
    [JsonObject(MemberSerialization.OptIn)]
    public class ConfigurableValuesDataContract
    {
        [JsonProperty("refresh_token_validity_days")]
        public int RefreshTokenValidityDays { get; private set; }

        [JsonProperty("live_interval")]
        public float LiveInterval { get; private set; }

        [JsonProperty("leaderboard_interval")]
        public float LeaderboardInterval { get; private set; }

        [JsonProperty("course_map_height")]
        public float CourseMapHeight { get; private set; }

        [JsonProperty("betting_area_height")]
        public float BettingAreaHeight { get; private set; }

        [JsonProperty("camera_offsets")]
        public CameraOffsetsDataContract CameraOffsets { get; private set; }
    }
}
