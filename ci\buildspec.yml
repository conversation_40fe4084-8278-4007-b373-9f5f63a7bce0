version: 0.2

env:
  shell: bash
  git-credential-helper: yes
  parameter-store:
    GH_TOKEN: /pipeline/githubAccessToken
    ARTIFACTORY_TOKEN: /pipeline/artifactoryToken
  secrets-manager:
    UNITY_SERIAL: "/pipeline/unity-licence:UNITY_SERIAL"
    UNITY_USERNAME: "/pipeline/unity-licence:UNITY_USERNAME"
    UNITY_PASSWORD: "/pipeline/unity-licence:UNITY_PASSWORD"
  exported-variables:
    - APP_VERSION
    - STAGE
    - BUILD_TARGET

cache:
  paths:
    - 'unity/Library/**/*'

phases:
  install:
    runtime-versions:
      nodejs: 22.x
    commands:
      - echo "Configure Sky NPM access."
      - echo "@sky-uk:registry=https://npm.pkg.github.com/" > .npmrc
      - echo "//npm.pkg.github.com/:_authToken=$GH_TOKEN" >> .npmrc
      - echo "@comcast:registry=https://partners.artifactory.comcast.com/artifactory/api/npm/shared-js-libs/" >> .npmrc
      - echo "//partners.artifactory.comcast.com/artifactory/api/npm/shared-js-libs/:_authToken=$ARTIFACTORY_TOKEN" >> .npmrc

      - |
        export BRANCH="$(git symbolic-ref HEAD --short 2>/dev/null)"
        if [ "$BRANCH" = "" ] ; then
          export BRANCH="$(git rev-parse HEAD | xargs git name-rev | cut -d' ' -f2 | sed 's/remotes\/origin\///g')";
        fi
      - echo "Branch is ${BRANCH}"
      - export STAGE=$([ "$BRANCH" = "main" ] && echo "main" || echo $BRANCH | awk '{print tolower($1)}' | sha1sum | sed 's/[^a-zA-Z]//g' | cut -c1-7)
      - echo "Stage is ${STAGE}"
      - export TRUNK_BRANCH=main
      - echo "Trunk Branch is ${TRUNK_BRANCH}"

      - npm install -g bun
      - bun -v
      - bun install --frozen-lockfile
      - (cd ui && bun install --frozen-lockfile)
  pre_build:
    commands:
      - bun run eslint
      - bunx prettier -c .
      - bunx jest --passWithNoTests
      - (cd ui && bun run eslint)
      - (cd ui && bunx prettier -c .)
      - |
        echo "Calculating build hash..."
        export BUILD_HASH=$(./ci/scripts/generateBuildHash.sh)
        echo "Build hash: $BUILD_HASH"
      - |
        export UNITY_BUILD_REQUIRED=true
        if aws s3 ls "s3://genex-golf-unity-build/$BUILD_HASH" > /dev/null 2>&1; then
          echo "Build already exists. Skipping build."
          export UNITY_BUILD_REQUIRED=false
        fi
      # Download Unity Library (build cache) to speed up builds
      # - |
      #   if [ "$UNITY_BUILD_REQUIRED" = "true" ]; then
      #     echo "Downloading Unity Library from S3..."
      #     aws s3 sync "s3://genex-golf-unity-build/build-cache/" ./unity/Library --quiet
      #   fi
      - export BUILD_TARGET=webgl
      
  build:
    commands:
      - "export APP_VERSION=$(awk '/^[[:space:]]*bundleVersion:/ { print $2 }' ./unity/ProjectSettings/ProjectSettings.asset)"
      ## Extract unity version from the project manifest
      - |
        echo "Extracting Unity version from the project..."
        export UNITY_VERSION=$(grep -oP 'm_EditorVersion: \K.*' unity/ProjectSettings/ProjectVersion.txt)
        if [ -z "$UNITY_VERSION" ]; then
          echo "Error: Unity version not found in unity/ProjectSettings/ProjectVersion.txt"
          exit 1
        fi
        echo "Detected Unity version: $UNITY_VERSION"
      ## Get image with Unity pre-installed
      - aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com
      - ./ci/scripts/installUnityContainer.sh
      - ./ci/scripts/startUnityBuild.sh
      - bun ./ci/scripts/generateCspHash.cjs
      - bun deploy:all --context stage=${STAGE}
  post_build:
    commands:
      # promote main to prod
      - |
        if [ "$BRANCH" = "$TRUNK_BRANCH" ] && [ "$CODEBUILD_BUILD_SUCCEEDING" -eq 1 ]; then
          echo "Branch is ${STAGE} and build succeeded. Copying release to staging."
          npx @sky-uk/automation-pipeline@^1.0.8 promote -d "$CODEBUILD_SRC_DIR" -b "$ARTIFACT_BUCKET" -k "$ARTIFACT_KEY" -r "$PROD_ROLE"
        else
          echo "Not releasing because branch ${STAGE} is not main or build failed."
        fi
