import { useRef, useState } from "react";
import {
  <PERSON><PERSON>,
  PageDescriptor,
  TextInput,
  PasswordInput,
  Button,
} from "../components";
import { sendResetPasswordCode, confirmNewPassword } from "../services/auth";
import { RecordPageView } from "../services/rum";
import { getFormValue } from "../services/textUtils";

export function Forgot() {
  const formRef = useRef<HTMLFormElement>(null);
  const [error, setError] = useState<{
    type?: string;
    message?: string;
  } | null>(null);
  const [loading, setLoading] = useState(false);
  const [codeSent, setCodeSent] = useState(false);

  const sendResetCode = async (email: string) => {
    const { success, ...errorMessage } = await sendResetPasswordCode(email);
    if (success) setCodeSent(true);
    else setError(errorMessage);
    setLoading(false);
  };

  const sendNewPassword = async (
    email: string,
    code: string,
    newPassword: string,
  ) => {
    const { success, ...errorMessage } = await confirmNewPassword(
      email,
      code,
      newPassword,
    );
    if (!success) setError(errorMessage);
    setLoading(false);
  };

  const onSubmit = async () => {
    setLoading(true);
    if (!formRef.current || !formRef.current!.checkValidity()) {
      formRef.current?.classList.add("validated");
      setLoading(false);
      return;
    }
    const email = getFormValue(formRef, "Email");
    if (!codeSent) {
      await sendResetCode(email);
    } else {
      const code = getFormValue(formRef, "Confirmation Code");
      const password = getFormValue(formRef, "Password");
      await sendNewPassword(email, code, password);
    }
  };

  RecordPageView("ForgotPassword");
  return (
    <div className="h-full w-full flex flex-col">
      <Header />
      <div className="flex flex-col gap-6 px-4 py-6 overflow-y-auto flex-grow">
        <PageDescriptor
          heading="Forgot password?"
          description="Enter the email address associated with your account"
        />
        <form
          className="flex flex-col flex-grow gap-5"
          noValidate
          ref={formRef}
        >
          <TextInput
            label="Email"
            required
            pattern=".*\S+.*"
            onChange={() => setError(null)}
            error={error?.type === "credentials" ? error.message : undefined}
            disabled={codeSent}
          />
          {codeSent && (
            <>
              <TextInput
                label="Confirmation Code"
                required
                onChange={() => setError(null)}
                error={error?.type === "code" ? error.message : undefined}
              />
              <PasswordInput validate />
            </>
          )}
          {error?.type === "limit" && (
            <div className="bg-red-500 text-white w-full rounded-md font-bold p-2">
              {error.message}
            </div>
          )}
          <Button
            onClick={onSubmit}
            label="Continue"
            className="mt-auto"
            disabled={loading}
          />
        </form>
      </div>
    </div>
  );
}
