using App.CameraSystem.StateMachine.Data;
using App.CameraSystem.StateMachine.States.Base;
using Cinemachine;
using Configuration;
using UnityEngine;

namespace App.CameraSystem.StateMachine.States
{
    public class PredictionCamera : BaseCameraState, ICameraStateWithPayload<PredictionPayload>
    {
        private Cinemachine3rdPersonFollow followCam;
        private float initialZoom;
        private float initialDistance;

        private float bettingGridHeight;

        protected override void Initialize()
        {
            followCam = StateCamera.GetCinemachineComponent<Cinemachine3rdPersonFollow>();
        }

        public void SetPayload(PredictionPayload predictionPayload)
        {
            //TODO: fix camera rotation to match the direction of the golf course hole
            // var direction = (new Vector2(predictionPayload.PinPosition.x, predictionPayload.PinPosition.z) - 
            //                         new Vector2(predictionPayload.TeePosition.x, predictionPayload.TeePosition.z)).normalized;
            //
            // predictionPayload.CameraFocus.rotation = Quaternion.LookRotation(new Vector3(direction.x, 0, direction.y), Vector3.up);
            
            StateCamera.Follow = predictionPayload.CameraFocus;
            StateCamera.LookAt = predictionPayload.CameraFocus;
            ResetZoom();
        }

        public override void Enter()
        {
            StateCamera.m_Lens.Orthographic = true;
        }

        public override void Exit()
        {
            StateCamera.m_Lens.Orthographic = false;
        }

        private void ResetZoom()
        {
            float armLength = ConfigurationManager.Configuration.ConfigurableValues.CameraOffsets.BettingCameraOffset.y;
            followCam.VerticalArmLength = armLength - 1;
            followCam.ShoulderOffset = new Vector3(0.01f,1,0);
            UpdateOrthographicZoom(armLength);
        }

        // Calculate the orthographic size that matches the given arm length for a perspective camera
        private void UpdateOrthographicZoom(float armLength)
        {
            StateCamera.m_Lens.OrthographicSize = armLength * Mathf.Tan(StateCamera.m_Lens.FieldOfView * 0.5f * Mathf.Deg2Rad);
        }
    }
}
