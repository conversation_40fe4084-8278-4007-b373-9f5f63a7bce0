﻿using Newtonsoft.Json;
using UnityEngine;

namespace Network.Auth
{
    public class TokenStorage
    {
        public void SaveAuthTokens(AuthTokens token)
        {
            var accessTokenJson = JsonConvert.SerializeObject(token.AccessToken);
            var refreshTokenJson = JsonConvert.SerializeObject(token.RefreshToken);
            PlayerPrefs.SetString("AccessToken", accessTokenJson);
            PlayerPrefs.SetString("RefreshToken", refreshTokenJson);
            PlayerPrefs.Save();
        }

        public AuthTokens LoadAuthTokens()
        {
            var accessTokenJson = PlayerPrefs.GetString("AccessToken", null);
            var refreshTokenJson = PlayerPrefs.GetString("RefreshToken", null);
            if (string.IsNullOrEmpty(accessTokenJson) || string.IsNullOrEmpty(refreshTokenJson))
                return null;

            var accessToken = JsonConvert.DeserializeObject<AuthToken>(accessTokenJson);
            var refreshToken = JsonConvert.DeserializeObject<AuthToken>(refreshTokenJson);

            if (accessToken == null || refreshToken == null)
                return null;

            return new AuthTokens(accessToken, refreshToken);
        }

        public void ClearAuthTokens()
        {
            Debug.Log("Clearing auth tokens from PlayerPrefs.");
            PlayerPrefs.DeleteKey("AccessToken");
            PlayerPrefs.DeleteKey("RefreshToken");
            PlayerPrefs.Save();
        }
    }
}