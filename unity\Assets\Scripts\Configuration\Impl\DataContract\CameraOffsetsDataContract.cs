using Newtonsoft.Json;

namespace Configuration.Impl.DataContract
{
    [JsonObject(MemberSerialization.OptIn)]
    public class CameraOffsetsDataContract
    {
        [JsonProperty("camera_starting_offset")]
        public Vector3DataContract CameraStartingOffset { get; private set; }

        [JsonProperty("camera_airborne_offset")]
        public Vector3DataContract CameraAirborneOffset { get; private set; }

        [JsonProperty("camera_landed_offset")]
        public Vector3DataContract CameraLandedOffset { get; private set; }

        [JsonProperty("betting_camera_offset")]
        public Vector3DataContract BettingCameraOffset { get; private set; }
    } 
}
