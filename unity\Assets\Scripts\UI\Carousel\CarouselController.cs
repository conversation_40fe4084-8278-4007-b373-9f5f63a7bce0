using System.Collections;
using UnityEngine;
using UnityEngine.EventSystems;

namespace UI.Carousel
{
    public class CarouselController : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>rag<PERSON><PERSON><PERSON>, IEndDragHandler
    {
        [Header("Carousel Settings")]
        [SerializeField] private RectTransform pagesContainer;
        [SerializeField] private Transform indicatorsContainer;
        [SerializeField] private float percentThreshold = 0.3f;
        [SerializeField] private float easing = 0.3f;
        [SerializeField] private bool loopToStart = true;

        private CarouselDot[] indicators;
        private float locationAnchorMinX;
        private float locationAnchorMaxX;
        private int pageCount;
        private int currentPage;

        private float PageWidth => pagesContainer.rect.width;

        public int CurrentPage => currentPage;
        public int PageCount => pageCount;

        private void Start()
        {
            currentPage = 0;
            InitializeCarousel();
            
            locationAnchorMinX = -currentPage;
            locationAnchorMaxX = -currentPage + 1;
            pagesContainer.anchorMin = new Vector2(locationAnchorMinX, pagesContainer.anchorMin.y);
            pagesContainer.anchorMax = new Vector2(locationAnchorMaxX, pagesContainer.anchorMax.y);
        }

        private void InitializeCarousel()
        {
            locationAnchorMinX = pagesContainer.anchorMin.x;
            locationAnchorMaxX = pagesContainer.anchorMax.x;
            pageCount = pagesContainer.childCount;

            indicators = indicatorsContainer.GetComponentsInChildren<CarouselDot>();
            foreach (var indicator in indicators)
            {
                indicator.SetActive(false);
            }

            if (indicators.Length > 0 && currentPage < indicators.Length)
            {
                indicators[currentPage].SetActive(true);
            }
        }

        public void GoToNextPage()
        {
            if (currentPage < pageCount - 1)
            {
                currentPage++;
            }
            else if (loopToStart)
            {
                currentPage = 0;
            }
            else
            {
                return;
            }

            UpdateCarouselPosition();
        }

        private void UpdateCarouselPosition()
        {
            locationAnchorMinX = -currentPage;
            locationAnchorMaxX = -currentPage + 1;
            
            StartCoroutine(SmoothIndicatorsChange(easing));
            StartCoroutine(SmoothMove(pagesContainer.anchorMin.x, pagesContainer.anchorMax.x, easing));
        }

        public void OnDrag(PointerEventData eventData)
        {
            var difference = (eventData.position.x - eventData.pressPosition.x) / PageWidth;
            pagesContainer.anchorMin = new Vector2(locationAnchorMinX - difference, pagesContainer.anchorMin.y);
            pagesContainer.anchorMax = new Vector2(locationAnchorMaxX - difference, pagesContainer.anchorMax.y);

            if (difference > 0 && currentPage < pageCount - 1)
            {
                indicators[currentPage].SetTransparency(1 - difference);
                indicators[currentPage + 1].SetTransparency(difference);
            }
            else if (difference < 0 && currentPage > 0)
            {
                indicators[currentPage].SetTransparency(1 + difference);
                indicators[currentPage - 1].SetTransparency(-difference);
            }
        }

        public void OnEndDrag(PointerEventData eventData)
        {
            var percentage = (eventData.position.x - eventData.pressPosition.x) / PageWidth;
            
            if (Mathf.Abs(percentage) >= percentThreshold)
            {
                if (percentage > 0 && currentPage < pageCount - 1)
                {
                    currentPage++;
                }
                else if (percentage < 0 && currentPage > 0)
                {
                    currentPage--;
                }

                locationAnchorMinX = -currentPage;
                locationAnchorMaxX = -currentPage + 1;
            }

            StartCoroutine(SmoothIndicatorsChange(easing));
            StartCoroutine(SmoothMove(pagesContainer.anchorMin.x, pagesContainer.anchorMax.x, easing));
        }

        private IEnumerator SmoothIndicatorsChange(float seconds)
        {
            var t = 0f;
            var startTransparencies = new float[indicators.Length];
            
            for (int i = 0; i < indicators.Length; i++)
            {
                startTransparencies[i] = indicators[i].CurrentTransparency;
            }

            while (t <= 1.0f)
            {
                t += Time.unscaledDeltaTime / seconds;

                for (int i = 0; i < indicators.Length; i++)
                {
                    float targetTransparency = (i == currentPage) ? 1f : 0f;
                    indicators[i].SetTransparency(Mathf.Lerp(startTransparencies[i], targetTransparency, t));
                }

                yield return null;
            }
        }

        private IEnumerator SmoothMove(float startAnchorMinX, float startAnchorMaxX, float seconds)
        {
            var t = 0f;
            while (t <= 1.0f)
            {
                t += Time.unscaledDeltaTime / seconds;
                pagesContainer.anchorMin = new Vector2(Mathf.Lerp(startAnchorMinX, locationAnchorMinX, t), pagesContainer.anchorMin.y);
                pagesContainer.anchorMax = new Vector2(Mathf.Lerp(startAnchorMaxX, locationAnchorMaxX, t), pagesContainer.anchorMax.y);
                yield return null;
            }
        }
    }
}
