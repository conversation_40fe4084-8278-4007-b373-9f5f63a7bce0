import { SVGProps } from "react";
export const PlayersIcon = (props: SVGProps<SVGSVGElement>) => {
  return (
    <svg
      viewBox="0 0 35 95"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <g id="Layer_1" clipPath="url(#clip0_3608_172631)">
        <path
          id="Vector"
          d="M28.5165 89.4111C27.69 89.7302 27.1161 89.9502 26.5123 90.1831C27.0776 91.8435 29.1717 92.292 29.2959 94.1249C27.0091 94.8322 24.1013 94.5217 21.245 93.2451C21.245 92.1152 21.1679 90.899 21.2621 89.7C21.4676 86.996 21.7803 84.2962 21.9944 81.5921C22.0757 80.5355 22.0072 79.4703 22.0072 78.211C18.273 81.5706 14.6801 84.8051 11.207 87.9275C9.75102 88.2812 8.95878 87.1987 7.78112 86.776C6.58205 87.8154 5.20312 88.7599 4.16678 89.9933C3.19039 91.1577 2.57373 92.6327 1.80718 93.961C0.295494 93.2623 -0.218393 92.486 0.0985043 90.9939C0.364013 89.7518 0.740863 88.5011 1.28044 87.3539C1.88426 86.0687 2.71933 84.8913 3.48588 83.6148C6.26943 82.9938 7.97382 80.7166 10.1536 79.1425C12.753 77.2665 15.2154 75.1877 17.8961 73.4454C18.9924 72.7338 19.1637 71.3796 20.517 70.8362C19.228 67.5844 18.4186 64.2162 16.6328 61.2706C15.7121 59.7525 15.1682 58.1223 14.9755 56.4404C14.8428 55.2889 15.3096 54.0684 15.6008 52.3131C14.436 50.299 13.0185 47.8235 11.5753 45.361C11.07 44.4984 10.4576 43.6963 9.96086 42.8294C8.61618 40.479 8.30357 38.1027 10.0379 35.7048C9.22428 34.9932 8.49199 34.3463 7.75542 33.6994C6.10242 32.2417 5.77268 30.5597 6.89894 28.2395C7.69119 26.605 9.95657 25.682 11.8665 26.0659C12.3504 26.1651 12.8086 26.385 13.3696 26.5791C14.3203 28.2697 15.2967 30.0034 16.2003 31.612C17.6092 31.1549 18.9368 30.6589 20.2943 30.2923C21.5619 29.9516 22.6581 29.3996 23.6688 28.5414C24.5852 27.7608 25.6258 27.1182 26.6451 26.467C28.4779 25.3025 29.8183 23.806 30.041 21.5418C30.1095 20.8475 30.1181 20.1273 30.0025 19.4415C29.7883 18.1779 29.4629 16.9315 29.116 15.366C22.1143 10.8981 14.9284 6.3007 7.62267 1.62571C6.87753 2.72545 6.20948 3.70875 5.57569 4.64893C4.7135 4.64893 3.96693 4.32978 3.33599 3.6915C3.49444 2.28556 4.60358 1.56102 5.44293 0.750228C6.75763 0.13351 7.75542 0.758854 8.73181 1.35832C12.9928 3.95889 17.2452 6.57671 21.5019 9.1859C24.0542 10.7514 26.6065 12.3169 29.056 13.8178C31.7968 13.7876 33.681 14.9218 35.0043 17.4491C34.0193 20.1618 33.0258 22.9866 31.9638 25.7812C31.0987 28.0584 30.1523 30.301 29.2359 32.5565C29.1288 32.8239 29.0175 33.0913 28.8719 33.3371C27.2103 36.0929 25.6002 38.879 23.8444 41.5744C23.0821 42.7432 22.8423 43.7566 23.1763 45.1712C24.4439 50.5233 25.6258 55.9013 26.6536 61.3051C27.1846 64.1041 27.2489 66.9893 27.6643 69.8141C28.3495 74.4632 27.9084 79.1339 28.1054 83.7873C28.1867 85.6375 28.3709 87.4876 28.5165 89.4154V89.4111ZM19.9217 34.3118C20.4142 35.4633 20.3756 36.6881 21.5404 37.5205C22.6367 35.9593 23.6902 34.4541 24.7437 32.9533C25.1676 32.3452 25.2233 31.7889 24.491 31.28C24.0756 31.5258 23.6431 31.75 23.2448 32.0174C22.1914 32.729 21.1507 33.4579 19.9217 34.3075V34.3118Z"
          fill="#011424"
        />
      </g>
      <defs>
        <clipPath id="clip0_3608_172631">
          <rect
            width="35"
            height="94"
            fill="white"
            transform="translate(0 0.5)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
