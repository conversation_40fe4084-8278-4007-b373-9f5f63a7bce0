﻿using TMPro;
using UnityEngine;

namespace Login.Validator
{
    public abstract class InputValidator : MonoBehaviour
    {
        [SerializeField] protected string emptyTextMessage = "Input field cannot be empty.";
        [SerializeField] protected string invalidFormatMessage = "";

        [SerializeField] protected TMP_InputField textInputField;
        [SerializeField] protected TMP_Text validationResultText;

        [SerializeField] protected bool ignoreValidation = false;

        public string InputText
        {
            get => textInputField.text.Trim();
        }

        private void Awake()
        {
            if (textInputField == null)
            {
                Debug.LogError("Input Field is not assigned in the inspector.");
                return;
            }

            if (validationResultText == null)
            {
                Debug.LogError("Validation Result Text is not assigned in the inspector.");
                return;
            }

            validationResultText.text = string.Empty;
        }

        public InputValidationResult ValidateInputText()
        {
            string inputText = InputText;

            if (string.IsNullOrEmpty(inputText))
            {
                validationResultText.text = emptyTextMessage;
                return InputValidationResult.InvalidFormat;
            }

            if (!ignoreValidation && !IsValidTextFormat(inputText))
            {
                validationResultText.text = invalidFormatMessage;
                return InputValidationResult.InvalidFormat;
            }

            validationResultText.text = string.Empty;
            return InputValidationResult.ValidFormat;
        }

        protected abstract bool IsValidTextFormat(string inputText);

        public void SetValidationText(string text) => validationResultText.text = text;

        public void ClearValidationText() => validationResultText.text = string.Empty;

        public void ClearAll()
        {
            textInputField.text = string.Empty;
            ClearValidationText();
        }
    }
}
