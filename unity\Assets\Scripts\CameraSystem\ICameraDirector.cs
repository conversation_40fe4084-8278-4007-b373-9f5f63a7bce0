using App.CameraSystem.StateMachine.Data;
using UnityEngine;

namespace App.CameraSystem
{
    public interface ICameraDirector
    {
        public void PrepareShotTracking(Transform holeTransform, Vector3? predictionLocation = null);
        public void ResetCamera();
        public void StartPredictionCamera(PredictionPayload predictionPayload);
        public Vector3 GetScreenToWorldPosition(Vector3 screenPosition);
        public Ray GetScreenPointToRay(Vector3 screenPosition);
    }
}
