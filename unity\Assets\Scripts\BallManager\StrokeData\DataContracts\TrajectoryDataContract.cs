using System.Collections.Generic;
using JetBrains.Annotations;
using Newtonsoft.Json;

namespace BallManager.StrokeData.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class TrajectoryDataContract
    {
        [JsonProperty("ballTrajectory")]
        [CanBeNull]
        public List<BallTrajectoryDataContract> BallTrajectory;

        public bool IsValid() {
            if (BallTrajectory == null) {
                return false;
            }
            return true;
        }
    }
}
