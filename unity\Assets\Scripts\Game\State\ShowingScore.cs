namespace Game.State
{
    public class ShowingScore : BaseGameState
    {
        public override void Enter()
        {
            scoreManager.ShowScore();
            
            tabMenuBackButton.OnBackButtonPressed += BackButtonPressed;
            scoreManager.OnContinueButtonPressed += ContinueButtonPressed;
        }

        public override void Exit()
        {
            tabMenuBackButton.OnBackButtonPressed -= BackButtonPressed;
            scoreManager.OnContinueButtonPressed -= ContinueButtonPressed;
        }
        
        private void BackButtonPressed()
        {
            onFinished?.Invoke(GameStateType.ResetGame);
        }

        private void ContinueButtonPressed()
        {
            scoreManager.HideScore();
            onFinished?.Invoke(GameStateType.ResetGame);
        }
    }
}
