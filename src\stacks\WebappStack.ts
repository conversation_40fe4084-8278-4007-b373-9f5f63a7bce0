import {
  BaseMainStack,
  CfnExportNamePrefixAspect,
  getConfigValue,
  type MainStackProps,
  RemovalPolicyAspect,
  type Tag,
} from "@comcast/gee-aws-cdk-shared";
import { type IAspect } from "aws-cdk-lib";
import * as cdk from "aws-cdk-lib";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as ssm from "aws-cdk-lib/aws-ssm";
import { Construct } from "constructs";
import { AuthStack } from "./AuthStack";
import { RealUserMonitoringStack } from "./RealUserMonitoringStack";
import { Config } from "@/types/config";
import { getAccountStage } from "@/utils/getAccountStage";

type WebappStackProps = MainStackProps &
  Readonly<{
    stage: string;
    mainStackName: string;
  }>;

export class WebappStack extends BaseMainStack {
  constructor(scope: Construct, id: string, props: WebappStackProps) {
    super(scope, id, props);
    const { mainStackName, stage } = props;

    const importStage = getAccountStage(this.account, stage);
    const leaderboardApiUrl = getConfigValue<string>(
      scope,
      importStage,
      Config.LeaderboardApiUrl,
    );
    const leaderboardApiId = getConfigValue<string>(
      scope,
      importStage,
      Config.LeaderboardApiId,
    );
    const golfApiUrl = getConfigValue<string>(
      scope,
      importStage,
      Config.GolfApiUrl,
    );
    const golfApiWsUrl = getConfigValue<string>(
      scope,
      importStage,
      Config.GolfApiWsUrl,
    );
    const golfApiId = getConfigValue<string>(
      scope,
      importStage,
      Config.GolfApiId,
    );
    const maxHouseholdSize = getConfigValue<string>(
      scope,
      importStage,
      Config.MaxHouseholdSize,
    );
    const appVariant = getConfigValue<string>(
      scope,
      importStage,
      Config.AppVariant,
    );

    const cfDomainName = ssm.StringParameter.valueForStringParameter(
      this,
      `/${mainStackName}/${stage}/CloudFrontDomainName`,
    );

    const bucketName = ssm.StringParameter.valueForStringParameter(
      this,
      `/${mainStackName}/${stage}/WebappBucketName`,
    );

    const distributionId = ssm.StringParameter.valueForStringParameter(
      this,
      `/${mainStackName}/${stage}/WebappDistributionId`,
    );

    const rumProxyUrl = ssm.StringParameter.valueForStringParameter(
      this,
      `/${mainStackName}/${stage}/RumProxyUrl`,
    );

    const domainName = ssm.StringParameter.valueForStringParameter(
      this,
      `/${mainStackName}/${stage}/WebappDomainName`,
    );

    const certificateArn = ssm.StringParameter.valueForStringParameter(
      this,
      `/${mainStackName}/${stage}/CertificateArn`,
    );

    let certificate: acm.ICertificate | undefined = undefined;

    if (Boolean(certificateArn)) {
      certificate = acm.Certificate.fromCertificateArn(
        this,
        "GolfAppCertificate",
        certificateArn,
      );
    }

    const { userPoolId, userPoolClientId, userPoolDomain, householdTableArn } =
      new AuthStack(this, AuthStack.name, {
        stage,
        appVariant,
        leaderboardApiUrl,
        leaderboardApiId,
        golfApiUrl,
        golfApiId,
        domainName,
        maxHouseholdSize,
        certificate,
      });

    const { identityPoolId: rumIdentityPoolId, appMonitorId: rumAppMonitorId } =
      new RealUserMonitoringStack(this, RealUserMonitoringStack.name, {
        stage,
        domainName: cfDomainName,
      });

    const weatherApiKey = ssm.StringParameter.valueForStringParameter(
      this,
      "/weather-api/api-key",
    );

    const outputs: [string, string][] = [
      ["UserPoolId", userPoolId],
      ["UserPoolClientId", userPoolClientId],
      ["UserPoolDomain", userPoolDomain],
      ["WebappDomainName", domainName ?? cfDomainName],
      ["WebappDistributionId", distributionId],
      ["WebappBucketName", bucketName],
      ["RumProxyUrl", rumProxyUrl],
      ["RumIdentityPoolId", rumIdentityPoolId],
      ["RumAppMonitorId", rumAppMonitorId],
      ["RumRegion", this.region],
      ["AppEnv", stage],
      ["WeatherApiKey", weatherApiKey],
      ["LeaderboardApiUrl", leaderboardApiUrl],
      ["LeaderboardApiId", leaderboardApiId],
      ["GolfApiUrl", golfApiUrl],
      ["GolfApiWsUrl", golfApiWsUrl],
      ["HouseholdTableArn", householdTableArn],
    ];

    this.createCfnOutputs(outputs);
  }

  defaultAspects(): IAspect[] {
    const cfnExportNamePrefixAspect = new CfnExportNamePrefixAspect(
      "golf-app-web",
      this.stage,
    );
    return [cfnExportNamePrefixAspect, new RemovalPolicyAspect(this.stage)];
  }

  private createCfnOutputs(outputs: [string, string][]) {
    return outputs.map(
      ([exportName, value]) =>
        new cdk.CfnOutput(this, exportName, { exportName, value }),
    );
  }

  defaultTags(): Tag[] {
    return [
      {
        key: "project",
        value: "btv-genex-golf-app",
      },
      {
        key: "territory",
        value: "uk",
      },
      {
        key: "billing_team",
        value: "gpds",
      },
      {
        key: "service",
        value: "btv-genex-golf-app",
      },
    ];
  }
}
