using Newtonsoft.Json;

namespace Configuration.Impl.DataContract
{
	[JsonObject(MemberSerialization.OptIn)]
	public class ConfigurationDataContract 
	{
		[JsonProperty("aws_urls")]
		public AWSUrlDataContract AWSUrls { get; private set; }
		
		[JsonProperty("configurable_values")]
		public ConfigurableValuesDataContract ConfigurableValues { get; private set; }
		
		[JsonProperty("betting_areas")]
		public BettingAreaDataContract[] BettingAreas { get; private set; }

        [JsonProperty("app_ids")]
        public AppIdsDataContract AppIds { get; private set; }
    }
}
