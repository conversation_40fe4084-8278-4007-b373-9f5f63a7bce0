using System;
using Constants;
using Login.State;
using Login.Validator;
using Network.Services;
using Sky.GenEx.Toast;
using UnityEngine;
using UnityEngine.UI;

namespace Login
{
    public class ForgottenPassword : BaseLoginState
    {
        [SerializeField] private InputValidator emailValidator;
        [SerializeField] private Button sendLinkButton;
        [SerializeField] private Button backButton;

        public string EmailAddress { get; private set; }

        
        private void Start()
        {
            if(!InspectorValidator.CheckAssigned(emailValidator, nameof(emailValidator), this) ||
               !InspectorValidator.CheckAssigned(sendLinkButton, nameof(sendLinkButton), this) ||
               !InspectorValidator.CheckAssigned(backButton, nameof(backButton), this))
            {
                return;
            }
        }

        public async void ResetPassword()
        {
            try
            {
                var result = emailValidator.ValidateInputText();
                if (result == InputValidationResult.ValidFormat)
                {
                    EmailAddress = emailValidator.InputText;
                    var sendResetResult = await AuthService.SendPasswordResetEmail(EmailAddress);
                    if (sendResetResult.IsSuccess)
                    {
                        onFinished(AuthState.ResetPasswordConfirmation);
                        Debug.Log("Password reset link sent to the provided email.");
                    }
                    else
                    {
                        Debug.LogError($"Error trying to send reset password link: {sendResetResult.Error.Message}");
                        MessageToastController.Instance.ShowToast(sendResetResult.Error.Message, ToastDuration.Long, ToastType.Error);
                    }
                }
                else
                {
                    MessageToastController.Instance.ShowToast(DefaultMessages.InvalidEmailMessage);
                    Debug.LogError(DefaultMessages.InvalidEmailMessage);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong);
            }
        }

        public override void Enter()
        {
            gameObject.SetActive(true);
            sendLinkButton.onClick.AddListener(ResetPassword);
            backButton.onClick.AddListener(() => onFinished(AuthState.Welcome));
        }

        public override void Exit()
        {
            sendLinkButton.onClick.RemoveAllListeners();
            backButton.onClick.RemoveAllListeners();
            gameObject.SetActive(false);
        }
    }
}
