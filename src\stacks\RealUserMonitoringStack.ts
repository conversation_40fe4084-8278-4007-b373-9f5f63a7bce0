import * as cognitoIdp from "@aws-cdk/aws-cognito-identitypool-alpha";
import * as iam from "aws-cdk-lib/aws-iam";
import * as rum from "aws-cdk-lib/aws-rum";
import * as cdk from "aws-cdk-lib/core";
import { Construct } from "constructs";

type RealUserMonitoringStackProps = cdk.NestedStackProps &
  Readonly<{
    stage: string;
    domainName: string;
  }>;

// NOTE: Disabling certificate until we have a domain
// const GAME_THE_GREEN_DOMAIN = "*.gamethegreen.com";

export class RealUserMonitoringStack extends cdk.NestedStack {
  readonly identityPoolId: string;
  readonly appMonitorId: string;

  constructor(
    scope: Construct,
    id: string,
    props: RealUserMonitoringStackProps,
  ) {
    super(scope, id, props);

    const { domainName: domain, stage } = props;
    const appMonitorName = `${stage}-golf-app-monitor`;

    const identityPool = new cognitoIdp.IdentityPool(this, "RumIdentityPool", {
      allowUnauthenticatedIdentities: true,
    });
    const identityPoolId = identityPool.identityPoolId;

    const appMonitor = new rum.CfnAppMonitor(this, "GolfAppMonitor", {
      // NOTE: Disabling certificate until we have a domain
      // domain: isDeploymentStageProd(stage) ? GAME_THE_GREEN_DOMAIN : domain,
      domain,
      name: appMonitorName,
      appMonitorConfiguration: {
        sessionSampleRate: 1,
        enableXRay: true,
        allowCookies: true,
        telemetries: ["performance", "errors", "http"],
        identityPoolId: identityPoolId,
        guestRoleArn: identityPool.unauthenticatedRole.roleArn,
      },
      cwLogEnabled: true,
      customEvents: {
        status: "ENABLED",
      },
    });

    identityPool.unauthenticatedRole.addManagedPolicy(
      iam.ManagedPolicy.fromAwsManagedPolicyName("AWSXrayWriteOnlyAccess"),
    );
    identityPool.unauthenticatedRole.addToPrincipalPolicy(
      new iam.PolicyStatement({
        actions: ["rum:PutRumEvents"],
        resources: [
          `arn:aws:rum:${cdk.Aws.REGION}:${cdk.Aws.ACCOUNT_ID}:appmonitor/${appMonitorName}`,
        ],
      }),
    );

    this.identityPoolId = identityPoolId;
    this.appMonitorId = appMonitor.attrId;
  }
}
