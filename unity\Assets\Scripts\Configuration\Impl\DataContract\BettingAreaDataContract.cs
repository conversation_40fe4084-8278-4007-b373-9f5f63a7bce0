using Newtonsoft.Json;

namespace Configuration.Impl.DataContract
{
    [JsonObject(MemberSerialization.OptIn)]
    public class BettingAreaDataContract
    {
        [JsonProperty("hole")]
        public int HoleNumber { get; private set; }
        
        [JsonProperty("terrain_corner_1")]
        public Vector3DataContract TerrainCorner1 { get; private set; }
        
        [JsonProperty("terrain_corner_2")]
        public Vector3DataContract TerrainCorner2 { get; private set; }
    } 
}
