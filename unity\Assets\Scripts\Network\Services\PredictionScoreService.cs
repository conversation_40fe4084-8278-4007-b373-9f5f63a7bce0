using System;
using Network.Queries;
using Network.Response;
using Scoring.Data;
using Scoring.DataContracts;
using Testing.Scoring;
using UnityEngine;

namespace Network.Services
{
    public static class PredictionScoreService
    {
        public static async Awaitable<Result<PredictionScoreData>> GetPredictionScore(PredictionScoreDataInput queryParams)
        {
            var query = new GetPredictionScoreQuery(queryParams);
            try
            {
                var response = await AwsClient.Golf.QueryAsync<PredictionScoreDataContractWrapper>(query.QueryString, query.Name);

                if (response.IsSuccess)
                {
                    if (response.Data.PredictionScoreDataContract == null)
                    {
                        return Result<PredictionScoreData>.Failure(new ServiceError(-1, "Prediction score of null returned"));
                    }
                    var predictionScoreData = new PredictionScoreData(response.Data.PredictionScoreDataContract);
                    Debug.Log("Prediction score data received: " + predictionScoreData.Score);
                    return Result<PredictionScoreData>.Success(predictionScoreData);
                }
                else
                {
                    return Result<PredictionScoreData>.Failure(new ServiceError(-1, response.Errors[0]?.Message ?? "Unknown error"));
                }
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return Result<PredictionScoreData>.Failure(new ServiceError(-1, ex.Message));
            }
        }

        public static PredictionScoreData GetMockScore(PredictionScoreDataInput queryParams, int? score = 10)
        {
            var mockDataContract = new PredictionScoreDataContract
            {
                Score = score,
                HoleNumber = queryParams.HoleNumber,
                StrokeNumber = queryParams.StrokeNumber,
                GolferId = queryParams.GolferId
            };
            return new PredictionScoreData(mockDataContract);
        }

        public static async Awaitable<Result<PastPredictionScoresData>> GetPastPredictionScores(PastPredictionScoresDataInput queryParams)
        {
            var query = new GetPastPredictionScoresQuery(queryParams);
            try
            {
                var response = await AwsClient.Golf.QueryAsync<PastPredictionScoresDataContractWrapper>(query.QueryString, query.Name);

                if (response.IsSuccess)
                {
                    var predictionScoreData = new PastPredictionScoresData(response.Data.PastPredictionScoresDataContract);
                    return Result<PastPredictionScoresData>.Success(predictionScoreData);
                }
                else
                {
                    return Result<PastPredictionScoresData>.Failure(new ServiceError(-1, response.Errors[0]?.Message ?? "Unknown error"));
                }
            }
            catch (Exception ex)
            {
                Debug.LogException(ex);
                return Result<PastPredictionScoresData>.Failure(new ServiceError(-1, ex.Message));
            }
        }
    }
}