import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";
import { getEnvironmentVariable } from "@sky-uk/coins-utilities";
import { PreSignUpTriggerEvent } from "aws-lambda";
import { isFakeEmail } from "fakefilter";
import words from "profane-words";
import { getHouseholdSize } from "./utils/getHouseholdSize";
import { isExistingSignInAlias } from "./utils/isExistingSignInAlias";
import { hash } from "../../common/hash";

const HOUSEHOLD_TABLE_NAME = getEnvironmentVariable("HOUSEHOLD_TABLE_NAME");
const MAX_HOUSEHOLD_SIZE = Number(getEnvironmentVariable("MAX_HOUSEHOLD_SIZE"));
const APP_VARIANT = getEnvironmentVariable("APP_VARIANT"); // NOTE: comma separated list of app variants, e.g "home,onsite"

export const baseHandler = async (event: PreSignUpTriggerEvent) => {
  const { userName } = event;

  if (!userName) {
    throw new Error(": Missing username");
  }

  if (!/^[a-z][a-z0-9]+$/.test(userName) || words.includes(userName)) {
    throw new Error(": Username is invalid");
  }

  const email = event.request.userAttributes.email;
  if (!email) {
    throw new Error(": Missing email");
  }

  if (isFakeEmail(email)) {
    logger.info("Fake email detected", { email });
    throw new Error(": Invalid email");
  }

  if (APP_VARIANT.split(",").includes("home")) {
    const deviceId = event.request.userAttributes["custom:deviceId"];
    if (!deviceId) {
      throw new Error(": Missing deviceId");
    }

    let householdSize: number;
    try {
      householdSize = await getHouseholdSize({
        deviceId,
        tableName: HOUSEHOLD_TABLE_NAME,
      });
    } catch (error) {
      logger.error("Error raised when getHouseholdSize was called", { error });
      throw new Error(": Internal Server Error");
    }

    if (householdSize >= MAX_HOUSEHOLD_SIZE) {
      logger.info("Too many users signed up with this device");
      throw new Error(": Household limit reached");
    }

    let isExistingEmail;
    try {
      isExistingEmail = await isExistingSignInAlias({
        signInAlias: hash(email),
        tableName: HOUSEHOLD_TABLE_NAME,
      });
    } catch (error) {
      logger.error("Error thrown when calling isExistingSignInAlias");
      throw new Error(": Internal Server Error");
    }

    if (isExistingEmail) {
      logger.info("Email already exists");
      throw new Error(": Email already exists");
    }
  }

  event.response.autoConfirmUser = true;
  event.response.autoVerifyEmail = true;

  return event;
};
