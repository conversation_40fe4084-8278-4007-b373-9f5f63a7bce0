using PredictionWindow;
using TMPro;
using UnityEngine;

namespace Game
{
    public class GameStateUi : MonoBehaviour
    {
        [SerializeField] private TMP_Text timerText;
        
        [SerializeField] private GameManager gameManager;
        [SerializeField] private PredictionWindowManager predictionWindowManager;
        
        private const string WaitForStrokeText = "Waiting";
        private const string ShowingStrokeText = "In progress";
        private const string ShowingScoreText = "Result";

        private void OnEnable()
        {
            timerText.text = "";
            
            if (gameManager.CurrentState.StateType == GameStateType.PredictionWindowOpen ||
                gameManager.CurrentState.StateType == GameStateType.PlacingPrediction)
            {
                predictionWindowManager.PredictionWindowTimeUpdated += UpdateTimeRemaining;
            }
            
            gameManager.OnStateChanged += OnGameStateChange;
        }
        
        private void OnDisable()
        {
            gameManager.OnStateChanged -= OnGameStateChange;
            predictionWindowManager.PredictionWindowTimeUpdated -= UpdateTimeRemaining;
        }

        private void OnGameStateChange(GameStateType newState)
        {
            predictionWindowManager.PredictionWindowTimeUpdated -= UpdateTimeRemaining;
            
            switch (newState)
            {
                case GameStateType.WaitingForStroke:
                    timerText.text = WaitForStrokeText;
                    break;
                case GameStateType.ShowingStroke:
                    timerText.text = ShowingStrokeText;
                    break;
                case GameStateType.ShowingScore:
                    timerText.text = ShowingScoreText;
                    break;
                case GameStateType.PredictionWindowOpen:
                    predictionWindowManager.PredictionWindowTimeUpdated += UpdateTimeRemaining;
                    break;
                case GameStateType.PlacingPrediction:
                    predictionWindowManager.PredictionWindowTimeUpdated += UpdateTimeRemaining;
                    break;
                default:
                    timerText.text = "";
                    break;
            }
        }

        private void UpdateTimeRemaining(int secondsRemaining)
        {
            timerText.text = $"Closes in <b>{secondsRemaining}s</b>";
        }
    }
}