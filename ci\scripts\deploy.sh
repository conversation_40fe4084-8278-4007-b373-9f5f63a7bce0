#!/usr/bin/env bash

set -e

for ARG in "$@"; do
  echo $ARG
  if [[ $ARG == stage=* ]]; then
    
    KEY=$(echo $ARG | cut -f1 -d=)
    KEY_LENGTH=${#KEY}
    VALUE="${ARG:$KEY_LENGTH+1}"
    STAGE=${VALUE}
  fi
done

if [ -z "$STAGE" ]; then
  echo "Stage not provided. Use --context stage=<stage_name> to specify the stage."
  exit 1
fi

export INLINE_SCRIPT_CSP_HASH=$(cat ./UnityBuildOutput/inline-script-csp-hash.txt)

# Running build as a Workaround for https://github.com/aws/aws-cdk/issues/6743
bunx cdk deploy ${STAGE}-golf-app --require-approval never --context stage=${STAGE}
bunx cdk deploy ${STAGE}-golf-app-web --require-approval never --context stage=${STAGE} --outputs-file "./dist/app-web-outputs.json"
node ./ci/scripts/updateUnityConfig.cjs
bunx cdk deploy ${STAGE}-golf-app-deployment --require-approval never --context stage=${STAGE}