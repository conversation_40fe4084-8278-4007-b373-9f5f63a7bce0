using System;
using Constants;
using Login.State;
using Login.Validator;
using Network.Auth;
using Network.Services;
using Sky.GenEx.Toast;
using UnityEngine;
using UnityEngine.UI;

namespace Login
{
    public class UserSignIn : BaseLoginState
    {
        [Header("Validators")]
        [SerializeField] private InputValidator emailValidator;
        [SerializeField] private InputValidator passwordValidator;

        [Head<PERSON>("Buttons")]
        [SerializeField] private Button signInButton;
        [SerializeField] private Button forgotPasswordButton;
        [SerializeField] private Button backButton;

        private void Start()
        {
            if (!InspectorValidator.CheckAssigned(emailValidator, nameof(emailValidator), this) ||
                !InspectorValidator.CheckAssigned(passwordValidator, nameof(passwordValidator), this) ||
                !InspectorValidator.CheckAssigned(signInButton, nameof(signInButton), this) ||
                !InspectorValidator.CheckAssigned(forgotPasswordButton, nameof(forgotPasswordButton), this) ||
                !InspectorValidator.CheckAssigned(backButton, nameof(backButton), this))
            {
                return;
            }
        }

        public void SignUserIn()
        {
            var emailResult = emailValidator.ValidateInputText();
            if (emailResult != InputValidationResult.ValidFormat)
            {
                Debug.LogError("Invalid email format.");
                return;
            }

            var passwordResult = passwordValidator.ValidateInputText();
            if (passwordResult != InputValidationResult.ValidFormat)
            {
                Debug.LogError("Invalid password format.");
                return;
            }

            SignInUserAsync(emailValidator.InputText, passwordValidator.InputText);
        }

        private async void SignInUserAsync(string email, string password)
        {
            try
            {
                signInButton.interactable = false;
                var signInResult = await AuthService.Login(email, password);

                // Check if this MonoBehaviour is still valid
                if (this == null || !this.gameObject) return;

                if (!signInResult.IsSuccess)
                {
                    Debug.LogError($"User sign-in failed: {signInResult.Error.Message}");
                    var updateMessage = signInResult.Error.Message + "\nPlease check your credentials and try again.";
                    MessageToastController.Instance.ShowToast(updateMessage, ToastDuration.Long, ToastType.Error);
                    signInButton.interactable = true;
                }
                else
                {
                    Debug.Log("User sign-in successful.");
                    Debug.Log("Auth Token: " + signInResult.Value);
                    AuthTokenManager.Instance.UpdateTokens(signInResult.Value);
                    onFinished(AuthState.Authenticated);
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong);
            }
        }


        public override void Enter()
        {
            gameObject.SetActive(true);

            signInButton.onClick.AddListener(SignUserIn);
            signInButton.interactable = true;

            forgotPasswordButton.onClick.AddListener(() => onFinished(AuthState.ForgotPassword));
            backButton.onClick.AddListener(() => onFinished(AuthState.Welcome));
        }

        public override void Exit()
        {            
            emailValidator.ClearAll();
            passwordValidator.ClearAll();

            signInButton?.onClick.RemoveAllListeners();
            forgotPasswordButton?.onClick.RemoveAllListeners();
            backButton?.onClick.RemoveAllListeners();
            gameObject?.SetActive(false);
        }
    }
}
