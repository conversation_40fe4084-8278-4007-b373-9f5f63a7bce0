using UnityEngine;

namespace Login.Validator
{
    public class UserNameValidator : InputValidator
    {
        [SerializeField] private int minLength = 3;
        [SerializeField] private int maxLength = 16;
        [SerializeField] private bool forceLowercase = true;


        private void OnEnable()
        {
            if (textInputField != null && !ignoreValidation)
            {
                textInputField.onValueChanged.AddListener(OnInputChanged);
            }
        }

        private void OnDisable()
        {
            if (textInputField != null)
            {
                textInputField.onValueChanged.RemoveListener(OnInputChanged);
            }
        }

        private void OnInputChanged(string input)
        {
            if (!forceLowercase)
                return;

            if (!string.IsNullOrEmpty(input))
            {
                string lowercaseInput = input.ToLower();
                textInputField.SetTextWithoutNotify(lowercaseInput);
            }
        }

        protected override bool IsValidTextFormat(string inputText)
        {
            if (string.IsNullOrWhiteSpace(inputText))
                return false;

            if (inputText.Length < minLength || inputText.Length > maxLength)
            {
                invalidFormatMessage = $"Username must be between {minLength} and {maxLength} characters.";
                return false;
            }

            if (char.IsDigit(inputText[0]))
            {
                invalidFormatMessage = "Username cannot start with a number.";
                return false;
            }

            foreach (char c in inputText)
            {
                if (!char.IsLetterOrDigit(c))
                {
                    invalidFormatMessage = "Username can only contain letters and numbers.";
                    return false;
                }
            }

            return true;
        }
    }
}
