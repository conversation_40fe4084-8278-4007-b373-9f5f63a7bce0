namespace Network.Response
{
    public abstract class ResultBase
    {
        public ServiceError Error { get; }
        public bool IsSuccess => Error == null;

        protected ResultBase(ServiceError error)
        {
            Error = error;
        }
    }

    public class Result : ResultBase
    {
        public static Result Success() => new Result(null);
        public static Result Failure(ServiceError error) => new Result(error);

        private Result(ServiceError error) : base(error)
        {
        }
    }

    public class Result<T> : ResultBase
    {
        public T Value { get; }

        public static Result<T> Success(T value) => new Result<T>(value, null);
        public static Result<T> Failure(ServiceError error) => new Result<T>(default, error);

        private Result(T value, ServiceError error) : base(error)
        {
            Value = value;
        }
    }
}