mergeInto(LibraryManager.library, {
  RequestToken: function () {
    window.dispatchReactUnityEvent("RequestToken");
  },
  SignOut: function () {
    window.dispatchReactUnityEvent("SignOut");
  },
  EmitLoadProgress: function (progress) {
    window.dispatchReactUnityEvent("UnityLoadProgress", progress);
  },
  OpenLinkInBrowser: function (link) {
    window.dispatchReactUnityEvent("UnityOpenLink", UTF8ToString(link));
  }
});
