import { SVGProps } from "react";

export const EyeIcon = ({
  disabled = false,
  ...props
}: SVGProps<SVGSVGElement> & { disabled?: boolean }) => {
  return (
    <svg
      width="100%"
      height="100%"
      viewBox="0 0 84 84"
      version="1.1"
      xmlns="http://www.w3.org/2000/svg"
      fillRule="evenodd"
      clipRule="evenodd"
      strokeLinejoin="round"
      strokeMiterlimit={2}
      fill="white"
      {...props}
    >
      <path d="M41.667,26.96c-8.122,0 -14.707,6.585 -14.707,14.707c0,8.122 6.585,14.706 14.707,14.706c8.122,0 14.706,-6.584 14.706,-14.706c0,-8.122 -6.584,-14.707 -14.706,-14.707Zm-8.404,14.707c-0,-4.641 3.762,-8.404 8.404,-8.404c4.641,0 8.403,3.763 8.403,8.404c0,4.641 -3.762,8.403 -8.403,8.403c-4.642,0 -8.404,-3.762 -8.404,-8.403Z" />
      <path d="M41.667,11.203c-22.912,0 -36.33,18.994 -40.713,29.222c-0.39,0.909 -0.33,1.947 0.16,2.805c5.554,9.72 17.569,28.9 40.553,28.9c22.983,0 34.998,-19.18 40.553,-28.9c0.522,-0.914 0.553,-2.03 0.082,-2.973c-2.215,-4.43 -6.627,-11.608 -13.295,-17.721c-6.69,-6.132 -15.819,-11.333 -27.34,-11.333Zm-0,54.624c-18.167,0 -28.463,-14.423 -34.277,-24.335c4.544,-9.488 16.101,-23.986 34.277,-23.986c9.488,0 17.166,4.254 23.081,9.676c5.245,4.808 8.971,10.423 11.139,14.406c-5.824,9.909 -16.112,24.239 -34.22,24.239Z" />
      {disabled && (
        <path d="M4.536,15.799l-3.837,6.645l78.099,45.091l3.837,-6.646l-78.099,-45.09Z" />
      )}
    </svg>
  );
};
