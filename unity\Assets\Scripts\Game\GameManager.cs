using System;
using Game.State;
using Prediction;
using PredictionWindow;
using Scoring;
using Stroke;
using TabMenu;
using TabMenu.Containers;
using UnityEngine;

namespace Game
{
    [Serializable]
    public struct GameState
    {
        public GameStateType StateType;
        public BaseGameState StateObject;
    }
    
    public class GameManager : MonoBehaviour
    {
        public Action<GameStateType> OnStateChanged;
        public GameState CurrentState => currentState;

        [SerializeField] private GameState[] GameStates;
        [SerializeField] private TabMenuManager tabMenuManager;
        [SerializeField] private TabMenuBackButton tabMenuBackButton;
        [SerializeField] private PredictionWindowManager predictionWindowManager;
        [SerializeField] private PredictionManager predictionManager;
        [SerializeField] private StrokeManager strokeManager;
        [SerializeField] private ScoreManager scoreManager;
        [SerializeField] private PlayContainer playContainer;

        private GameState currentState;

        private void Start()
        {
            if (tabMenuManager == null)
            {
                Debug.LogError("TabMenuManager is not assigned in the inspector.");
                return;
            }
            if (tabMenuBackButton == null)
            {
                Debug.LogError("TabMenuBackButton is not assigned in the inspector.");
                return;
            }
            if (predictionWindowManager == null)
            {
                Debug.LogError("PredictionWindowManager is not assigned in the inspector.");
                return;
            }
            if (predictionManager == null)
            {
                Debug.LogError("PredictionManager is not assigned in the inspector.");
                return;
            }
            if (strokeManager == null)
            {
                Debug.LogError("StrokeManager is not assigned in the inspector.");
                return;
            }
            if (scoreManager == null)
            {
                Debug.LogError("ScoreManager is not assigned in the inspector.");
                return;
            }
            if (playContainer == null)
            {
                Debug.LogError("PlayContainer is not assigned in the inspector.");
                return;
            }
            
            foreach (var gameState in GameStates)
            {
                if (gameState.StateObject == null)
                {
                    Debug.LogError($"GameState {gameState.StateType} is not assigned in the inspector.");
                    return;
                }
                
                gameState.StateObject.SetupState(ChangeStateTo, tabMenuManager, tabMenuBackButton, predictionWindowManager, predictionManager, strokeManager, scoreManager, playContainer);
            }

            ChangeStateTo(GameStateType.WaitingForPredictionWindow);
        }

        private void ChangeStateTo(GameStateType state)
        {
            currentState.StateObject?.Exit();

            foreach (var gameState in GameStates)
            {
                if (gameState.StateType == state)
                {
                    Debug.Log("Changing state to: " + state);
                    currentState = gameState;
                    currentState.StateObject.Enter();
                    OnStateChanged?.Invoke(state);
                    return;
                }
            }

            Debug.LogError($"State {state} not found in GameStates.");
        }
    }
}