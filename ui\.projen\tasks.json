{"tasks": {"build": {"name": "build", "description": "Full release build", "steps": [{"spawn": "pre-compile"}, {"spawn": "compile"}, {"spawn": "post-compile"}, {"spawn": "test"}, {"spawn": "package"}]}, "compile": {"name": "compile", "description": "Only compile", "steps": [{"exec": "react-scripts build"}]}, "default": {"name": "default", "description": "Synthesize project files", "steps": [{"exec": "bunx projen default", "cwd": ".."}]}, "dev": {"name": "dev", "description": "Starts the react application", "steps": [{"exec": "react-scripts start"}]}, "eslint": {"name": "eslint", "description": "Runs eslint against the codebase", "env": {"ESLINT_USE_FLAT_CONFIG": "false"}, "steps": [{"exec": "eslint --ext .ts,.tsx --fix --no-error-on-unmatched-pattern $@ test build-tools", "receiveArgs": true}]}, "install": {"name": "install", "description": "Install project dependencies and update lockfile (non-frozen)", "steps": [{"exec": "bun install"}]}, "install:ci": {"name": "install:ci", "description": "Install project dependencies using frozen lockfile", "steps": [{"exec": "bun install --frozen-lockfile"}]}, "package": {"name": "package", "description": "Creates the distribution package"}, "post-compile": {"name": "post-compile", "description": "Runs after successful compilation"}, "post-upgrade": {"name": "post-upgrade", "description": "Runs after upgrading dependencies"}, "pre-compile": {"name": "pre-compile", "description": "Prepare the project for compilation"}, "test": {"name": "test", "description": "Run tests", "steps": [{"spawn": "eslint"}, {"exec": "react-scripts test --watchAll=false"}]}, "upgrade": {"name": "upgrade", "description": "upgrade dependencies", "env": {"CI": "0"}, "steps": [{"exec": "bunx npm-check-updates@16 --upgrade --target=minor --peer --no-deprecated --dep=dev,peer,prod,optional --filter=@testing-library/jest-dom,@testing-library/react,@testing-library/user-event,@types/jest,@types/js-cookie,@types/react,@types/react-dom,autoprefixer,eslint-import-resolver-typescript,eslint-plugin-import,eslint-plugin-prettier,postcss,prettier,tailwindcss,@react-spring/web,aws-amplify,aws-rum-web,js-cookie,no-profanity,react,react-dom,react-error-boundary,react-router,react-unity-webgl,web-vitals"}, {"exec": "bun install"}, {"exec": "bun update @testing-library/jest-dom @testing-library/react @testing-library/user-event @types/jest @types/js-cookie @types/node @types/react @types/react-dom @typescript-eslint/eslint-plugin @typescript-eslint/parser autoprefixer eslint-config-prettier eslint-import-resolver-typescript eslint-plugin-import eslint-plugin-prettier eslint postcss prettier react-scripts tailwindcss typescript @react-spring/web aws-amplify aws-rum-web js-cookie no-profanity react react-dom react-error-boundary react-router react-unity-webgl web-vitals"}, {"exec": "bunx projen"}, {"spawn": "post-upgrade"}]}, "watch": {"name": "watch", "description": "Watch & compile in the background", "steps": [{"exec": "tsc --build -w"}]}}, "env": {"PATH": "$(bun --eval \"console.log(process.env.PATH)\")"}, "//": "~~ Generated by projen. To modify, edit .projenrc.js and run \"bunx projen\"."}