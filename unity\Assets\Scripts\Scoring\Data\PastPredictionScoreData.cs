using System;
using Scoring.DataContracts;
using UnityEngine;

namespace Scoring.Data
{
    public class PastPredictionScoreData
    {
        public DateTime CreatedTimestamp { get; }
        public int GolferId { get; }
        public int HoleNumber { get; }
        public int? Score { get; }
        public string GolferName { get; }

        public PastPredictionScoreData(DateTime createdTimestamp, int golferId, int holeNumber, int? score, string golferName)
        {
            CreatedTimestamp = createdTimestamp;
            GolferId = golferId;
            HoleNumber = holeNumber;
            Score = score;
            GolferName = golferName;
        }

        public PastPredictionScoreData(PastPredictionScoreDataContract contract)
        {
            if (DateTime.TryParse(contract.CreatedTimestamp, out DateTime createdTimestamp))
            {
                CreatedTimestamp = createdTimestamp;
            }
            else
            {
                throw new ArgumentException("[PastPredictionScoreData] CreatedTimestamp could not be parsed");
            }
            
            GolferId = contract.GolferId;
            HoleNumber = contract.HoleNumber;
            Score = contract.Score;
            GolferName = contract.GolferName;
        }
    }
}