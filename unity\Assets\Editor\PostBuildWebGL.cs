using System.IO;
using UnityEditor;
using UnityEditor.Callbacks;
using UnityEngine;

public class PostBuildWebGL
{
    // This method is called after the build process is completed
    // achieves 2 things - remove unnecessary build files, and fix snyk issues
    [PostProcessBuild]
    public static void OnPostprocessBuild(BuildTarget target, string pathToBuiltProject)
    {
        // Check if the build target is WebGL
        if (target == BuildTarget.WebGL)
        {
            // Define the path to the index.html file
            string indexPath = Path.Combine(pathToBuiltProject, "index.html");

            // Get the project name from the build path
            string projectName = new DirectoryInfo(pathToBuiltProject).Name;

            // Define the path to the Build folder
            string buildFolder = Path.Combine(pathToBuiltProject, "Build");

            // Check if the Build folder exists
            if (Directory.Exists(buildFolder))
            {
                // Get all .loader.js files in the Build folder
                string[] files = Directory.GetFiles(buildFolder, "*.loader.js");

                // If there are any .loader.js files
                if (files.Length > 0)
                {
                    // Use the detected build name in another process
                    string loaderPath = Path.Combine(buildFolder, projectName + ".loader.js");

                    // Read the content of the loader.js file
                    string content = File.ReadAllText(loaderPath);

                    // Replace "StreamingAssets" with the project-specific path
                    content = content.Replace(
                        "\"StreamingAssets\"",
                        "\"" + projectName + "/StreamingAssets\""
                    );

                    // Find the pattern to modify streamingAssetsUrl
                    string pattern = "streamingAssetsUrl=new URL(";
                    int lastSemicolonBeforePattern = content.LastIndexOf(';', content.IndexOf(pattern));
                    int startIndex = lastSemicolonBeforePattern + 1;

                    // If the start index is valid, remove the existing URL assignment
                    if (startIndex != -1)
                    {
                        int endIndex = content.IndexOf(";", startIndex);
                        if (endIndex != -1)
                        {
                            content = content.Remove(startIndex, endIndex - startIndex + 1);
                        }
                    }

                    // Write the modified content back to the loader.js file
                    File.WriteAllText(loaderPath, content);
                    Debug.Log($"Modified {projectName}.loader.js successfully");
                }
                else
                {
                    Debug.LogError("No .loader.js file found in Build folder");
                }
            }
        }
    }
}
