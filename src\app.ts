import { App } from "aws-cdk-lib";
import { MainStack } from "@/stacks/MainStack";
import { WebappDeploymentStack } from "@/stacks/WebappDeploymentStack";
import { WebappStack } from "@/stacks/WebappStack";

const webappEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: process.env.CDK_DEFAULT_REGION,
};

const firewallEnv = {
  account: process.env.CDK_DEFAULT_ACCOUNT,
  region: "us-east-1",
};

const app = new App();

app.node.setContext(
  "@aws-cdk/customresources:installLatestAwsSdkDefault",
  false,
);

const stage = process.env.STAGE ?? app.node.getContext("stage");
const mainStackName = "golf-app";

new MainStack(app, `${stage}-${mainStackName}`, {
  env: firewallEnv,
  webappRegion: webappEnv.region ?? "",
  stage,
});

new WebappStack(app, `${stage}-${mainStackName}-web`, {
  env: webappEnv,
  stage,
  mainStackName,
});

new WebappDeploymentStack(app, `${stage}-${mainStackName}-deployment`, {
  env: webappEnv,
  stage,
  webAppStackName: `${mainStackName}-web`,
});

app.synth();
