using UnityEngine;
using UnityEngine.UI;

namespace UI.Carousel
{
    [RequireComponent(typeof(Button))]
    public class CarouselCloseButton : MonoBehaviour
    {
        [SerializeField] private GameObject carouselRoot;
        
        private Button button;

        private void Start()
        {
            button = GetComponent<Button>();
            
            if (carouselRoot != null)
            {
                button.onClick.AddListener(DisableCarousel);
            }
        }

        private void DisableCarousel()
        {
            carouselRoot.SetActive(false);
        }
    }
}