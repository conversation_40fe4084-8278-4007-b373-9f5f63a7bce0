-target:library
-out:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.dll"
-refout:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.ref.dll"
-define:UNITY_6000_0_44
-define:UNITY_6000_0
-define:UNITY_6000
-define:UNITY_5_3_OR_NEWER
-define:UNITY_5_4_OR_NEWER
-define:UNITY_5_5_OR_NEWER
-define:UNITY_5_6_OR_NEWER
-define:UNITY_2017_1_OR_NEWER
-define:UNITY_2017_2_OR_NEWER
-define:UNITY_2017_3_OR_NEWER
-define:UNITY_2017_4_OR_NEWER
-define:UNITY_2018_1_OR_NEWER
-define:UNITY_2018_2_OR_NEWER
-define:UNITY_2018_3_OR_NEWER
-define:UNITY_2018_4_OR_NEWER
-define:UNITY_2019_1_OR_NEWER
-define:UNITY_2019_2_OR_NEWER
-define:UNITY_2019_3_OR_NEWER
-define:UNITY_2019_4_OR_NEWER
-define:UNITY_2020_1_OR_NEWER
-define:UNITY_2020_2_OR_NEWER
-define:UNITY_2020_3_OR_NEWER
-define:UNITY_2021_1_OR_NEWER
-define:UNITY_2021_2_OR_NEWER
-define:UNITY_2021_3_OR_NEWER
-define:UNITY_2022_1_OR_NEWER
-define:UNITY_2022_2_OR_NEWER
-define:UNITY_2022_3_OR_NEWER
-define:UNITY_2023_1_OR_NEWER
-define:UNITY_2023_2_OR_NEWER
-define:UNITY_2023_3_OR_NEWER
-define:UNITY_6000_0_OR_NEWER
-define:PLATFORM_ARCH_64
-define:UNITY_64
-define:UNITY_INCLUDE_TESTS
-define:ENABLE_AR
-define:ENABLE_AUDIO
-define:ENABLE_CACHING
-define:ENABLE_CLOTH
-define:ENABLE_EVENT_QUEUE
-define:ENABLE_MICROPHONE
-define:ENABLE_MULTIPLE_DISPLAYS
-define:ENABLE_PHYSICS
-define:ENABLE_TEXTURE_STREAMING
-define:ENABLE_VIRTUALTEXTURING
-define:ENABLE_LZMA
-define:ENABLE_UNITYEVENTS
-define:ENABLE_VR
-define:ENABLE_WEBCAM
-define:ENABLE_UNITYWEBREQUEST
-define:ENABLE_WWW
-define:ENABLE_CLOUD_SERVICES
-define:ENABLE_CLOUD_SERVICES_ADS
-define:ENABLE_CLOUD_SERVICES_USE_WEBREQUEST
-define:ENABLE_CLOUD_SERVICES_CRASH_REPORTING
-define:ENABLE_CLOUD_SERVICES_PURCHASING
-define:ENABLE_CLOUD_SERVICES_ANALYTICS
-define:ENABLE_CLOUD_SERVICES_BUILD
-define:ENABLE_EDITOR_GAME_SERVICES
-define:ENABLE_UNITY_GAME_SERVICES_ANALYTICS_SUPPORT
-define:ENABLE_CLOUD_LICENSE
-define:ENABLE_EDITOR_HUB_LICENSE
-define:ENABLE_WEBSOCKET_CLIENT
-define:ENABLE_GENERATE_NATIVE_PLUGINS_FOR_ASSEMBLIES_API
-define:ENABLE_DIRECTOR_AUDIO
-define:ENABLE_DIRECTOR_TEXTURE
-define:ENABLE_MANAGED_JOBS
-define:ENABLE_MANAGED_TRANSFORM_JOBS
-define:ENABLE_MANAGED_ANIMATION_JOBS
-define:ENABLE_MANAGED_AUDIO_JOBS
-define:ENABLE_MANAGED_UNITYTLS
-define:INCLUDE_DYNAMIC_GI
-define:ENABLE_SCRIPTING_GC_WBARRIERS
-define:PLATFORM_SUPPORTS_MONO
-define:RENDER_SOFTWARE_CURSOR
-define:ENABLE_MARSHALLING_TESTS
-define:ENABLE_VIDEO
-define:ENABLE_NAVIGATION_OFFMESHLINK_TO_NAVMESHLINK
-define:ENABLE_ACCELERATOR_CLIENT_DEBUGGING
-define:TEXTCORE_1_0_OR_NEWER
-define:EDITOR_ONLY_NAVMESH_BUILDER_DEPRECATED
-define:PLATFORM_STANDALONE_WIN
-define:PLATFORM_STANDALONE
-define:UNITY_STANDALONE_WIN
-define:UNITY_STANDALONE
-define:ENABLE_RUNTIME_GI
-define:ENABLE_MOVIES
-define:ENABLE_NETWORK
-define:ENABLE_NVIDIA
-define:ENABLE_AMD
-define:ENABLE_CRUNCH_TEXTURE_COMPRESSION
-define:ENABLE_OUT_OF_PROCESS_CRASH_HANDLER
-define:ENABLE_CLUSTER_SYNC
-define:ENABLE_CLUSTERINPUT
-define:PLATFORM_UPDATES_TIME_OUTSIDE_OF_PLAYER_LOOP
-define:GFXDEVICE_WAITFOREVENT_MESSAGEPUMP
-define:PLATFORM_USES_EXPLICIT_MEMORY_MANAGER_INITIALIZER
-define:PLATFORM_SUPPORTS_WAIT_FOR_PRESENTATION
-define:PLATFORM_SUPPORTS_SPLIT_GRAPHICS_JOBS
-define:ENABLE_MONO
-define:NET_STANDARD_2_0
-define:NET_STANDARD
-define:NET_STANDARD_2_1
-define:NETSTANDARD
-define:NETSTANDARD2_1
-define:ENABLE_PROFILER
-define:DEBUG
-define:TRACE
-define:UNITY_ASSERTIONS
-define:UNITY_EDITOR
-define:UNITY_EDITOR_64
-define:UNITY_EDITOR_WIN
-define:ENABLE_UNITY_COLLECTIONS_CHECKS
-define:ENABLE_BURST_AOT
-define:UNITY_TEAM_LICENSE
-define:UNITY_PRO_LICENSE
-define:ENABLE_CUSTOM_RENDER_TEXTURE
-define:ENABLE_DIRECTOR
-define:ENABLE_LOCALIZATION
-define:ENABLE_SPRITES
-define:ENABLE_TERRAIN
-define:ENABLE_TILEMAP
-define:ENABLE_TIMELINE
-define:ENABLE_LEGACY_INPUT_MANAGER
-define:TEXTCORE_FONT_ENGINE_1_5_OR_NEWER
-define:TEXTCORE_TEXT_ENGINE_1_5_OR_NEWER
-define:CSHARP_7_OR_LATER
-define:CSHARP_7_3_OR_NEWER
-r:"Assets/ThirdParty/DOTween/DOTween.dll"
-r:"Assets/ThirdParty/ZXing.Net.0.16.9/lib/netstandard2.1/zxing.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.AccessibilityModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.AdaptivePerformanceModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.BuildProfileModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreBusinessMetricsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.CoreModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.DeviceSimulatorModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.DiagnosticsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.EditorToolbarModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.EmbreeModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.GIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.GraphViewModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridAndSnapModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.GridModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.MultiplayerModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.Physics2DModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.PhysicsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.PresetsUIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.PropertiesModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.QuickSearchModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SafeModeModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneTemplateModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SceneViewModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.ShaderFoundryModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SketchUpModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteMaskModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SpriteShapeModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.SubstanceModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.TerrainModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.TextRenderingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.TilemapModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.TreeModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIAutomationModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIBuilderModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.UIElementsSamplesModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.UmbraModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.UnityConnectModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.VFXModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.VideoModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEditor.XRModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.AccessibilityModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.AIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.AndroidJNIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.AnimationModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ARModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.AssetBundleModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.AudioModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClothModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterInputModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ClusterRendererModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ContentLoadModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.CoreModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.CrashReportingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.DirectorModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.DSPGraphModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.GameCenterModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.GIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.GraphicsStateCollectionSerializerModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.GridModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.HierarchyCoreModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.HotReloadModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ImageConversionModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.IMGUIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputForUIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputLegacyModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.InputModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.JSONSerializeModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.LocalizationModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.MarshallingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.MultiplayerModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ParticleSystemModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.PerformanceReportingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.Physics2DModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.PhysicsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.PropertiesModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.RuntimeInitializeOnLoadManagerInitializerModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ScreenCaptureModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.ShaderVariantAnalyticsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.SharedInternalsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteMaskModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.SpriteShapeModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.StreamingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubstanceModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.SubsystemsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TerrainPhysicsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreFontEngineModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextCoreTextEngineModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TextRenderingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TilemapModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.TLSModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIElementsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UIModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UmbraModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsCommonModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityAnalyticsModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityConnectModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityCurlModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityTestProtocolModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAssetBundleModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestAudioModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestTextureModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.UnityWebRequestWWWModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.VehiclesModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.VFXModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.VideoModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.VirtualTexturingModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.VRModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.WindModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/Managed/UnityEngine/UnityEngine.XRModule.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/mscorlib.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ComponentModel.Composition.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Core.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Data.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Drawing.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.IO.Compression.FileSystem.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Net.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Numerics.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Runtime.Serialization.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.ServiceModel.Web.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Transactions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Web.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Windows.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Linq.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netfx/System.Xml.Serialization.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/Microsoft.Win32.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.AppContext.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Buffers.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Concurrent.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.NonGeneric.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Collections.Specialized.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.EventBasedAsync.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ComponentModel.TypeConverter.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Console.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Data.Common.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Contracts.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Debug.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.FileVersionInfo.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Process.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.StackTrace.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TextWriterTraceListener.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tools.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.TraceSource.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Diagnostics.Tracing.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Drawing.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Dynamic.Runtime.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Calendars.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Globalization.Extensions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Compression.ZipFile.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.DriveInfo.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.FileSystem.Watcher.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.IsolatedStorage.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.MemoryMappedFiles.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.Pipes.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.IO.UnmanagedMemoryStream.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Expressions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Parallel.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Linq.Queryable.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Memory.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Http.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NameResolution.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.NetworkInformation.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Ping.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Requests.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Security.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.Sockets.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebHeaderCollection.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.Client.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Net.WebSockets.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Numerics.Vectors.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ObjectModel.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.DispatchProxy.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.ILGeneration.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Emit.Lightweight.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Extensions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Reflection.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Reader.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.ResourceManager.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Resources.Writer.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.CompilerServices.VisualC.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Extensions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Handles.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.InteropServices.RuntimeInformation.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Numerics.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Formatters.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Json.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Runtime.Serialization.Xml.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Claims.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Algorithms.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Csp.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Encoding.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.Primitives.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Cryptography.X509Certificates.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.Principal.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Security.SecureString.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.Encoding.Extensions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Text.RegularExpressions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Overlapped.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Extensions.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Tasks.Parallel.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Thread.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.ThreadPool.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Threading.Timer.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.ValueTuple.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.ReaderWriter.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XDocument.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlDocument.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XmlSerializer.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/compat/2.1.0/shims/netstandard/System.Xml.XPath.XDocument.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/Extensions/2.0.0/System.Runtime.InteropServices.WindowsRuntime.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/NetStandard/ref/2.1.0/netstandard.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Gradle.dll"
-r:"C:/Program Files/6000.0.44f1/Editor/Data/PlaybackEngines/AndroidPlayer/Unity.Android.Types.dll"
-r:"Library/PackageCache/com.unity.collections@56bff8827a7e/Unity.Collections.LowLevel.ILSupport/Unity.Collections.LowLevel.ILSupport.dll"
-r:"Library/PackageCache/com.unity.ext.nunit@031a54704bff/net40/unity-custom/nunit.framework.dll"
-r:"Library/PackageCache/com.unity.nuget.mono-cecil@d6f9955a5d5f/Mono.Cecil.dll"
-r:"Library/PackageCache/com.unity.nuget.newtonsoft-json@74deb55db2a0/Runtime/Newtonsoft.Json.dll"
-r:"Library/PackageCache/com.unity.testtools.codecoverage@205a02cbcb39/lib/ReportGenerator/ReportGeneratorMerged.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Cinemachine.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/com.unity.cinemachine.editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.arttoolkit.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.arttoolkit.samples.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.files.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.files.samples.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.graphql.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.graphql.samples.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.infinitescroller.editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.infinitescroller.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.infinitescroller.samples.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.promises.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.promises.samples.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.toast.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.toast.samples.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.utilities.editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/genex.utilities.runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Nobi.UiRoundedCorners.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Nobi.UiRoundedCorners.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/PPv2URPConverters.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Tayx.Graphy.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Tayx.Graphy.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.Addressables.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.DOTween.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.Linq.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UniTask.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Common.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Common.Path.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Common.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Sprite.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.SpriteShape.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.SpriteShape.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.2D.Tilemap.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Burst.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Collections.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.EditorCoroutines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InternalAPIEditorBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.InternalAPIEngineBridge.001.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Mathematics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Common.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Multiplayer.Center.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Performance.Profile-Analyzer.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlayerPrefsEditor.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.PlayerPrefsEditor.EditorResources.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rendering.LightTransport.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipeline.Universal.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Editor.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.Runtime.Shared.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Core.ShaderLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.GPUDriven.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.ShaderGraph.ShaderGraphLibrary.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.2D.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Config.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Runtime.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.RenderPipelines.Universal.Shaders.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Rider.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Searcher.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Services.CloudDiagnostics.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Services.CloudDiagnostics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Services.Core.Analytics.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Services.Core.Components.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Services.Core.Environments.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Services.Core.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.ShaderGraph.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Splines.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Splines.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Model.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.OpenCover.Mono.Reflection.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TestTools.CodeCoverage.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.TextMeshPro.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.Timeline.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/Unity.VisualStudio.Editor.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEditor.UI.ref.dll"
-r:"Library/Bee/artifacts/1900b0aEDbg.dag/UnityEngine.UI.ref.dll"
-analyzer:"C:/Program Files/6000.0.44f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.Properties.SourceGenerator.dll"
-analyzer:"C:/Program Files/6000.0.44f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.SourceGenerators.dll"
-analyzer:"C:/Program Files/6000.0.44f1/Editor/Data/Tools/Unity.SourceGenerators/Unity.UIToolkit.SourceGenerator.dll"
"Assets/Scripts/BallManager/Ball/BallComponent.cs"
"Assets/Scripts/BallManager/Ball/BallMovement.cs"
"Assets/Scripts/BallManager/Ball/BallTrail.cs"
"Assets/Scripts/BallManager/Ball/IGolfBall.cs"
"Assets/Scripts/BallManager/Factory/BallFactory.cs"
"Assets/Scripts/BallManager/GolfBallManager.cs"
"Assets/Scripts/BallManager/IGolfBallManager.cs"
"Assets/Scripts/BallManager/StrokeData/Data/BallTrajectoryData.cs"
"Assets/Scripts/BallManager/StrokeData/Data/CoordinateData.cs"
"Assets/Scripts/BallManager/StrokeData/Data/GolferData.cs"
"Assets/Scripts/BallManager/StrokeData/Data/HoleDetailsData.cs"
"Assets/Scripts/BallManager/StrokeData/Data/RadarData.cs"
"Assets/Scripts/BallManager/StrokeData/Data/StrokeData.cs"
"Assets/Scripts/BallManager/StrokeData/Data/TrajectoryData.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/BallTrajectoryDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/CoordinateLocationDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/CoordinatesDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/GolferDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/HoleDetailsDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/StrokeDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/StrokeDataContractWrapper.cs"
"Assets/Scripts/BallManager/StrokeData/DataContracts/TrajectoryDataContract.cs"
"Assets/Scripts/BallManager/StrokeData/IStrokeData.cs"
"Assets/Scripts/BallManager/StrokeData/Queries/NextStrokeQuery.cs"
"Assets/Scripts/BallManager/StrokeData/Utils/TrajectoryUtilities.cs"
"Assets/Scripts/Bootstrap/BootProcesses.cs"
"Assets/Scripts/Bootstrap/BootstrapKeys.cs"
"Assets/Scripts/Bootstrap/BootstrapManager.cs"
"Assets/Scripts/Bootstrap/UrlParamsExtractor.cs"
"Assets/Scripts/CameraSystem/CameraDirector.cs"
"Assets/Scripts/CameraSystem/ICameraDirector.cs"
"Assets/Scripts/CameraSystem/StateMachine/AutomaticCameraSystem.cs"
"Assets/Scripts/CameraSystem/StateMachine/Data/PredictionPayload.cs"
"Assets/Scripts/CameraSystem/StateMachine/Data/PredictionResultPayload.cs"
"Assets/Scripts/CameraSystem/StateMachine/Data/TrackableTargets.cs"
"Assets/Scripts/CameraSystem/StateMachine/ICameraSystem.cs"
"Assets/Scripts/CameraSystem/StateMachine/IStateChanger.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/BallInAirCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/BallLandedCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/Base/BaseCameraState.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/BeforeShotCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/ICameraStateWithPayload.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/LoadingCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/MidPuttShotCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/PredictionCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/PredictionResultCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/States/StartingCamera.cs"
"Assets/Scripts/CameraSystem/StateMachine/Types/CameraStateType.cs"
"Assets/Scripts/Configuration/ConfigurationManager.cs"
"Assets/Scripts/Configuration/Impl/Data/AppIds.cs"
"Assets/Scripts/Configuration/Impl/Data/AWSUrlData.cs"
"Assets/Scripts/Configuration/Impl/Data/BettingAreaData.cs"
"Assets/Scripts/Configuration/Impl/Data/CameraOffsetsData.cs"
"Assets/Scripts/Configuration/Impl/Data/Config.cs"
"Assets/Scripts/Configuration/Impl/Data/ConfigurableValuesData.cs"
"Assets/Scripts/Configuration/Impl/DataContract/AppIdsDataContract.cs"
"Assets/Scripts/Configuration/Impl/DataContract/AWSUrlDataContract.cs"
"Assets/Scripts/Configuration/Impl/DataContract/BettingAreaDataContract.cs"
"Assets/Scripts/Configuration/Impl/DataContract/CameraOffsetsDataContract.cs"
"Assets/Scripts/Configuration/Impl/DataContract/ConfigurableValuesDataContract.cs"
"Assets/Scripts/Configuration/Impl/DataContract/ConfigurationDataContract.cs"
"Assets/Scripts/Configuration/Impl/DataContract/Vector3DataContract.cs"
"Assets/Scripts/Constants/AppConstants.cs"
"Assets/Scripts/Constants/DefaultMessages.cs"
"Assets/Scripts/Data/SessionData.cs"
"Assets/Scripts/Flag/FlagFactory.cs"
"Assets/Scripts/Flag/FlagManager.cs"
"Assets/Scripts/Game/GameManager.cs"
"Assets/Scripts/Game/GameStateType.cs"
"Assets/Scripts/Game/IGameState.cs"
"Assets/Scripts/Game/OverlayUI/GameStateOverlayUI.cs"
"Assets/Scripts/Game/OverlayUI/GameStateUi.cs"
"Assets/Scripts/Game/State/BaseGameState.cs"
"Assets/Scripts/Game/State/PlacingPrediction.cs"
"Assets/Scripts/Game/State/PredictionWindowOpen.cs"
"Assets/Scripts/Game/State/ResetGame.cs"
"Assets/Scripts/Game/State/ShowingScore.cs"
"Assets/Scripts/Game/State/ShowingStroke.cs"
"Assets/Scripts/Game/State/WaitingForPredictionWindow.cs"
"Assets/Scripts/Game/State/WaitingForStroke.cs"
"Assets/Scripts/Headshots/BulkHeadshotTester.cs"
"Assets/Scripts/Headshots/Headshot.cs"
"Assets/Scripts/Headshots/HeadshotService.cs"
"Assets/Scripts/Inputs/MobileInputModel.cs"
"Assets/Scripts/Leaderboard/Data/LeaderboardData.cs"
"Assets/Scripts/Loading/LoadingScreen.cs"
"Assets/Scripts/Login/ForgottenPassword.cs"
"Assets/Scripts/Login/LoginManager.cs"
"Assets/Scripts/Login/ResetLinkPanel.cs"
"Assets/Scripts/Login/State/AuthState.cs"
"Assets/Scripts/Login/State/BaseLoginState.cs"
"Assets/Scripts/Login/State/IAuthState.cs"
"Assets/Scripts/Login/TogglePasswordVisibility.cs"
"Assets/Scripts/Login/UserSignIn.cs"
"Assets/Scripts/Login/UserSignUp.cs"
"Assets/Scripts/Login/Validator/EmailValidator.cs"
"Assets/Scripts/Login/Validator/InputValidationResult.cs"
"Assets/Scripts/Login/Validator/InputValidator.cs"
"Assets/Scripts/Login/Validator/InspectorValidator.cs"
"Assets/Scripts/Login/Validator/PasswordConfirmationCodeValidator.cs"
"Assets/Scripts/Login/Validator/PasswordValidator.cs"
"Assets/Scripts/Login/Validator/UserNameValidator.cs"
"Assets/Scripts/Login/WelcomePanel.cs"
"Assets/Scripts/Network/Auth/AccessTokenReader.cs"
"Assets/Scripts/Network/Auth/AuthClient.cs"
"Assets/Scripts/Network/Auth/AuthenticationResult.cs"
"Assets/Scripts/Network/Auth/AuthErrorParser.cs"
"Assets/Scripts/Network/Auth/AuthResponse.cs"
"Assets/Scripts/Network/Auth/AuthToken.cs"
"Assets/Scripts/Network/Auth/AuthTokenManager.cs"
"Assets/Scripts/Network/Auth/AuthTokens.cs"
"Assets/Scripts/Network/Auth/ITokenProvider.cs"
"Assets/Scripts/Network/Auth/TokenStorage.cs"
"Assets/Scripts/Network/AwsClient.cs"
"Assets/Scripts/Network/DataContract/TournamentDataContract.cs"
"Assets/Scripts/Network/DataContracts/LeaderboardDataContract.cs"
"Assets/Scripts/Network/GraphQLInitialiser.cs"
"Assets/Scripts/Network/Mutations/AddHomePlayerPredictionMutation.cs"
"Assets/Scripts/Network/Mutations/AddHomePlayerPredictionMutationResponse.cs"
"Assets/Scripts/Network/Mutations/BaseMutation.cs"
"Assets/Scripts/Network/Payload/AccessTokenPayload.cs"
"Assets/Scripts/Network/Payload/LoginPayload.cs"
"Assets/Scripts/Network/Payload/LogoutPayload.cs"
"Assets/Scripts/Network/Payload/SignUpPayload.cs"
"Assets/Scripts/Network/Queries/BaseQuery.cs"
"Assets/Scripts/Network/Queries/GetCurrentTournamentQuery.cs"
"Assets/Scripts/Network/Queries/GetLeaderboardQuery.cs"
"Assets/Scripts/Network/Queries/GetPastPredictionScoresQuery.cs"
"Assets/Scripts/Network/Queries/GetPredictionScoreQuery.cs"
"Assets/Scripts/Network/Response/RequestResult.cs"
"Assets/Scripts/Network/Response/Result.cs"
"Assets/Scripts/Network/Response/ServiceError.cs"
"Assets/Scripts/Network/Services/AuthService.cs"
"Assets/Scripts/Network/Services/ImageFetchService.cs"
"Assets/Scripts/Network/Services/LeaderBoardService.cs"
"Assets/Scripts/Network/Services/PredictionScoreService.cs"
"Assets/Scripts/Network/Services/PredictionWindowService.cs"
"Assets/Scripts/Network/Services/StrokeService.cs"
"Assets/Scripts/Network/Services/TournamentService.cs"
"Assets/Scripts/Network/Subscriptions/BaseSubscription.cs"
"Assets/Scripts/Network/Subscriptions/PredictionWindowSubscription.cs"
"Assets/Scripts/Network/Subscriptions/StrokeSubscription.cs"
"Assets/Scripts/Prediction/PredictionArea.cs"
"Assets/Scripts/Prediction/PredictionController.cs"
"Assets/Scripts/Prediction/PredictionDragAnimationHandler.cs"
"Assets/Scripts/Prediction/PredictionManager.cs"
"Assets/Scripts/Prediction/PredictionPinUi.cs"
"Assets/Scripts/Prediction/TerrainColorLibrary.cs"
"Assets/Scripts/Prediction/TerrainType.cs"
"Assets/Scripts/PredictionWindow/Data/PredictionWindowData.cs"
"Assets/Scripts/PredictionWindow/Data/PredictionWindowStatus.cs"
"Assets/Scripts/PredictionWindow/DataContracts/PlayerPredictionDataContract.cs"
"Assets/Scripts/PredictionWindow/DataContracts/PredictionWindowDataContract.cs"
"Assets/Scripts/PredictionWindow/DataContracts/PredictionWindowDataContractWrapper.cs"
"Assets/Scripts/PredictionWindow/PredictionWindowManager.cs"
"Assets/Scripts/PredictionWindow/PredictionWindowTimer.cs"
"Assets/Scripts/Scoring/Data/PastPredictionScoreData.cs"
"Assets/Scripts/Scoring/Data/PastPredictionScoresData.cs"
"Assets/Scripts/Scoring/Data/PredictionScoreData.cs"
"Assets/Scripts/Scoring/DataContracts/PastPredictionScoreDataContract.cs"
"Assets/Scripts/Scoring/DataContracts/PastPredictionScoresDataContract.cs"
"Assets/Scripts/Scoring/DataContracts/PastPredictionScoresDataContractWrapper.cs"
"Assets/Scripts/Scoring/DataContracts/PredictionScoreDataContract.cs"
"Assets/Scripts/Scoring/DataContracts/PredictionScoreDataContractWrapper.cs"
"Assets/Scripts/Scoring/ScoreManager.cs"
"Assets/Scripts/Stroke/StrokeManager.cs"
"Assets/Scripts/TabMenu/ContainerManager.cs"
"Assets/Scripts/TabMenu/Containers/PlayContainer.cs"
"Assets/Scripts/TabMenu/Containers/PlayContainer/PlayTabOpenPWScreen.cs"
"Assets/Scripts/TabMenu/Containers/RankingContainer.cs"
"Assets/Scripts/TabMenu/Containers/RankingContainer/PlayerColorLibrary.cs"
"Assets/Scripts/TabMenu/Containers/RankingContainer/RankingCell.cs"
"Assets/Scripts/TabMenu/Containers/ResultsContainer.cs"
"Assets/Scripts/TabMenu/Containers/ResultsContainer/PastResultRow.cs"
"Assets/Scripts/TabMenu/Containers/ResultsContainer/PastResultsRenderer.cs"
"Assets/Scripts/TabMenu/Data/RankingData.cs"
"Assets/Scripts/TabMenu/ITabContainer.cs"
"Assets/Scripts/TabMenu/TabContainer.cs"
"Assets/Scripts/TabMenu/TabMenuAnimator.cs"
"Assets/Scripts/TabMenu/TabMenuBackButton.cs"
"Assets/Scripts/TabMenu/TabMenuIndicator.cs"
"Assets/Scripts/TabMenu/TabMenuItem.cs"
"Assets/Scripts/TabMenu/TabMenuManager.cs"
"Assets/Scripts/TabMenu/TabType.cs"
"Assets/Scripts/Testing/DebugMenu/DebugMenuController.cs"
"Assets/Scripts/Testing/PredictionWindow/PredictionWindowApiTester.cs"
"Assets/Scripts/Testing/PredictionWindow/PredictionWindowApiTesterData.cs"
"Assets/Scripts/Testing/Scoring/PastPredictionScoresDataInput.cs"
"Assets/Scripts/Testing/Scoring/PredictionScoreDataInput.cs"
"Assets/Scripts/Testing/Scoring/PredictionScoreTester.cs"
"Assets/Scripts/Testing/Scoring/PredictionScoreTesterData.cs"
"Assets/Scripts/Testing/StrokeApi/StrokeApiTester.cs"
"Assets/Scripts/Testing/StrokeApi/StrokeApiTesterData.cs"
"Assets/Scripts/UI/Carousel/CarouselCloseButton.cs"
"Assets/Scripts/UI/Carousel/CarouselController.cs"
"Assets/Scripts/UI/Carousel/CarouselDot.cs"
"Assets/Scripts/UI/Settings/UISignOutButton.cs"
"Assets/Scripts/UI/Settings/UIUsernameText.cs"
"Assets/Scripts/UI/UIOpenPanelButton.cs"
"Assets/Scripts/UI/UIPanel.cs"
"Assets/Scripts/UI/UITouchable.cs"
"Assets/Scripts/Utilities/HierarchyExporter.cs"
"Assets/Scripts/Utilities/QuickHierarchyExport.cs"
"Assets/Scripts/World/WorldManager.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModuleAudio.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModuleEPOOutline.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModulePhysics.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModulePhysics2D.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModuleSprite.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModuleUI.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModuleUnityVersion.cs"
"Assets/ThirdParty/DOTween/Modules/DOTweenModuleUtils.cs"
"Assets/Visuals/IgniteCoders/Simple Water Shader/Resources/WaterReflection.cs"
-langversion:9.0
/deterministic
/optimize-
/debug:portable
/nologo
/RuntimeMetadataVersion:v4.0.30319
/nowarn:0169
/nowarn:0649
/nowarn:0282
/nowarn:1701
/nowarn:1702
/utf8output
/preferreduilang:en-US
/additionalfile:"Library/Bee/artifacts/1900b0aEDbg.dag/Assembly-CSharp.UnityAdditionalFile.txt"