using Configuration.Impl.DataContract;

namespace Configuration.Impl.Data
{
    public class Config
    {
        public AWSUrlData AWSUrlData { get; private set; }
        public ConfigurableValuesData ConfigurableValues { get; private set; }
        public BettingAreaData[] BettingAreas { get; private set; }
        public AppIds AppIds { get; private set; } 

        public Config(ConfigurationDataContract dataContract)
        {
            AWSUrlData = new AWSUrlData(dataContract.AWSUrls);
            ConfigurableValues = new ConfigurableValuesData(dataContract.ConfigurableValues);

            BettingAreas = new BettingAreaData[dataContract.BettingAreas.Length];
            for (var i = 0; i < dataContract.BettingAreas.Length; i++)
            {
                BettingAreas[i] = new BettingAreaData(dataContract.BettingAreas[i]);
            }

            AppIds = new AppIds(dataContract.AppIds);
        }
    }
}