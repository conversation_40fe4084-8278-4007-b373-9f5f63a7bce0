import { AwsRum, type AwsRumConfig, TelemetryEnum } from "aws-rum-web";
import { useEffect } from "react";
import { configService } from "./config";

const region = configService.get("RumRegion");
const identityPoolId = configService.get("RumIdentityPoolId");
const appMonitorId = configService.get("RumAppMonitorId");
const rumProxyUrl = configService.get("RumProxyUrl");
const appVersion = "1.0.0";
let awsRum: AwsRum;

try {
  const config: AwsRumConfig = {
    sessionSampleRate: 1,
    identityPoolId,
    endpoint: rumProxyUrl,
    telemetries: [
      TelemetryEnum.Performance,
      [
        TelemetryEnum.Errors,
        {
          stackTraceLength: 500,
          ignore: (errorEvent: Error) => {
            return (
              errorEvent &&
              errorEvent.message &&
              /^Warning:/.test(errorEvent.message)
            );
          },
        },
      ],
      [
        TelemetryEnum.Http,
        { addXRayTraceIdHeader: true, stackTraceLength: 500 },
      ],
    ],
    allowCookies: true,
    enableXRay: true,
    retries: 3,
    useBeacon: false,
    disableAutoPageView: true,
    userIdRetentionDays: 30,
  };

  awsRum = new AwsRum(appMonitorId, appVersion, region, config);
  console.log("user monitoring initialized");
} catch (error) {
  console.error("user monitoring initialization failed", error);
  // Ignore errors thrown during CloudWatch RUM web client initialization
}

export const getAwsRum = () => {
  return awsRum;
};

export const RecordPageView = (page: string) => {
  useEffect(() => {
    getAwsRum().recordPageView(page);
  }, [location]);
};
