using Configuration.Impl.DataContract;

namespace Configuration.Impl.Data
{
    public class ConfigurableValuesData
    {
        public int RefreshTokenValidityDays { get; }
        public float LiveInterval { get; }
        public float LeaderboardInterval { get; }
        public float CourseMapHeight { get; }
        public float BettingAreaHeight { get; }
        public CameraOffsetsData CameraOffsets { get; }

        public ConfigurableValuesData(ConfigurableValuesDataContract dataContract)
        {
            RefreshTokenValidityDays = dataContract.RefreshTokenValidityDays;
            LiveInterval = dataContract.LiveInterval;
            LeaderboardInterval = dataContract.LeaderboardInterval;
            CourseMapHeight = dataContract.CourseMapHeight;
            BettingAreaHeight = dataContract.BettingAreaHeight;
            CameraOffsets = new CameraOffsetsData(dataContract.CameraOffsets);
        }
    }
}
