using System.Collections.Generic;
using JetBrains.Annotations;
using Newtonsoft.Json;

namespace BallManager.StrokeData.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class BallTrajectoryDataContract
    {
        [JsonProperty("xFit")] 
        [CanBeNull] 
        public List<float> XFit;

        [JsonProperty("yFit")]
        [CanBeNull] 
        public List<float> YFit;

        [JsonProperty("zFit")]
        [CanBeNull] 
        public List<float> ZFit;

        [JsonProperty("timeInterval")]
        [CanBeNull] 
        public List<float> TimeInterval;
    }
}
