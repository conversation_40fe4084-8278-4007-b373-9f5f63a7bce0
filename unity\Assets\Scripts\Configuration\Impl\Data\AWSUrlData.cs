using Configuration.Impl.DataContract;

namespace Configuration.Impl.Data
{
    public class AWSUrlData
    {
        public string GraphQlUrl { get; }
        public string WebsocketUrl { get; }
        public string LeaderboardUrl { get; }
        public string HeadshotsUrl { get; }

        public AWSUrlData(AWSUrlDataContract dataContract)
        {
            GraphQlUrl = dataContract.GraphQlUrl;
            WebsocketUrl = dataContract.WebsocketUrl;
            LeaderboardUrl = dataContract.LeaderboardUrl;  
            HeadshotsUrl = dataContract.HeadshotsUrl;
        }
    }
}
