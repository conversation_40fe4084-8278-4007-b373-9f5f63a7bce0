using System.Threading;
using Headshots;
using PredictionWindow;
using Sky.GenEx.ArtToolKit;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

namespace Game
{
    public class GameStateOverlayUI : MonoBehaviour
    {
        [SerializeField] private GameManager gameManager;
        [SerializeField] private PredictionWindowManager predictionWindowManager;
        
        [SerializeField] private GameObject container;
        
        [SerializeField] private TMP_Text playerNameText;
        [SerializeField] private TMP_Text holeAndParText;
        [SerializeField] private Image headshotImage;
        [SerializeField] private CanvasGroupAnimator mainAnimator;
        [SerializeField] private CanvasGroupAnimator tipAnimator;
        [SerializeField] private Button makePredictionButton;
        [SerializeField] private Button continueButton;

        private CancellationTokenSource animationCancellationToken;

        private void Start()
        {
            gameManager.OnStateChanged += OnGameStateChange;
        }

        private void OnDestroy()
        {
            
            gameManager.OnStateChanged -= OnGameStateChange;
        }

        public void Show(bool show)
        {
            container.SetActive(show);
        }

        public void SetPredictionButtonInteractable(bool interactable)
        {
            makePredictionButton.interactable = interactable;
        }
        
        private void OnGameStateChange(GameStateType newState)
        {
            switch (newState)
            {
                case GameStateType.PlacingPrediction:
                    ShowPlacingPredictionUI();
                    break;
                case GameStateType.WaitingForStroke:
                    ShowWaitingForStrokeUI();
                    break;
                case GameStateType.ShowingScore:
                    ShowShowingScoreUI();
                    break;
                default:
                    break;
            }
        }

        private void ShowPlacingPredictionUI()
        {
            CancelAnimationToken();
            mainAnimator.Open();
            tipAnimator.Open();
            makePredictionButton.gameObject.SetActive(true);
            continueButton.gameObject.SetActive(false);
            UpdateOverlayText();
            UpdateHeadshotImage();
            SetPredictionButtonInteractable(true);
            _ = WaitForAnimationEnd();
        }

        private void ShowWaitingForStrokeUI()
        {
            CancelAnimationToken();
            mainAnimator.Close();
            tipAnimator.Close();
            makePredictionButton.gameObject.SetActive(false);
            SetPredictionButtonInteractable(false);
            continueButton.gameObject.SetActive(false);
            _ = WaitForAnimationEnd();
        }

        private void ShowShowingScoreUI()
        {
            CancelAnimationToken();
            mainAnimator.Open();
            tipAnimator.Close();
            continueButton.gameObject.SetActive(true);
            continueButton.interactable = true;
            makePredictionButton.gameObject.SetActive(false);
            _ = WaitForAnimationEnd();
        }

        private void UpdateOverlayText()
        {
            var predictionWindowData = predictionWindowManager.CurrentPredictionWindowData;
            if (predictionWindowData == null)
            {
                playerNameText.text = "Unknown Player";
                holeAndParText.text = "Hole N/A · Par N/A";
                return;
            }

            playerNameText.text = predictionWindowData.Golfer.PlayerName.ToUpper();
            holeAndParText.text = $"Hole {predictionWindowData.HoleDetails.Number} · Par {predictionWindowData.HoleDetails.Par}";
        }

        private async void UpdateHeadshotImage()
        {
            headshotImage.gameObject.SetActive(false);
            var predictionWindowData = predictionWindowManager.CurrentPredictionWindowData;
            if (predictionWindowData?.Golfer?.PlayerId == null)
            {
                Debug.LogWarning("No data for golfer available, not setting headshot.");
                return;
            }
            var headshot = await HeadshotService.GetHeadshot(predictionWindowData.Golfer.PlayerId);

            if (headshot?.ProfilePhoto != null)
            {
                headshotImage.sprite = headshot.ProfilePhoto;
                headshotImage.gameObject.SetActive(true);
            }
        }

        private async Awaitable WaitForAnimationEnd()
        {
            animationCancellationToken = new CancellationTokenSource();
            await Awaitable.WaitForSecondsAsync(mainAnimator.AnimationDuration, animationCancellationToken.Token);
            animationCancellationToken.Dispose();
            animationCancellationToken = null;
        }

        private void CancelAnimationToken()
        {
            if (animationCancellationToken != null)
            {
                animationCancellationToken.Cancel();
                animationCancellationToken.Dispose();
                animationCancellationToken = null;
            }
        }
    }
}
