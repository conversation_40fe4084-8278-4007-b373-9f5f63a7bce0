using System;
using BallManager.Ball;
using BallManager.Factory;
using BallManager.StrokeData;
using UnityEngine;

namespace BallManager
{
    public class GolfBallManager : MonoBehaviour, IGolfBallManager
    {
        [SerializeField] private BallComponent ballPrefab;

        public IGolfBall Ball => currentBall;
        
        public event Action StartedMoving;
        public event Action BallLanded;
        public event Action FinishedMoving;

        private BallFactory ballFactory;
        private BallComponent currentBall;
        
        private void InvokeStartedMoving() => StartedMoving?.Invoke();
        private void InvokeBallLanded() => BallLanded?.Invoke();
        private void InvokeFinishedMoving() => FinishedMoving?.Invoke();

        void Start()
        {
            ballFactory = new BallFactory(ballPrefab, gameObject.transform);
        }

        public void GenerateBall(IStrokeData strokeData)
        {
            var ball = ballFactory.GenerateBall(strokeData);
            DestroyBall();
            currentBall = ball;
            
            ball.StartedMoving += InvokeStartedMoving;
            ball.Landed += InvokeBallLanded;
            ball.FinishedMoving += InvokeFinishedMoving;
        }
        
        public void StartStroke()
        {
            Ball.StartShot();
        }
        
        public void DestroyBall()
        {
            if (currentBall != null)
            {
                currentBall.StartedMoving -= InvokeStartedMoving;
                currentBall.Landed -= InvokeBallLanded;
                currentBall.FinishedMoving -= InvokeFinishedMoving;
                Destroy(currentBall.Transform.gameObject);
            }
        }
    }
}