using System;
using BallManager.StrokeData;
using UnityEngine;

namespace BallManager.Ball
{
    public class BallComponent : MonoBehaviour, IGolfBall
    {
        [SerializeField] private BallTrail ballTrail;
        [SerializeField] private BallMovement ballMovement;
        
        public event Action StartedMoving
        {
            add => ballMovement.StartedMoving += value;
            remove => ballMovement.StartedMoving -= value;
        }
        
        public event Action Landed
        {
            add => ballMovement.Landed += value;
            remove => ballMovement.Landed -= value;
        }
        
        public event Action FinishedMoving
        {
            add => ballMovement.FinishedMoving += value;
            remove => ballMovement.FinishedMoving -= value;
        }
        
        private IStrokeData _strokeData;
        
        public Transform Transform => transform;
        public int PlayerId => _strokeData.GolferData.PlayerId;
        public bool HasTrajectory => _strokeData.Radar.IsValid;

        public void Initialize(IStrokeData strokeData)
        {
            this._strokeData = strokeData;
            ballMovement.Initialize(strokeData);
            ballTrail.Initialize();
            
            name = $"Ball {strokeData.GolferData.PlayerId}";
        }
        
        public void StartShot()
        {
            ballMovement.StartShot();
        }
    }
}