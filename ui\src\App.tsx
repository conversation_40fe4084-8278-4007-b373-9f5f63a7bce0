import { Amplify } from "aws-amplify";
import { cognitoUserPoolsTokenProvider } from "aws-amplify/auth/cognito";
import { CookieStorage } from "aws-amplify/utils";
import { ErrorBoundary } from "react-error-boundary";
import { BrowserRouter, Route, Routes } from "react-router";
import { ProtectedRoute } from "./components/ProtectedRoute";
import { RedirectRoute } from "./components/RedirectRoute";
import { Forgot, FTUE, Login, Unity, Welcome, NotFound, Error } from "./pages";
import { configService } from "./services/config";
import { getAwsRum } from "./services/rum";
import { Rules } from "./pages/Rules";

const userPoolClientId = configService.get("UserPoolClientId");
const userPoolId = configService.get("UserPoolId");
const userPoolDomain = configService.get("UserPoolDomain");
const domainName = configService.get("WebappDomainName");
const appEnv = configService.get("AppEnv");

const redirectUrls = [
  `https://${domainName}/`,
  ...(appEnv === "prod" ? [`https://www.${domainName}/`] : []),
  ...(!["staging", "prod"].includes(appEnv) ? ["http://localhost:3000/"] : []),
];

// disable console.log in production
if (process.env.NODE_ENV === "production") console.log = () => {};

Amplify.configure({
  Auth: {
    Cognito: {
      userPoolClientId,
      userPoolId,
      loginWith: {
        oauth: {
          domain: userPoolDomain,
          scopes: [
            "email",
            "openid",
            "profile",
            "aws.cognito.signin.user.admin",
          ],
          redirectSignIn: redirectUrls,
          redirectSignOut: redirectUrls,
          responseType: "code",
        },
      },
    },
  },
});

cognitoUserPoolsTokenProvider.setKeyValueStorage(new CookieStorage());

const handleError = (error: Error) => {
  getAwsRum()?.recordError(error);
};

function App() {
  return (
    <div className="font-sans antialiased h-full w-full">
      <BrowserRouter>
        <ErrorBoundary FallbackComponent={Error} onError={handleError}>
          <Routes>
            <Route
              path="/welcome"
              element={
                <RedirectRoute>
                  <Welcome />
                </RedirectRoute>
              }
            />
            <Route
              path="/login"
              element={
                <RedirectRoute>
                  <Login />
                </RedirectRoute>
              }
            />
            <Route
              path="/"
              element={
                <ProtectedRoute>
                  <Unity />
                </ProtectedRoute>
              }
            />
            <Route
              path="/ftue"
              element={
                <RedirectRoute>
                  <FTUE />
                </RedirectRoute>
              }
            />
            <Route
              path="/forgot"
              element={
                <RedirectRoute>
                  <Forgot />
                </RedirectRoute>
              }
            />
            <Route path="/rules" element={<Rules />} />
            <Route path="*" element={<NotFound />} />
          </Routes>
        </ErrorBoundary>
      </BrowserRouter>
    </div>
  );
}

export default App;
