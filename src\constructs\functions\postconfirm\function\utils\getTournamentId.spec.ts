import { getTournamentId } from "./getTournamentId";
import { sendGraphQLRequest } from "@/utils/graphql";

jest.mock("@/utils/graphql", () => ({
  sendGraphQLRequest: jest.fn(),
}));

describe("publishSignUpSuccess", () => {
  const props = {
    apiUrl: "https://api.example.com/graphql",
    apiId: "api-123",
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should get the tournament id", async () => {
    (sendGraphQLRequest as jest.Mock).mockResolvedValue({
      data: {
        getCurrentTournament: "R2025011",
      },
    });
    const tournamentId = await getTournamentId(props);
    expect(sendGraphQLRequest).toHaveBeenCalledWith(
      expect.objectContaining({
        apiUrl: props.apiUrl,
        apiId: props.apiId,
        body: expect.objectContaining({
          operationName: "GetCurrentTournament",
        }),
      }),
    );
    expect(tournamentId).toBe("R2025011");
  });
});
