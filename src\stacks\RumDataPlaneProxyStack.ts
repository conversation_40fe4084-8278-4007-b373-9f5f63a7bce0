import * as cloudfront from "aws-cdk-lib/aws-cloudfront";
import * as cloudfrontOrigins from "aws-cdk-lib/aws-cloudfront-origins";
import * as cdk from "aws-cdk-lib/core";
import { Construct } from "constructs";

type RumDataPlaneProxyApiStackProps = cdk.NestedStackProps &
  Readonly<{
    stage: string;
    webappRegion: string;
  }>;

export class RumDataPlaneProxyStack extends cdk.NestedStack {
  readonly rumProxyUrl: string;

  constructor(
    scope: Construct,
    id: string,
    props: RumDataPlaneProxyApiStackProps,
  ) {
    super(scope, id, props);

    const { webappRegion } = props;

    const rumDataPlaneDomain = `dataplane.rum.${webappRegion}.amazonaws.com`;

    const distribution = new cloudfront.Distribution(
      this,
      "CloudFrontDistribution",
      {
        defaultBehavior: {
          origin: new cloudfrontOrigins.HttpOrigin(rumDataPlaneDomain),
          allowedMethods: cloudfront.AllowedMethods.ALLOW_ALL,
          viewerProtocolPolicy: cloudfront.ViewerProtocolPolicy.HTTPS_ONLY,
          cachePolicy: cloudfront.CachePolicy.CACHING_DISABLED,
          originRequestPolicy: cloudfront.OriginRequestPolicy.ALL_VIEWER,
        },
        minimumProtocolVersion: cloudfront.SecurityPolicyProtocol.TLS_V1_2_2021,
        geoRestriction: cloudfront.GeoRestriction.allowlist("US", "GB"),
      },
    );

    this.rumProxyUrl = `https://${distribution.distributionDomainName}`;
  }
}
