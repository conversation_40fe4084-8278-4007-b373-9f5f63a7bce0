namespace Network.Queries
{
    public class GetLeaderboardQuery : BaseQuery
    {
        public override string QueryString => queryString;
        public override string Name => "GetLeaderboardQuery";

        private string queryString;

        public GetLeaderboardQuery(string tournamentId, string userId, int limit = 10)
        {
            queryString = $@"
                query {Name} {{
                    leaderboardByUserId(gameId: ""{tournamentId}"", limit: {limit}, userId: ""{userId}"") {{
                        prev {{
                            rank
                            score
                            userName
                        }},
                        current {{
                            rank
                            score
                            userName
                        }},
                        next {{
                            rank
                            score
                            userName
                        }}
                    }}
                }}
            ";
        }
    }
}