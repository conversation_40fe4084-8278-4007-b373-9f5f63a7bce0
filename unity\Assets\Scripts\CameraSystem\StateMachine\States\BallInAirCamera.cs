using App.CameraSystem.StateMachine.States.Base;
using BallManager.Ball;

namespace App.CameraSystem.StateMachine.States
{
	public class BallInAirCamera : BaseCameraState, ICameraStateWithPayload<IGolfBall>
	{
		private IGolfBall golfBall;

		public void SetPayload(IGolfBall ball)
		{
			golfBall = ball;
			StateCamera.LookAt = ball.Transform;
		}

		public override void Enter()
		{
			StateCamera.Follow = golfBall.Transform;
			StateCamera.LookAt = golfBall.Transform;
		}
		
		public override void Exit()
		{
		}
	}
}