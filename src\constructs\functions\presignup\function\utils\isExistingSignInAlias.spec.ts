import { DynamoDBClient, QueryCommand } from "@aws-sdk/client-dynamodb";
import { mockClient } from "aws-sdk-client-mock";
import { isExistingSignInAlias } from "./isExistingSignInAlias";

jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
const ddbMock = mockClient(DynamoDBClient);

describe("isExistingSignInAlias", () => {
  const props = {
    tableName: "TestTable",
    signInAlias: "alias-abc",
  };

  beforeEach(() => {
    ddbMock.reset();
  });

  it("should return true if alias exists (Count > 0)", async () => {
    ddbMock.on(QueryCommand).resolves({ Count: 1 });
    const result = await isExistingSignInAlias(props);
    expect(result).toBe(true);
  });

  it("should return false if alias does not exist (Count = 0)", async () => {
    ddbMock.on(QueryCommand).resolves({ Count: 0 });
    const result = await isExistingSignInAlias(props);
    expect(result).toBe(false);
  });

  it("should throw and log on error", async () => {
    ddbMock.on(QueryCommand).rejects(new Error("fail"));
    await expect(isExistingSignInAlias(props)).rejects.toThrow(
      "isExistingSignInAlias failed",
    );
  });
});
