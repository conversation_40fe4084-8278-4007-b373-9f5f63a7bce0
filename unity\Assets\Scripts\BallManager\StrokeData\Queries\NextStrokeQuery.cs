namespace BallManager.StrokeData.Queries
{
    public class NextStrokeQuery
    {
        public static string GetNextStrokeQuery()
        {
            return $@"subscription NextStrokeSubscription {{
                          onNextStroke {{
                            distance
                            from {{
                              code
                              text
                              x
                              y
                              z
                            }}
                            golferDetails {{
                              country
                              courseLocation
                              golferId
                              groupNumber
                              name
                            }}
                            groupNumber
                            holeDetails {{
                              centerOfFairway {{
                                x
                                y
                                z
                              }}
                              holeNumber
                              pin {{
                                x
                                y
                                z
                              }}
                              tee {{
                                x
                                y
                                z
                              }}
                            }}
                            inTheHole
                            roundNumber
                            strokeId
                            strokeNumber
                            to {{
                              code
                              text
                              x
                              y
                              z
                            }}
                            tournamentId
                            trail {{
                              actualFlightTime
                              apexHeight
                              ballSpeed
                              ballTrajectory {{
                                measuredTimeInterval
                                spinRateFit
                                timeInterval
                                validTimeInterval
                                xFit
                                yFit
                                zFit
                              }}
                              horizontalLaunchAngle
                              launchSpin
                              verticalLaunchAngle
                            }}
                            type
                          }}
                        }}";
        }
    }
}