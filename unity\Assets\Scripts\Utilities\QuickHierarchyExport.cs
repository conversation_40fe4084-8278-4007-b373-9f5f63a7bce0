using UnityEngine;

namespace Utilities
{
    public class QuickHierarchyExport : MonoBeh<PERSON><PERSON>
    {
        [Header("Quick Export")]
        [SerializeField] private KeyCode exportKey = KeyCode.F12;
        
        private void Update()
        {
            if (Input.GetKeyDown(exportKey))
            {
                ExportToClipboard();
            }
        }
        
        [ContextMenu("Quick Export to Clipboard")]
        public void ExportToClipboard()
        {
            var exporter = FindObjectOfType<HierarchyExporter>();
            if (exporter == null)
            {
                // Create temporary exporter
                GameObject temp = new GameObject("TempExporter");
                exporter = temp.AddComponent<HierarchyExporter>();
                exporter.ExportHierarchy();
                DestroyImmediate(temp);
            }
            else
            {
                exporter.ExportHierarchy();
            }
        }
    }
}