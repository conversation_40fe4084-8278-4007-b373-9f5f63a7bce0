using UnityEditor;
using UnityEditor.Build.Reporting;
using UnityEngine;
using System.Collections.Generic;

namespace Editor
{
	public class BuildScript
	{
		private static void PerformBuild(BuildTarget buildTarget)
		{
			var buildPlayerOptions = new BuildPlayerOptions
			{
				scenes = GetCurrentScenesToBuild(),
				locationPathName = buildTarget == BuildTarget.WebGL ? $"./Build/WebGL" : $"./Build",
				target = buildTarget
			};
			
			BuildReport report = BuildPipeline.BuildPlayer(buildPlayerOptions);
			BuildSummary summary = report.summary;

			if (summary.result == BuildResult.Succeeded)
			{
				Debug.Log($"Build succeeded: " + summary.totalSize + " bytes");
			}

			if (summary.result == BuildResult.Failed)
			{
				Debug.Log($"Build failed");
			}
		}

		// Using separate methods as parameters not supported for cli build command
		public static void PerformBuildWebGL() => PerformBuild(BuildTarget.WebGL);
		public static void PerformBuildAndroid() => PerformBuild(BuildTarget.Android);
		
		
		public static string[] GetCurrentScenesToBuild()
		{
			var scenesToBuild = new List<string>();
			foreach (EditorBuildSettingsScene scene in EditorBuildSettings.scenes)
			{
				if (scene.enabled)
				{
					scenesToBuild.Add(scene.path);
				}
			}
			return scenesToBuild.ToArray();
		}
	}
}
