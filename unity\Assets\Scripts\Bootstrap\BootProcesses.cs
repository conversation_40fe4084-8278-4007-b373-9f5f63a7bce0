using System;
using Configuration;
using Data;
using Network;
using Network.Services;
using UnityEngine;

namespace Bootstrap
{
    public static class BootProcesses
    {
        private static bool deviceIDExtracted = false;

        public static async Awaitable StartNetworkProcesses()
        {
            try
            {
                Debug.Log("[BootProcesses] Starting network processes...");
                if(!deviceIDExtracted)
                {
                    ExtractDeviceId();
                }
                InitializeGraphQl();
                await RetrieveTournamentId();
                SubscribeToGolfApi();
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                throw;
            }
        }
        
        public static void ExtractDeviceId()
        {
            deviceIDExtracted = UrlParamsExtractor.Extract();

            if (!deviceIDExtracted)
            {
                throw new InvalidOperationException("No device id found in the provided url parameters.");
            }

            Debug.Log("[BootProcesses] Device ID extracted successfully.");
        }

        private static void InitializeGraphQl()
        {
            if (!deviceIDExtracted)
            {
                deviceIDExtracted = UrlParamsExtractor.Extract();
            }

            if (deviceIDExtracted)
            {
                GraphQLInitialiser.InitialiseClients();
                Debug.Log("[BootProcesses] GraphQL clients initialized successfully.");
            }
            else
            {
                throw new Exception("No device id found in the provided url parameters.");
            }

            Debug.Log("[BootProcesses] GraphQL clients initialized successfully.");
        }

        private static void SubscribeToGolfApi()
        {
            try
            {
                GraphQLInitialiser.SubscribeToGolfApi();
                Debug.Log("[BootProcesses] Subscribed to Golf API successfully.");
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                throw;
            }
        }

        private static async Awaitable RetrieveTournamentId()
        {
            try
            {
                var tournamentIdRequest = await TournamentService.GetCurrentTournamentId();
                if (tournamentIdRequest.IsSuccess)
                {
                    SessionData.SetTournamentId(tournamentIdRequest.Value);
                }
                else
                {
                    throw new Exception("No tournament ID found");
                }
            }
            catch (Exception e)
            {
                throw new Exception("Error retrieving tournament ID: " + e.Message, e);
            }
        }

        public static async Awaitable AsyncLoadConfig()
        {
            var finished = false;
            ConfigurationManager
                .LoadConfig()
                .Then(() => { finished = true; });

            while (!finished)
            {
                await Awaitable.NextFrameAsync();
            }
        }
    }
}