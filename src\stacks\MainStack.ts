import {
  BaseMainStack,
  CfnExportNamePrefixAspect,
  // isDeploymentStageProd,
  getConfigValue,
  isDeploymentStageProd,
  type MainStackProps,
  RemovalPolicyAspect,
  type Tag,
} from "@comcast/gee-aws-cdk-shared";
import { type IAspect } from "aws-cdk-lib";
import * as cdk from "aws-cdk-lib";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as cr from "aws-cdk-lib/custom-resources";
import { Construct } from "constructs";
// import { DomainStack } from "./DomainStack";
import { RumDataPlaneProxyStack } from "./RumDataPlaneProxyStack";
import { WebappHostingStack } from "./WebappHostingStack";
import { Config } from "@/types/config";
import { getAccountStage } from "@/utils/getAccountStage";

type StackProps = MainStackProps & {
  webappRegion: string;
};

export class MainStack extends BaseMainStack {
  constructor(scope: Construct, id: string, props: StackProps) {
    super(scope, id, props);
    const { stage, webappRegion } = props;

    const importStage = getAccountStage(this.account, stage);
    const leaderboardApiUrl = getConfigValue<string>(
      scope,
      importStage,
      Config.LeaderboardApiUrl,
    );
    const golfApiUrl = getConfigValue<string>(
      scope,
      importStage,
      Config.GolfApiUrl,
    );
    const golfApiWsUrl = getConfigValue<string>(
      scope,
      importStage,
      Config.GolfApiWsUrl,
    );

    let domainName: string | undefined = undefined;
    let certificate: acm.ICertificate | undefined = undefined;
    // NOTE: Disabling certificate until we have a domain
    if (isDeploymentStageProd(stage)) {
      // const domainStack = new DomainStack(this, DomainStack.name);
      domainName = "calltheshot.btv.gpo.sky";
      certificate = acm.Certificate.fromCertificateArn(
        this,
        "WebappCertificate",
        getConfigValue(this, stage, "certificateArn"),
      );
    }

    const { rumProxyUrl } = new RumDataPlaneProxyStack(
      this,
      RumDataPlaneProxyStack.name,
      {
        stage,
        webappRegion,
      },
    );

    const {
      originBucket,
      distribution,
      domainName: cfDomainName,
    } = new WebappHostingStack(this, WebappHostingStack.name, {
      stage,
      leaderboardApiUrl,
      golfApiUrl,
      golfApiWsUrl,
      webappRegion,
      domainName,
      certificate,
      rumProxyUrl,
    });

    const outputs: [string, string][] = [
      ["WebappBucketName", originBucket.bucketName],
      ["WebappDistributionId", distribution.distributionId],
      ["WebappDomainName", domainName ?? cfDomainName],
      ["CloudFrontDomainName", cfDomainName],
      ["RumProxyUrl", rumProxyUrl],
      // NOTE: Disabling certificate until we have a domain
      ["CertificateArn", "false"],
    ];

    outputs.forEach(([outputName, value]) => {
      new cr.AwsCustomResource(this, `CrossRegionSSMWriter-${outputName}`, {
        onCreate: {
          service: "SSM",
          action: "putParameter",
          parameters: {
            Name: `/golf-app/${stage}/${outputName}`,
            Type: "String",
            Value: value,
            Overwrite: true,
          },
          region: webappRegion,
          physicalResourceId: cr.PhysicalResourceId.of(
            `CrossRegionParameter-golf-app-${stage}-${outputName}`,
          ),
        },
        policy: cr.AwsCustomResourcePolicy.fromSdkCalls({
          resources: [
            `arn:aws:ssm:${webappRegion}:${this.account}:parameter/golf-app/${stage}/${outputName}`,
          ],
        }),
      });
    });

    this.createCfnOutputs(outputs);
  }

  defaultAspects(): IAspect[] {
    const cfnExportNamePrefixAspect = new CfnExportNamePrefixAspect(
      "golf-app",
      this.stage,
    );
    return [cfnExportNamePrefixAspect, new RemovalPolicyAspect(this.stage)];
  }

  private createCfnOutputs(outputs: [string, string][]) {
    return outputs.map(
      ([exportName, value]) =>
        new cdk.CfnOutput(this, exportName, { exportName, value }),
    );
  }

  defaultTags(): Tag[] {
    return [
      {
        key: "project",
        value: "btv-genex-golf-app",
      },
      {
        key: "territory",
        value: "uk",
      },
      {
        key: "billing_team",
        value: "gpds",
      },
      {
        key: "service",
        value: "btv-genex-golf-app",
      },
    ];
  }
}
