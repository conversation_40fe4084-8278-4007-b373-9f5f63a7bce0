using System;
using Constants;
using Login;
using Login.State;
using Login.Validator;
using Network.Services;
using Sky.GenEx.Toast;
using TMPro;
using UnityEngine;
using UnityEngine.UI;

public class ResetLinkPanel : BaseLoginState
{
    private const string ResetLinkMessage = "We sent a verfication code to <b>[EMAIL]</b>.  Please insert it below with a New Password to Reset Password.";
    
    [SerializeField] private InputValidator passwordValidator;
    [SerializeField] private InputValidator confirmationCodeValidator;
    
    [SerializeField] private TextMeshProUGUI resetLinkMessageText;
    [SerializeField] private ForgottenPassword forgottenPasswordPanel;
    [SerializeField] private Button resetPasswordButton;
    [SerializeField] private Button cancelButton;
    [SerializeField] private Button resendEmailButton;

    private void Start()
    {
        if (!InspectorValidator.CheckAssigned(passwordValidator, nameof(passwordValidator), this) ||
            !InspectorValidator.CheckAssigned(confirmationCodeValidator, nameof(confirmationCodeValidator), this) ||
            !InspectorValidator.CheckAssigned(resetLinkMessageText, nameof(resetLinkMessageText), this) ||
            !InspectorValidator.CheckAssigned(forgottenPasswordPanel, nameof(forgottenPasswordPanel), this) ||
            !InspectorValidator.CheckAssigned(resetPasswordButton, nameof(resetPasswordButton), this) ||
            !InspectorValidator.CheckAssigned(cancelButton, nameof(cancelButton), this) ||
            !InspectorValidator.CheckAssigned(resendEmailButton, nameof(resendEmailButton), this))
        {
            return;
        }
    }

    public override void Enter()
    {
        gameObject.SetActive(true);
        
        passwordValidator.ClearAll();
        confirmationCodeValidator.ClearAll();
        
        resetPasswordButton.onClick.AddListener(ResetPassword);
        cancelButton.onClick.AddListener(() => onFinished(AuthState.ForgotPassword));
        resendEmailButton.onClick.AddListener(SendResetLink);
        UpdateInfoMessage();
    }

    private void UpdateInfoMessage()
    {
        string email = forgottenPasswordPanel.EmailAddress;
        resetLinkMessageText.text = ResetLinkMessage.Replace("[EMAIL]", email);
    }

    private void ResetPassword()
    {
        if (resetPasswordButton.interactable)
        {
            InputValidationResult confirmationCodeResult = confirmationCodeValidator.ValidateInputText();
            if (confirmationCodeResult != InputValidationResult.ValidFormat)
                return;
            
            InputValidationResult passwordResult = passwordValidator.ValidateInputText();
            if (passwordResult != InputValidationResult.ValidFormat)
                return;

            ResetPasswordAsync();
        }
    }

    private void SendResetLink()
    {
        if (resendEmailButton.interactable)
        {
            SendResetLinkAsync();
        }
    }

    private async void ResetPasswordAsync()
    {
        try
        {
            resetPasswordButton.interactable = false;
            string email = forgottenPasswordPanel.EmailAddress;
            if (string.IsNullOrEmpty(email))
            {
                Debug.LogError("Email address is not set. Cannot reset password.");
                return;
            }

            var password = passwordValidator.InputText;
            var confirmationCode = confirmationCodeValidator.InputText;
            var sendResetResult = await AuthService.ConfirmForgotPassword(email, password, confirmationCode);
            if (sendResetResult.IsSuccess)
            {
                onFinished(AuthState.SignIn);
                MessageToastController.Instance.ShowToast("Password reset successful. Please login again.");
                Debug.Log("Password successfully reset.");
            }
            else
            {
                Debug.LogError($"Error trying to send reset password link: {sendResetResult.Error.Message}");
                MessageToastController.Instance.ShowToast(sendResetResult.Error.Message, ToastDuration.Long, ToastType.Error);
            }
        }
        catch (Exception e)
        {
            Debug.LogException(e);
            MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrongTryAgain, ToastDuration.Long, ToastType.Error);
            
        }
        finally
        {
            resetPasswordButton.interactable = true;
        }
    }

    private async void SendResetLinkAsync()
    {
        try
        {
            resendEmailButton.interactable = false;

            string email = forgottenPasswordPanel.EmailAddress;
            if (string.IsNullOrEmpty(email))
            {
                Debug.LogError("Email address is not set. Cannot send reset link.");
                return;
            }

            var sendResetResult = await AuthService.SendPasswordResetEmail(email);
            if (sendResetResult.IsSuccess)
            {
                onFinished(AuthState.ResetPasswordConfirmation);
                Debug.Log("Password reset link sent to the provided email.");
                MessageToastController.Instance.ShowToast("A new password reset link has been sent to the provided email.");
            }
            else
            {
                Debug.LogError($"Error trying to send reset link: {sendResetResult.Error.Message}");
                MessageToastController.Instance.ShowToast(sendResetResult.Error.Message, ToastDuration.Long, ToastType.Error);
            }
        }
        catch (Exception ex)
        {
            Debug.LogException(ex);
            MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrongTryAgain, ToastDuration.Long, ToastType.Error);
        }
        finally
        {
            resendEmailButton.interactable = true;
        }
    }

    public override void Exit()
    {
        resetPasswordButton.onClick.RemoveAllListeners();
        cancelButton.onClick.RemoveAllListeners();
        resendEmailButton.onClick.RemoveAllListeners();
        gameObject.SetActive(false);
    }
}
