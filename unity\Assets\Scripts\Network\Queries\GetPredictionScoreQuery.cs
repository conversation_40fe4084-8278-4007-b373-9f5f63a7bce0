using Scoring.Data;

namespace Network.Queries
{
    public class GetPredictionScoreQuery : BaseQuery
    {
        public override string QueryString => queryString;
        public override string Name => "GetPredictionScoreQuery";

        private string queryString;

        public GetPredictionScoreQuery(PredictionScoreDataInput input)
        {
            queryString = $@"
                query GetPredictionScoreQuery {{
                    getHomePredictionScore(input: {{
                        tournamentId: ""{input.TournamentId}"",
                        roundNumber: {input.RoundNumber},
                        holeNumber: {input.HoleNumber},
                        strokeNumber: {input.StrokeNumber},
                        golferId: {input.GolferId},
                        playerId: ""{input.PlayerId}""
                    }}) 
                    {{
                        score
                        golferId
                        holeNumber
                        strokeNumber
                    }}
                }}
            ";
        }
    }
}
