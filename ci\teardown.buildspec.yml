version: 0.2

env:
  shell: bash
  git-credential-helper: yes
  parameter-store:
    GH_TOKEN: /pipeline/githubAccessToken
    ARTIFACTORY_TOKEN: /pipeline/artifactoryToken

phases:
  install:
    runtime-versions:
      nodejs: 22.x
    commands:
      - echo "Configure Sky NPM access."
      - echo "@sky-uk:registry=https://npm.pkg.github.com/" > .npmrc
      - echo "//npm.pkg.github.com/:_authToken=$GH_TOKEN" >> .npmrc
      - echo "@comcast:registry=https://partners.artifactory.comcast.com/artifactory/api/npm/shared-js-libs/" >> .npmrc
      - echo "//partners.artifactory.comcast.com/artifactory/api/npm/shared-js-libs/:_authToken=$ARTIFACTORY_TOKEN" >> .npmrc

      - export PR_ORIGIN=$(echo $CODEBUILD_WEBHOOK_HEAD_REF | awk -F/ '{ printf tolower($3) }')
      - export STAGE=$(echo $PR_ORIGIN | sha1sum | sed 's/[^a-zA-Z]//g' | cut -c1-7)
      - echo "Stage is ${STAGE}"

      - npm install -g bun
      - bun -v
      - bun install --frozen-lockfile
      - (cd ui && bun install --frozen-lockfile)
  build:
    commands:
      # teardown
      - echo "Starting teardown for ${STAGE}"
      - export INLINE_SCRIPT_CSP_HASH=placeholder #added because it's required by the cdk destroy command
      - mkdir -p ./UnityBuildOutput # empty but required for cdk destroy to work
      - bunx cdk destroy --all --require-approval never --force --context stage=${STAGE}
