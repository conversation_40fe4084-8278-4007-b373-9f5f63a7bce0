using BallManager.Ball;
using BallManager.StrokeData;
using UnityEngine;

namespace BallManager.Factory
{
	public class BallFactory
	{
		private readonly BallComponent prefab;
		private readonly Transform container;
		
		public BallFactory(BallComponent ballPrefab, Transform ballContainer)
		{
			prefab = ballPrefab;
			container = ballContainer;
		}

		public BallComponent GenerateBall(IStrokeData strokeDataData)
		{
			var ball = Object.Instantiate(prefab, strokeDataData.BallStartingPosition, Quaternion.identity,container);
			ball.Initialize(strokeDataData);
			return ball;
		}
	}
}
