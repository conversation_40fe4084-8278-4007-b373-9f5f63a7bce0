import { PostConfirmationTriggerEvent } from "aws-lambda";
import { baseHandler } from "./baseHandler";
import { addUserToLeaderboard } from "./utils/addUserToLeaderboard";
import { getTournamentId } from "./utils/getTournamentId";
import { incrementHouseholdSize } from "./utils/incrementHouseholdSize";
import { publishSignUpSuccess } from "./utils/publishSignUpSuccess";

jest.mock("./utils/incrementHouseholdSize");
jest.mock("./utils/publishSignUpSuccess");
jest.mock("./utils/addUserToLeaderboard");
jest.mock("./utils/getTournamentId");
jest.mock("@sky-uk/coins-utilities", () => ({
  getEnvironmentVariable: jest.fn((key) => {
    const env: Record<string, string> = {
      LEADERBOARD_API_URL: "https://api.example.com/graphql",
      LEADERBOARD_API_ID: "api-123",
      TOURNAMENT_ID: "********",
      GOLF_API_URL: "https://api.example.com/graphql",
      GOLF_API_ID: "api-123",
      HOUSEHOLD_TABLE_NAME: "TestTable",
      MAX_HOUSEHOLD_SIZE: "5",
      APP_VARIANT: "home",
    };
    return env[key] || "";
  }),
}));
jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
jest.mock("@sky-uk/coins-utilities");

describe("home - baseHandler", () => {
  const event = {
    triggerSource: "PostConfirmation_ConfirmSignUp",
    userName: "testuser",
    request: {
      userAttributes: {
        "custom:deviceId": "device-1",
        email: "<EMAIL>",
        sub: "user-1",
      },
    },
  } as unknown as PostConfirmationTriggerEvent;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it("should confirm signup when incrementHouseholdSize and publishSignUpSuccess resolves", async () => {
    (incrementHouseholdSize as jest.Mock).mockResolvedValue(undefined);
    (publishSignUpSuccess as jest.Mock).mockResolvedValue(undefined);
    const result = await baseHandler(event);
    expect(incrementHouseholdSize).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceId: "device-1",
        signInAlias: expect.any(String),
        tableName: expect.any(String),
        maxHouseholdSize: expect.any(Number),
      }),
    );
    expect(publishSignUpSuccess).toHaveBeenCalledWith(
      expect.objectContaining({
        deviceId: "device-1",
        userId: "user-1",
        apiId: expect.any(String),
        apiUrl: expect.any(String),
      }),
    );
    expect(result).toBe(event);
  });

  it("should throw if incrementHouseholdSize fails", async () => {
    (incrementHouseholdSize as jest.Mock).mockRejectedValue(new Error("fail"));
    await expect(baseHandler(event)).rejects.toThrow(
      ": Unable to confirm user",
    );
  });

  it("should throw if publishSignUpSuccess fails", async () => {
    (incrementHouseholdSize as jest.Mock).mockResolvedValue(undefined);
    (publishSignUpSuccess as jest.Mock).mockRejectedValue(new Error("fail"));
    await expect(baseHandler(event)).rejects.toThrow(
      ": Unable to publish sign up success",
    );
  });

  it("should return the expected forgot password event when trigger is PostConfirmation_ConfirmForgotPassword", async () => {
    const forgotPasswordEvent = {
      ...event,
      triggerSource: "PostConfirmation_ConfirmForgotPassword",
    } as PostConfirmationTriggerEvent;
    const response = await baseHandler(forgotPasswordEvent);
    expect(response).toEqual(forgotPasswordEvent);
  });

  it("should throw if userName is missing", async () => {
    const missingUserName = { ...event, userName: "" };
    await expect(baseHandler(missingUserName)).rejects.toThrow(
      ": Missing username",
    );
  });

  it("should throw if deviceId is missing", async () => {
    const missingDeviceId = {
      ...event,
      request: {
        userAttributes: {
          ...event.request.userAttributes,
          "custom:deviceId": "",
        },
      },
    };
    await expect(baseHandler(missingDeviceId)).rejects.toThrow(
      ": Missing deviceId",
    );
  });

  it("should throw if email is missing", async () => {
    const missingEmail = {
      ...event,
      request: {
        userAttributes: { ...event.request.userAttributes, email: "" },
      },
    };
    await expect(baseHandler(missingEmail)).rejects.toThrow(": Missing email");
  });

  it("should throw if userId is missing", async () => {
    const missingUserId = {
      ...event,
      request: {
        userAttributes: { ...event.request.userAttributes, sub: "" },
      },
    };
    await expect(baseHandler(missingUserId)).rejects.toThrow(
      ": Missing userId",
    );
  });

  it("should not add user to leaderboard", async () => {
    (incrementHouseholdSize as jest.Mock).mockResolvedValue(undefined);
    (publishSignUpSuccess as jest.Mock).mockResolvedValue(undefined);
    (getTournamentId as jest.Mock).mockResolvedValue("********");
    (addUserToLeaderboard as jest.Mock).mockResolvedValue(undefined);
    const result = await baseHandler(event);
    expect(addUserToLeaderboard).not.toHaveBeenCalled();
    expect(result).toBe(event);
  });
});
