using Data;
using TMPro;
using UnityEngine;

namespace UI.Settings
{
    public class UIUsernameText : MonoBehaviour
    {
        [SerializeField]
        private TextMeshProUGUI usernameText;
        [SerializeField]
        private string template;
        
        private void OnEnable()
        {
            SetUsernameText();
        }

        private void SetUsernameText()
        {
            if (usernameText == null)
                return;

            string username = SessionData.UserName;
            if (!string.IsNullOrEmpty(template))
            {
                username = string.Format(template, username);                
            }
            
            usernameText.text = username;
        }
    }
}