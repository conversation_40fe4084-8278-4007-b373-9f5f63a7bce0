using System;
using App.Managers.Gameplay.BettingArea;
using BallManager.StrokeData.Data;
using Configuration;
using Configuration.Impl.Data;
using UnityEngine;
using UnityEngine.Splines;
using UnityEngine.U2D;
using UnityEngine.UI;
using Spline = UnityEngine.U2D.Spline;

namespace Prediction 
{
    public class PredictionArea : MonoBehaviour
    {
        [SerializeField] private Transform holeIndicator;
        [SerializeField] private Sprite[] terrainMapSprites;
        [SerializeField] private Image terrainImage;
        [SerializeField] private RectTransform terrainImageParentTransform;
        [SerializeField] private SpriteShapeController bettingMask;
        [SerializeField] private TerrainColorLibrary terrainColorLibrary;

        private const int ColliderResolution = 100; // The number of points to use to generate the betting surface collider
        private const float TerrainMapShrinkBorders = 0.5f; // Amount to shrink borders to ensure edges of the terrain image are not a valid prediction area
        
        private float predictionAreaHeight;
        private GameObject bettingSurface;

        public void Initialize(HoleDetailsData holeDetails)
        {
            Debug.Log("Initializing Prediction Area for Hole: " + holeDetails.Number);

            BettingAreaData bettingAreaConfig = null;
            foreach (var bettingArea in ConfigurationManager.Configuration.BettingAreas)
            {
                if (bettingArea.HoleNumber == holeDetails.Number)
                {
                    bettingAreaConfig = bettingArea;
                }
            }
            if (bettingAreaConfig == null)
            {
                Debug.LogError($"No betting area configuration found for hole number {holeDetails.Number}");
                bettingAreaConfig = ConfigurationManager.Configuration.BettingAreas[0]; // Fallback to the first betting area if none found
            }
            
            terrainImage.sprite = terrainMapSprites[holeDetails.Number - 1];
            
            predictionAreaHeight = ConfigurationManager.Configuration.ConfigurableValues.BettingAreaHeight;
            transform.position = new Vector3(transform.position.x, predictionAreaHeight, transform.position.z);

            // position the betting area and terrain image according to configuration file
            var widthCenter = (bettingAreaConfig.TerrainCorner1.x + bettingAreaConfig.TerrainCorner2.x) / 2;
            var heightCenter = (bettingAreaConfig.TerrainCorner1.z + bettingAreaConfig.TerrainCorner2.z) / 2;
            bettingMask.gameObject.transform.localPosition = new Vector3(widthCenter, 0, heightCenter);
            terrainImageParentTransform.localPosition = new Vector3(widthCenter, 0, heightCenter);
            
            // shrink borders to make sure the edges of the terrain image are not a valid prediction area
            var shrunkTerrainCorner1 = new Vector2(bettingAreaConfig.TerrainCorner1.x + TerrainMapShrinkBorders, bettingAreaConfig.TerrainCorner1.z - TerrainMapShrinkBorders);
            var shrunkTerrainCorner2 = new Vector2(bettingAreaConfig.TerrainCorner1.x - TerrainMapShrinkBorders, bettingAreaConfig.TerrainCorner2.z - TerrainMapShrinkBorders);
            var shrunkTerrainCorner3 = new Vector2(bettingAreaConfig.TerrainCorner2.x + TerrainMapShrinkBorders, bettingAreaConfig.TerrainCorner1.z + TerrainMapShrinkBorders);
            var shrunkTerrainCorner4 = new Vector2(bettingAreaConfig.TerrainCorner2.x - TerrainMapShrinkBorders, bettingAreaConfig.TerrainCorner2.z + TerrainMapShrinkBorders);
            
            var spline = bettingMask.spline;
            spline.SetPosition(0, new Vector2(shrunkTerrainCorner1.x - widthCenter, shrunkTerrainCorner1.y - heightCenter));
            spline.SetPosition(1, new Vector2(shrunkTerrainCorner2.x - widthCenter, shrunkTerrainCorner2.y - heightCenter));
            spline.SetPosition(2, new Vector2(shrunkTerrainCorner4.x - widthCenter, shrunkTerrainCorner4.y - heightCenter));
            spline.SetPosition(3, new Vector2(shrunkTerrainCorner3.x - widthCenter, shrunkTerrainCorner3.y - heightCenter));
            
            var width = Math.Abs(bettingAreaConfig.TerrainCorner2.x - bettingAreaConfig.TerrainCorner1.x);
            var height = Math.Abs(bettingAreaConfig.TerrainCorner2.z - bettingAreaConfig.TerrainCorner1.z);
            terrainImageParentTransform.sizeDelta = new Vector2(width, height);

            GenerateCollider();
            holeIndicator.position = new Vector3(holeDetails.PinPosition.x , predictionAreaHeight, holeDetails.PinPosition.z);
        }

        public TerrainType WorldToTerrain(Vector3 worldPosition)
        {
            var pixelColor = WorldToPixel(worldPosition);
            return terrainColorLibrary.terrainColors.TryGetValue(pixelColor, out var terrainType) ? terrainType : TerrainType.Rough;
        }
        
        private Color WorldToPixel(Vector3 worldPosition) 
        {
            Rect rect = terrainImage.sprite.rect;
            Vector3 localPositionInImage = terrainImage.transform.InverseTransformPoint(worldPosition);
            Vector2 textureCoord = new Vector2
            (
                (localPositionInImage.x + terrainImage.rectTransform.rect.width / 2) / terrainImage.rectTransform.rect.width,
                (localPositionInImage.y + terrainImage.rectTransform.rect.height / 2) / terrainImage.rectTransform.rect.height
            );
            int pixelX = Mathf.FloorToInt(textureCoord.x * rect.width);
            int pixelY = Mathf.FloorToInt(textureCoord.y * rect.height);

            if (pixelX >= 0 && pixelX < rect.width && pixelY >= 0 && pixelY < rect.height)
            {
                return terrainImage.sprite.texture.GetPixel(pixelX, pixelY);
            } 
            else 
            {
                return Color.clear;
            }
        }

        private void GenerateCollider() 
        {
            // Convert 2D spline to 3D so we can generate a collider for raycasting 
            
            if (bettingSurface != null) 
            {
                Destroy(bettingSurface);
            }
            
            Spline spline2d = bettingMask.spline;
            UnityEngine.Splines.Spline spline3d = new();
            Mesh bettingMesh = new();

            spline3d.Closed = true;
            for (int i = 0; i < spline2d.GetPointCount(); i++) {
                Vector3 point = spline2d.GetPosition(i);
                Vector3 leftTangent = spline2d.GetLeftTangent(i);
                Vector3 rightTangent = spline2d.GetRightTangent(i);
                spline3d.Add(new BezierKnot(new Vector3(point.x, 0, -point.y), new Vector3(leftTangent.x, 0, -leftTangent.y), new Vector3(rightTangent.x, 0, -rightTangent.y)));
            }

            Vector3[] points = new Vector3[ColliderResolution];
            for (int i = 0; i < ColliderResolution; i++) {
                float t = i / (float)ColliderResolution;
                points[i] = spline3d.EvaluatePosition(t);
            }
            bettingMesh.vertices = points;

            int[] triangles = new int[(ColliderResolution - 2) * 3];
            for (int i = 0; i < (ColliderResolution - 2); i++) {
                triangles[i * 3] = 0;
                triangles[i * 3 + 1] = (i + 2) % ColliderResolution;
                triangles[i * 3 + 2] = i + 1;
            }
            bettingMesh.triangles = triangles;
            bettingMesh.RecalculateNormals();

            bettingSurface = new("BettingSurface");
            bettingSurface.transform.SetParent(bettingMask.transform, false);
            bettingSurface.AddComponent<MeshFilter>().mesh = bettingMesh;
            bettingSurface.AddComponent<MeshCollider>().sharedMesh = bettingMesh;
            bettingSurface.layer = LayerMask.NameToLayer("BettingSurface");
            bettingSurface.transform.localEulerAngles = new Vector3(90, 0, 0);
        }
    }
}

