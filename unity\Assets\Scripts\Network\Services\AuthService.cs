using System;
using Network.Auth;
using Network.Response;
using UnityEngine;
using UnityEngine.Networking;

namespace Network.Services
{
    public static class AuthService
    {
        private const int RequestTimeoutSeconds = 30;

        private static async Awaitable<RequestResult> SendRequest(UnityWebRequest request)
        {
            request.timeout = RequestTimeoutSeconds;
            var operation = request.SendWebRequest();
            await operation;
            var response = request.downloadHandler?.text;

            bool success = request.result == UnityWebRequest.Result.Success;

            if (!success)
            {
                if (request.result == UnityWebRequest.Result.ProtocolError)
                {
                    Debug.LogError($"Server Error: {response}");
                }
                else if (request.result == UnityWebRequest.Result.ConnectionError)
                {
                    Debug.LogError($"Connection error: {request.error}");
                }
                else if (request.result == UnityWebRequest.Result.DataProcessingError)
                {
                    Debug.LogError($"Data processing error: {request.error}");
                }
                else
                {
                    Debug.LogError($"Unknown error: {request.error}");
                }
            }

            return new RequestResult(success, response, request);
        }

        public static async Awaitable<Result<AuthTokens>> Login(string username, string password)
        {
            var request = AuthClient.GetLoginRequest(username, password);
            var result = await SendRequest(request);
            if (!result.Success)
            {
                var error = AuthErrorParser.Parse(result.Request.responseCode, result.Response, result.Request.error);
                return Result<AuthTokens>.Failure(error);
            }
            var authResponse = AuthClient.GetAuthResponse(result.Response);
            if (authResponse == null || authResponse.AuthenticationResult == null)
            {
                var error = new ServiceError(-1, "Authentication result is null");
                return Result<AuthTokens>.Failure(error);
            }
            var loginResult = new AuthTokens(authResponse.AuthenticationResult);
            return Result<AuthTokens>.Success(loginResult);
        }
        
        public static async Awaitable<Result> Logout(string accessToken)
        {
            var request = AuthClient.GetLogoutRequest(accessToken);
            var result = await SendRequest(request);
            if (!result.Success)
            {
                var error = AuthErrorParser.Parse(result.Request.responseCode, result.Response, result.Request.error);
                return Result.Failure(error);
            }
            return Result.Success();
        }

        public static async Awaitable<Result> Register(string email, string username, string password)
        {
            var request = AuthClient.GetSignUpRequest(username, password, email);
            var result = await SendRequest(request);
            if (!result.Success)
            {
                var error = AuthErrorParser.Parse(result.Request.responseCode, result.Response, result.Request.error);
                return Result.Failure(error);
            }
            return Result.Success();
        }

        public static async Awaitable<Result<AuthToken>> RefreshAccessToken(string refreshToken)
        {
            var request = AuthClient.GetRefreshAccessTokenRequest(refreshToken);
            var result = await SendRequest(request);
            if (!result.Success)
            {
                var error = AuthErrorParser.Parse(result.Request.responseCode, result.Response, result.Request.error);
                return Result<AuthToken>.Failure(error);
            }
            var authResponse = AuthClient.GetAuthResponse(result.Response);
            if (authResponse == null || authResponse.AuthenticationResult == null)
            {
                var error = new ServiceError(-1, "Authentication result is null");
                return Result<AuthToken>.Failure(error);
            }
            var newAccessToken = new AuthToken(authResponse.AuthenticationResult.AccessToken, DateTime.Now.AddSeconds(authResponse.AuthenticationResult.ExpiresIn));
            return Result<AuthToken>.Success(newAccessToken);
        }

        public static async Awaitable<Result> SendPasswordResetEmail(string email)
        {
            var request = AuthClient.GetForgotPasswordRequest(email);
            var result = await SendRequest(request);
            if (!result.Success)
            {
                var error = AuthErrorParser.Parse(result.Request.responseCode, result.Response, result.Request.error);
                return Result.Failure(error);
            }
            return Result.Success();
        }
        
        public static async Awaitable<Result> ConfirmForgotPassword(string email, string password, string confirmationCode)
        {
            var request = AuthClient.GetConfirmForgotPasswordRequest(email, password, confirmationCode);
            var result = await SendRequest(request);
            if (!result.Success)
            {
                var error = AuthErrorParser.Parse(result.Request.responseCode, result.Response, result.Request.error);
                return Result.Failure(error);
            }
            return Result.Success();
        }
    }
}