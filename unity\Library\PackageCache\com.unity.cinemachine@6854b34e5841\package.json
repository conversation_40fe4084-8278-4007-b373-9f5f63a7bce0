{"name": "com.unity.cinemachine", "displayName": "Cinemachine", "version": "2.10.2", "unity": "2019.4", "description": "Smart camera tools for passionate creators. \n\nNew starting from 2.7.1: Are you looking for the Cinemachine menu? It has moved to the GameObject menu.\n\nIMPORTANT NOTE: If you are upgrading from the legacy Asset Store version of Cinemachine, delete the Cinemachine asset from your project BEFORE installing this version from the Package Manager.", "keywords": ["camera", "follow", "rig", "fps", "cinematography", "aim", "orbit", "cutscene", "cinematic", "collision", "freelook", "cinemachine", "compose", "composition", "dolly", "track", "clearshot", "noise", "framing", "handheld", "lens", "impulse"], "category": "cinematography", "dependencies": {"com.unity.test-framework": "1.1.31"}, "samples": [{"displayName": "Cinemachine Example Scenes", "description": "Sample scenes illustrating various ways to use Cinemachine", "path": "Samples~/Cinemachine Example Scenes"}], "_upm": {"changelog": "- Added Padding option to CinemachineConfiner2D.\n- Regression fix: Sometimes a deeply-nested passive camera's position would creep due to precision inaccuracies."}, "upmCi": {"footprint": "22aeeefc6bb583574b74df163153f9e7a4070ebb"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.cinemachine@2.10/manual/index.html", "repository": {"url": "https://github.com/Unity-Technologies/com.unity.cinemachine.git", "type": "git", "revision": "579032a363b6269a3817d495c99a64394339b076"}, "_fingerprint": "6854b34e58419b9346026c4393678804583b044b"}