using System;
using BallManager.StrokeData.Data;
using BallManager.StrokeData.DataContracts;
using Network.Subscriptions;
using Sky.GenEx.GraphQL.Core;
using UnityEngine;

namespace Network.Services
{
    public static class StrokeService
    {
        public static event Action<StrokeData> StrokeReceived;
        
        private static IGraphQLSubscriptionHandler<StrokeDataContractWrapper> subscriptionHandler;
        public static async void InitializeSubscription(string tvDeviceId)
        {
            var subscription = new StrokeSubscription(tvDeviceId);
            try
            {
                subscriptionHandler = await AwsClient.Golf
                    .SubscribeAsync<StrokeDataContractWrapper>(subscription.SubscriptionString, subscription.Name);
                subscriptionHandler.OnData += OnStrokeReceived;
                subscriptionHandler.OnError += OnStrokeError;
            }
            catch (Exception e)
            {
                // TODO: Handle subscription errors
                Debug.LogException(e);
            }
        }
        
        public static void StopSubscription()
        {
            try
            {
                if (subscriptionHandler == null) return;
                subscriptionHandler.OnData -= OnStrokeReceived;
                subscriptionHandler.OnError -= OnStrokeError;
                subscriptionHandler.Unsubscribe();
                subscriptionHandler = null;
            }
            catch (Exception e)
            {
                Debug.LogException(e);
            }
        }

        private static void OnStrokeReceived(StrokeDataContractWrapper result)
        {
            if (result == null)
            {
                Debug.LogError("Received null stroke data");
                return;
            }
            
            var strokeData = new StrokeData(result.StrokeDataContract);
            Debug.Log($"Stroke received hole {strokeData.HoleDetails.Number} stroke {strokeData.StrokeNumber} for golfer {strokeData.GolferData.PlayerName}");
            
            StrokeReceived?.Invoke(strokeData);
        }
        
        private static void OnStrokeError(GraphQLError[] graphQlErrors)
        {
            foreach (var graphQlError in graphQlErrors)
            {
                Debug.LogError($"Error in stroke subscription: {graphQlError.Message}");
            }
        }

        public static void SimulateStrokeReceived(StrokeDataContract stroke)
        {
            var wrapper = new StrokeDataContractWrapper
            {
                StrokeDataContract = stroke
            };
            OnStrokeReceived(wrapper);
        }
    }
}