using BallManager.StrokeData.DataContracts;
using Sky.GenEx.Utilities;
using UnityEngine;

namespace BallManager.StrokeData.Data
{
	public class CoordinateData
	{
		public Vector3 Coordinate { get; }
		public bool IsValid { get; }
		
		public CoordinateData(CoordinateLocationDataContract dataContract)
		{
			if (dataContract.X == null || dataContract.Y == null || dataContract.Z == null)
			{
				Coordinate = Vector3.zero;
				IsValid = false;
			}

			float x = dataContract.X ?? 0;
			float y = dataContract.Y ?? 0;
			float z = dataContract.Z ?? 0;

			Coordinate = UnitsUtilities.ConvertFeetToMetres(new Vector3(x,z,y));
			IsValid = true;
		}

		public CoordinateData(CoordinatesDataContract coordinatesDataContract)
		{
			Coordinate = UnitsUtilities.ConvertFeetToMetres(new Vector3(coordinatesDataContract.X, coordinatesDataContract.Z, coordinatesDataContract.Y));
			IsValid = Coordinate != Vector3.zero;
		}
	}
}
