{"ProjectName": "golf-app", "BuildLogRetention": "THREE_MONTHS", "IncludeEnvVars": "false", "SourceBucketKey": "cd-release.zip", "DeploymentBuildSpecPath": "ci/deploy.buildspec.yml", "SmokeTestsBuildSpecPath": "\"\"", "Image": "aws/codebuild/standard:7.0", "BuildTimeout": "60", "IncludeStagingDeploy": "true", "IncludeManualApproval": "false", "IncludeProdDeploy": "true", "ParameterARNs": "", "WriteParameterARNs": "", "SecretARNs": "", "RoleARNs": "", "CodeBuildPolicy": "", "PrivilegedMode": "false", "CDKMode": "true", "CDKEnhancedMode": "true", "FailureSnsTopic": "", "SendFailureSlackNotifications": "false", "TriggeringAccount": "************", "Promote": false, "TARGET_STACK_ID": "cd-serverless", "TAGS": "[\n  {\n    \"name\": \"project\",\n    \"value\": \"golf-app\"\n  },\n  {\n    \"name\": \"territory\",\n    \"value\": \"uk\"\n  },\n  {\n    \"name\": \"billing_team\",\n    \"value\": \"gpds\"\n  },\n  {\n    \"name\": \"service\",\n    \"value\": \"golf-app\"\n  }\n]\n"}