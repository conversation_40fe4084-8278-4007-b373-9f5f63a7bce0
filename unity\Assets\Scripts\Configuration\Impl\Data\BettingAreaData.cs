using Configuration.Impl.DataContract;
using UnityEngine;

namespace Configuration.Impl.Data
{
    public class BettingAreaData
    {
        public int HoleNumber { get; }
        public Vector3 TerrainCorner1 { get; }
        public Vector3 TerrainCorner2 { get; }
        
        public BettingAreaData(BettingAreaDataContract dataContract)
        {
            HoleNumber = dataContract.HoleNumber;
            TerrainCorner1 = new Vector3(dataContract.TerrainCorner1.X, dataContract.TerrainCorner1.Y, dataContract.TerrainCorner1.Z);
            TerrainCorner2 = new Vector3(dataContract.TerrainCorner2.X, dataContract.TerrainCorner2.Y, dataContract.TerrainCorner2.Z);
        }
    }
}
