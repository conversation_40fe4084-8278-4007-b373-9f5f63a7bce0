using Login.State;
using UnityEngine;
using UnityEngine.UI;

public class WelcomePanel : BaseLoginState
{
    [SerializeField] private Button signUpButton;
    [SerializeField] private Button signInButton;

    public override void Enter()
    {
        if(!InspectorValidator.CheckAssigned(signUpButton, nameof(signUpButton), this) ||
            !InspectorValidator.CheckAssigned(signInButton, nameof(signInButton), this))
        {
            return;
        }

        gameObject.SetActive(true);
        signUpButton.onClick.AddListener(() => onFinished(AuthState.SignUp));
        signInButton.onClick.AddListener(() => onFinished(AuthState.SignIn));
    }

    public override void Exit()
    {
        signUpButton.onClick.RemoveAllListeners();
        signInButton.onClick.RemoveAllListeners();
        gameObject.SetActive(false);
    }
}
