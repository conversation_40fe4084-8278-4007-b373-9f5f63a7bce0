import { fetchAuthSession, fetchUserAttributes } from "aws-amplify/auth";
import { useEffect, useState, JSX } from "react";
import { Navigate } from "react-router";

export const RedirectRoute: ({
  children,
}: {
  children: JSX.Element;
}) => JSX.Element = ({ children }) => {
  const [validSession, setValidSession] = useState<boolean | null>(null);

  const validateAuthSession = async () => {
    try {
      const session = await fetchAuthSession({ forceRefresh: true });
      if (!!session && !!session.tokens?.accessToken) {
        const userAttributes = await fetchUserAttributes();
        if (!!userAttributes.preferred_username) return setValidSession(true);
      }
      setValidSession(false);
    } catch (error) {
      setValidSession(false);
    }
  };

  // validate auth session on load
  useEffect(() => {
    void validateAuthSession();
  }, []);
  if (validSession === null) return <></>;
  if (validSession === true) return <Navigate to="/" />;
  return children;
};
