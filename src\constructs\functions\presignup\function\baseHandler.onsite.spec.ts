import { PreSignUpTriggerEvent } from "aws-lambda";
import words from "profane-words";
import { baseHandler } from "./baseHandler";
import { getHouseholdSize } from "./utils/getHouseholdSize";
import { isExistingSignInAlias } from "./utils/isExistingSignInAlias";

jest.mock("./utils/getHouseholdSize");
jest.mock("./utils/isExistingSignInAlias");
jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
jest.mock("profane-words", () => ({ includes: jest.fn(() => false) }));
jest.mock("@sky-uk/coins-utilities", () => ({
  getEnvironmentVariable: jest.fn((key) => {
    const env: Record<string, string> = {
      HOUSEHOLD_TABLE_NAME: "TestTable",
      MAX_HOUSEHOLD_SIZE: "5",
      APP_VARIANT: "onsite",
    };
    return env[key] || "";
  }),
}));

describe("onsite - baseHandler (preSignUp)", () => {
  const event = {
    request: {
      userAttributes: {
        "custom:deviceId": "device-1",
        email: "<EMAIL>",
      },
    },
    userName: "testuser",
    response: {},
  } as unknown as PreSignUpTriggerEvent;

  beforeEach(() => {
    jest.clearAllMocks();
    (words.includes as jest.Mock).mockReturnValue(false);
  });

  it("should auto confirm and verify if all checks pass", async () => {
    (getHouseholdSize as jest.Mock).mockResolvedValue(2);
    (isExistingSignInAlias as jest.Mock).mockResolvedValue(false);
    const result = await baseHandler(event);
    expect(result.response.autoConfirmUser).toBe(true);
    expect(result.response.autoVerifyEmail).toBe(true);
  });

  it("should not throw if deviceId is missing", async () => {
    const missingDeviceId = {
      ...event,
      request: {
        userAttributes: {
          ...event.request.userAttributes,
          "custom:deviceId": "",
        },
      },
    };
    const response = await baseHandler(missingDeviceId);
    expect(response).toBe(missingDeviceId);
  });

  it("should throw if username is missing", async () => {
    await expect(baseHandler({ ...event, userName: "" })).rejects.toThrow(
      ": Missing username",
    );
  });

  it.each([
    ["UppercaseUser", "starts with uppercase"],
    ["1username", "starts with number"],
    ["_username", "starts with underscore"],
    ["user-name", "contains hyphen"],
    ["user.name", "contains dot"],
    ["user@name", "contains special character"],
    ["user name", "contains space"],
  ])(
    "should throw if username is invalid: %s (%s)",
    async (invalidUsername) => {
      const inputEvent = { ...event, userName: invalidUsername };
      await expect(baseHandler(inputEvent)).rejects.toThrow(
        ": Username is invalid",
      );
    },
  );

  it("should throw if username is profane", async () => {
    (words.includes as jest.Mock).mockReturnValue(true);
    await expect(baseHandler(event)).rejects.toThrow(": Username is invalid");
  });

  it("should throw if email is missing", async () => {
    await expect(
      baseHandler({
        ...event,
        request: {
          userAttributes: { ...event.request.userAttributes, email: "" },
        },
      }),
    ).rejects.toThrow(": Missing email");
  });

  it("should not validate household size", async () => {
    (getHouseholdSize as jest.Mock).mockResolvedValue(2);
    await baseHandler(event);
    expect(getHouseholdSize).not.toHaveBeenCalled();
  });
});
