import "aws-sdk-client-mock-jest";
import {
  DynamoDBClient,
  TransactWriteItemsCommand,
} from "@aws-sdk/client-dynamodb";
import { mockClient } from "aws-sdk-client-mock";
import { incrementHouseholdSize } from "./incrementHouseholdSize";

jest.mock("@sky-uk/coins-o11y-utils/src/lib/logger");
const ddbMock = mockClient(DynamoDBClient);

describe("incrementHouseholdSize", () => {
  const props = {
    tableName: "TestTable",
    maxHouseholdSize: 5,
    deviceId: "device-123",
    signInAlias: "alias-abc",
  };

  beforeEach(() => {
    ddbMock.reset();
  });

  it("should call DynamoDB TransactWriteItemsCommand", async () => {
    await incrementHouseholdSize(props);
    expect(ddbMock).toHaveReceivedCommand(TransactWriteItemsCommand);
  });

  it("should throw 'Household capacity has been reached' on userCount ConditionalCheckFailed", async () => {
    ddbMock.on(TransactWriteItemsCommand).rejects({
      message:
        "Transaction cancelled, please refer cancellation reasons for specific reasons [ConditionalCheckFailed, None] userCount",
    });
    await expect(incrementHouseholdSize(props)).rejects.toThrow(
      "Household capacity has been reached",
    );
  });

  it("should throw 'Update item failed' on other errors", async () => {
    ddbMock
      .on(TransactWriteItemsCommand)
      .rejects({ message: "Some other error" });
    await expect(incrementHouseholdSize(props)).rejects.toThrow(
      "Update item failed",
    );
  });
});
