using System;
using System.Text;
using System.Web;
using Constants;
using Data;
using Sky.GenEx.Toast;
using UnityEngine;

namespace Bootstrap
{
    public static class UrlParamsExtractor
    {
        // Extra params from URL when app is launched on WebGL build
        public static bool Extract()
        {
            // Extract a device id from the editor so we
            if (Application.isEditor)
            {
                string editorDeviceId = SystemInfo.deviceUniqueIdentifier;
                editorDeviceId = Convert.ToBase64String(Encoding.UTF8.GetBytes(editorDeviceId));
                SessionData.SetTvDeviceId(editorDeviceId);
                return true;
            }

            var urlParams = Application.absoluteURL;
            if (string.IsNullOrEmpty(urlParams))
            {
                Debug.LogError("Application.absoluteURL is null or empty.");
                LogError();
                return false;
            }

            var uri = new Uri(urlParams);
            var query = uri.Query;

            if (string.IsNullOrEmpty(query))
            {
                Debug.LogError("Query is null or empty.");
                LogError();
                return false;
            }

            var queryParams = HttpUtility.ParseQueryString(query);

            var deviceId = queryParams["device-id"];
            if (deviceId != null)
            {
                SessionData.SetTvDeviceId(deviceId);
                return true;
            }
            else
            {
                Debug.LogError("No 'device-id' found in URL parameters.");
                LogError();
            }

            return false;
        }

        private static void LogError()
        {
            MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong);
        }
    }
}