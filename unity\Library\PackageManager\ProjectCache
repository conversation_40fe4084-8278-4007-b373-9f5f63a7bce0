m_ProjectFiles:
  m_ManifestFileStatus:
    m_FilePath: C:/Users/<USER>/Documents/btv-genex-golf-app/unity/Packages/manifest.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638905868990016272
    m_Hash: 1176881827
  m_LockFileStatus:
    m_FilePath: C:/Users/<USER>/Documents/btv-genex-golf-app/unity/Packages/packages-lock.json
    m_PathExists: 1
    m_ContentTrackingEnabled: 1
    m_ModificationDate:
      serializedVersion: 2
      ticks: 638905869569070315
    m_Hash: 2287874309
m_EmbeddedPackageManifests:
  m_ManifestsStatus: {}
m_LocalPackages:
  m_LocalFileStatus: []
m_ProjectPath: C:/Users/<USER>/Documents/btv-genex-golf-app/unity/Packages
m_EditorVersion: 6000.0.44f1 (101c91f3a8fb)
m_ResolvedPackages:
- packageId: com.cysharp.unitask@https://github.com/Cysharp/UniTask.git?path=src/UniTask/Assets/Plugins/UniTask
  testable: 0
  isDirectDependency: 1
  version: 2.5.10
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.cysharp.unitask@86b6e6a2e286
  assetPath: Packages/com.cysharp.unitask
  name: com.cysharp.unitask
  displayName: UniTask
  author:
    name: Cysharp, Inc.
    email: 
    url: https://cysharp.co.jp/en/
  category: Task
  type: 
  description: Provides an efficient async/await integration to Unity.
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - async/await
  - async
  - Task
  - UniTask
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: f213ff497e4ff462a77319cf677cf20cc0860ca9
    revision: HEAD
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 86b6e6a2e2863facad59648b5c1bc1b9e91e6514
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2018.4.0a1
- packageId: com.genex.arttoolkit@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - ArtToolKit#ArtToolKit/v1.0.2
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.arttoolkit@f55ad041b30e
  assetPath: Packages/com.genex.arttoolkit
  name: com.genex.arttoolkit
  displayName: GenEx - ArtToolKit
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: sdk
  description: This package provides a set of tools for generating and manipulating
    art assets in Unity. These reusable components should be used to easily create
    animations, fxs, and other art-related features in your Unity projects.
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: bab72c375152f9b5e29a1046cb7f3749eb5b0c17
    revision: ArtToolKit/v1.0.2
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f55ad041b30e9539d806349436562fa2643cb76a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.genex.files@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - Files#Files/v1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.files@40c82321c90e
  assetPath: Packages/com.genex.files
  name: com.genex.files
  displayName: GenEx - Files
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: sdk
  description: Files Component part of GenEx Components toolkit
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.genex.promises
    version: 1.0.0
  - name: com.genex.utilities
    version: 1.0.0
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.genex.promises
    version: 1.0.0
  - name: com.genex.utilities
    version: 1.0.1
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 9823e8b9d6e920b3435bd8c1ccc7bb7df7689d09
    revision: Files/v1.0.0
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 40c82321c90e49a06988bee347642b198b68f015
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.genex.graphql@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - GraphQL#GraphQL/v1.0.3
  testable: 0
  isDirectDependency: 1
  version: 1.0.3
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.graphql@ba4a1887c5f1
  assetPath: Packages/com.genex.graphql
  name: com.genex.graphql
  displayName: GenEx - GraphQL
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: 
  description: GenEx - GraphQL is a Unity package that provides a production-ready
    GraphQL client with comprehensive support for queries, mutations, and subscriptions
    across all platforms including WebGL, iOS, Android, and Unity Editor. Features
    AWS AppSync protocol support, custom WebSocket implementation, and configurable
    logging.
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - unity
  - graphql
  - api
  - websocket
  - subscriptions
  - mutations
  - queries
  - networking
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 50f3e1575c4d85c5f6bc61c644c2b3957f24bdad
    revision: GraphQL/v1.0.3
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: ba4a1887c5f11ff3fd623d74d7b00387a21828a4
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.genex.promises@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - Promises#Promises/v1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.promises@89a0ab862811
  assetPath: Packages/com.genex.promises
  name: com.genex.promises
  displayName: GenEx - Promises
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: sdk
  description: Promises Component part of GenEx Components toolkit
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 9823e8b9d6e920b3435bd8c1ccc7bb7df7689d09
    revision: Promises/v1.0.0
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 89a0ab8628113cab792cb482608d398e08cf2c7a
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.genex.toast@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - MessageToast#MessageToast/v1.0.1
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.toast@255fe98b024c
  assetPath: Packages/com.genex.toast
  name: com.genex.toast
  displayName: GenEx - MessageToast
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: sdk
  description: Message toast tool, similar to Android Toast component, used as a
    default way to display messages to user
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.textmeshpro
    version: 3.0.6
  - name: com.genex.utilities
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.textmeshpro
    version: 5.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.genex.utilities
    version: 1.0.1
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 825a85cd3a447757b3ecdfc4b56c0cc10e2a51bf
    revision: MessageToast/v1.0.1
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 255fe98b024c33ed482aafc1b070f29e5c63cbd4
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.genex.utilities@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - Utilities#Utilities/v1.0.1
  testable: 0
  isDirectDependency: 1
  version: 1.0.1
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.utilities@8871600b5869
  assetPath: Packages/com.genex.utilities
  name: com.genex.utilities
  displayName: GenEx - Utilities
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: sdk
  description: Utilities Component part of GenEx Components toolkit
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  resolvedDependencies:
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: f41bf83b7c67c4afcf108215385919a355aa17f9
    revision: Utilities/v1.0.1
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 8871600b5869091fd751fc19d969e7eb11e315bb
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.genex.infinitescroller@https://github.com/sky-uk/btv-genex-unity-components.git?path=/GenEx
    Unity Components/Packages/GenEx - InfiniteScroller#InfiniteScroller/v1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 5
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.genex.infinitescroller@051af65851fd
  assetPath: Packages/com.genex.infinitescroller
  name: com.genex.infinitescroller
  displayName: GenEx - Infinite Scroller
  author:
    name: Sky
    email: 
    url: 
  category: 
  type: sdk
  description: Infinite Scroller Component part of GenEx Components toolkit
  errors: []
  versions:
    all: []
    compatible: []
    recommended: 
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 0
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 894b4dc1a1e0dcea3f27d8c1424950e2b1abf6fb
    revision: InfiniteScroller/v1.0.0
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 051af65851fd65d1b082bad31017700c9919765c
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2024.0.27f1
- packageId: com.unity.2d.sprite@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.2d.sprite@072d7bd355e5
  assetPath: Packages/com.unity.2d.sprite
  name: com.unity.2d.sprite
  displayName: 2D Sprite
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: Use Unity Sprite Editor Window to create and edit Sprite asset properties
    like pivot, borders and Physics shape
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - 2d
  - sprite
  - sprite editor window
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 072d7bd355e55ee3ded20a6a52435a4f5fded0d8
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.2d.spriteshape@10.0.7
  testable: 0
  isDirectDependency: 1
  version: 10.0.7
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.2d.spriteshape@9e35352ae135
  assetPath: Packages/com.unity.2d.spriteshape
  name: com.unity.2d.spriteshape
  displayName: 2D SpriteShape
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: SpriteShape Runtime & Editor Package contains the tooling and the
    runtime component that allows you to create very organic looking spline based
    2D worlds. It comes with intuitive configurator and a highly performant renderer.
  errors: []
  versions:
    all:
    - 1.0.10-preview.2
    - 1.0.11-preview
    - 1.0.12-preview
    - 1.0.12-preview.1
    - 1.0.14-preview.2
    - 2.0.0-preview.4
    - 2.0.0-preview.5
    - 2.0.0-preview.7
    - 2.0.0-preview.8
    - 2.0.0-preview.9
    - 2.1.0-preview.2
    - 2.1.0-preview.6
    - 2.1.0-preview.7
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 3.0.1
    - 3.0.2
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.0.10
    - 3.0.11
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.1.0
    - 4.1.1
    - 4.1.2
    - 4.1.3
    - 4.1.4
    - 4.1.5
    - 5.0.0
    - 5.0.1
    - 5.0.2
    - 5.1.0
    - 5.1.1
    - 5.1.2
    - 5.1.3
    - 5.1.4
    - 5.1.5
    - 5.1.6
    - 5.1.7
    - 5.2.0
    - 5.3.0
    - 6.0.0-pre.1
    - 6.0.0-pre.2
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 7.0.0-pre.2
    - 7.0.0-pre.3
    - 7.0.0
    - 7.0.2
    - 7.0.3
    - 7.0.4
    - 7.0.5
    - 7.0.6
    - 7.0.7
    - 7.1.0
    - 8.0.0-pre.4
    - 8.0.0-pre.5
    - 8.0.0
    - 8.0.1
    - 9.0.0-pre.1
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.1.0
    - 9.1.1
    - 10.0.0-pre.1
    - 10.0.0-pre.2
    - 10.0.0
    - 10.0.1
    - 10.0.2
    - 10.0.3
    - 10.0.4
    - 10.0.5
    - 10.0.6
    - 10.0.7
    - 10.1.0
    - 11.0.0
    - 12.0.0
    - 12.0.1
    - 13.0.0
    compatible:
    - 10.0.7
    - 10.1.0
    recommended: 10.0.7
    deprecated: []
  dependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.2d.common
    version: 9.0.7
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords:
  - 2d
  - shape
  - spriteshape
  - smartsprite
  - spline
  - terrain2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459104040000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.spriteshape@10.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: baf581b10d05f7aa59c58b39f4b798829f703946
    path: 
  unityLifecycle:
    version: 10.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- DANB-731 \"System.IndexOutOfRangeException\"
    is thrown when increasing Sprite Shape Mesh size beyond limits"}'
  assetStore:
    productId: 
  fingerprint: 9e35352ae135f602746220e7edc09eb95bbec530
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 10.0.7
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.2d.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.2d.tilemap@cfc8c9bb756e
  assetPath: Packages/com.unity.2d.tilemap
  name: com.unity.2d.tilemap
  displayName: 2D Tilemap Editor
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Tilemap Editor is a package that contains editor functionalities
    for editing Tilemaps.
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.tilemap
    version: 1.0.0
  - name: com.unity.modules.physics2d
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  - Tilemap
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: cfc8c9bb756efb1948e5647a5bf4433a1d56b0aa
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.cinemachine@2.10.2
  testable: 0
  isDirectDependency: 1
  version: 2.10.2
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.cinemachine@6854b34e5841
  assetPath: Packages/com.unity.cinemachine
  name: com.unity.cinemachine
  displayName: Cinemachine
  author:
    name: 
    email: 
    url: 
  category: cinematography
  type: 
  description: "Smart camera tools for passionate creators. \n\nNew starting from
    2.7.1: Are you looking for the Cinemachine menu? It has moved to the GameObject
    menu.\n\nIMPORTANT NOTE: If you are upgrading from the legacy Asset Store version
    of Cinemachine, delete the Cinemachine asset from your project BEFORE installing
    this version from the Package Manager."
  errors: []
  versions:
    all:
    - 2.1.11-beta.1
    - 2.1.12
    - 2.1.13
    - 2.2.0
    - 2.2.7
    - 2.2.8
    - 2.2.9
    - 2.2.10-preview.3
    - 2.2.10-preview.4
    - 2.3.1
    - 2.3.3
    - 2.3.4
    - 2.3.5-preview.3
    - 2.4.0-preview.3
    - 2.4.0-preview.4
    - 2.4.0-preview.6
    - 2.4.0-preview.7
    - 2.4.0-preview.8
    - 2.4.0-preview.9
    - 2.4.0-preview.10
    - 2.4.0
    - 2.5.0
    - 2.6.0-preview.2
    - 2.6.0-preview.3
    - 2.6.0-preview.5
    - 2.6.0-preview.8
    - 2.6.0
    - 2.6.1-preview.6
    - 2.6.1
    - 2.6.2-preview.1
    - 2.6.2
    - 2.6.3-preview.2
    - 2.6.3
    - 2.6.4
    - 2.6.5
    - 2.6.9
    - 2.6.10
    - 2.6.11
    - 2.6.14
    - 2.6.15
    - 2.6.17
    - 2.7.1
    - 2.7.2
    - 2.7.3
    - 2.7.4
    - 2.7.5
    - 2.7.8
    - 2.7.9
    - 2.8.0-exp.1
    - 2.8.0-exp.2
    - 2.8.0-pre.1
    - 2.8.0
    - 2.8.1
    - 2.8.2
    - 2.8.3
    - 2.8.4
    - 2.8.6
    - 2.8.9
    - 2.9.0-pre.1
    - 2.9.0-pre.6
    - 2.9.1
    - 2.9.2
    - 2.9.4
    - 2.9.5
    - 2.9.7
    - 2.10.0
    - 2.10.1
    - 2.10.2
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    compatible:
    - 2.10.3
    - 2.10.4
    - 3.0.0-pre.3
    - 3.0.0-pre.4
    - 3.0.0-pre.5
    - 3.0.0-pre.6
    - 3.0.0-pre.7
    - 3.0.0-pre.8
    - 3.0.0-pre.9
    - 3.0.1
    - 3.1.0
    - 3.1.1
    - 3.1.2
    - 3.1.3
    - 3.1.4
    recommended: 3.1.4
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - camera
  - follow
  - rig
  - fps
  - cinematography
  - aim
  - orbit
  - cutscene
  - cinematic
  - collision
  - freelook
  - cinemachine
  - compose
  - composition
  - dolly
  - track
  - clearshot
  - noise
  - framing
  - handheld
  - lens
  - impulse
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459477540000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.cinemachine@2.10/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.cinemachine.git
    revision: 579032a363b6269a3817d495c99a64394339b076
    path: 
  unityLifecycle:
    version: 2.10.3
    nextVersion: 
    recommendedVersion: 3.1.0
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Added Padding option to CinemachineConfiner2D.\n-
    Regression fix: Sometimes a deeply-nested passive camera''s position would creep
    due to precision inaccuracies."}'
  assetStore:
    productId: 
  fingerprint: 6854b34e58419b9346026c4393678804583b044b
  editorCompatibility:
    compatibilityLevel: 1
    minimumPackageVersion: 2.10.3
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.feature.development@1.0.2
  testable: 0
  isDirectDependency: 1
  version: 1.0.2
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.feature.development@767aadbc6eb7
  assetPath: Packages/com.unity.feature.development
  name: com.unity.feature.development
  displayName: Engineering
  author:
    name: 
    email: 
    url: 
  category: 
  type: feature
  description: "Optimize your development experience in Unity with the Dev Tools
    feature set. Enable support for multiple integrated development environments
    (IDE) for editing your Unity code. Get access to development tools to help you
    test and analyze your project\u2019s performance."
  errors: []
  versions:
    all:
    - 1.0.2
    compatible:
    - 1.0.2
    recommended: 1.0.2
    deprecated: []
  dependencies:
  - name: com.unity.ide.visualstudio
    version: default
  - name: com.unity.ide.rider
    version: default
  - name: com.unity.editorcoroutines
    version: default
  - name: com.unity.performance.profile-analyzer
    version: default
  - name: com.unity.test-framework
    version: default
  - name: com.unity.testtools.codecoverage
    version: default
  resolvedDependencies:
  - name: com.unity.ide.visualstudio
    version: 2.0.23
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ide.rider
    version: 3.0.36
  - name: com.unity.editorcoroutines
    version: 1.0.0
  - name: com.unity.performance.profile-analyzer
    version: 1.2.3
  - name: com.unity.testtools.codecoverage
    version: 1.2.6
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"quickstart":"https://docs.unity3d.com/Documentation/Manual/DeveloperToolsFeature.html"}'
  assetStore:
    productId: 
  fingerprint: 767aadbc6eb72681a4ca807c8fa248e0230a0cef
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.2
    minimumUnityVersion: 
- packageId: com.unity.multiplayer.center@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.multiplayer.center@f3fb577b3546
  assetPath: Packages/com.unity.multiplayer.center
  name: com.unity.multiplayer.center
  displayName: Multiplayer Center
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The multiplayer center provides a starting point to create multiplayer
    games. It will recommend specific packages and enable you to easily access integrations,
    samples and documentation.
  errors: []
  versions:
    all:
    - 0.2.1
    - 0.3.0
    - 0.4.0
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - Multiplayer
  - Netcode
  - Services
  - Tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: f3fb577b3546594b97b8cc34307cd621f60f1c73
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.nuget.newtonsoft-json@3.2.1
  testable: 0
  isDirectDependency: 1
  version: 3.2.1
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.nuget.newtonsoft-json@74deb55db2a0
  assetPath: Packages/com.unity.nuget.newtonsoft-json
  name: com.unity.nuget.newtonsoft-json
  displayName: Newtonsoft Json
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'Newtonsoft Json for use in Unity projects and Unity packages. Currently
    synced to version 13.0.2.


    This package is used for advanced json serialization
    and deserialization. Most Unity users will be better suited using the existing
    json tools built into Unity.

    To avoid assembly clashes, please use this
    package if you intend to use Newtonsoft Json.'
  errors: []
  versions:
    all:
    - 1.0.0-preview.2
    - 1.0.0-preview.3
    - 1.0.0-preview.4
    - 1.0.1-preview.1
    - 1.1.2
    - 2.0.0-preview
    - 2.0.0-preview.1
    - 2.0.0-preview.2
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.2
    - 3.0.1
    - 3.0.2
    - 3.1.0
    - 3.2.0
    - 3.2.1
    compatible:
    - 3.2.1
    recommended: 3.2.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638186318800000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.newtonsoft-json.git
    revision: d8e49aef8979bef617144382052ec2f479645eaf
    path: 
  unityLifecycle:
    version: 3.2.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"* Fixed Newtonsoft DLL when compiling with netstandard
    2.0."}'
  assetStore:
    productId: 
  fingerprint: 74deb55db2a0c29ddfda576608bcb86abbd13ee6
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.2.1
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.render-pipelines.universal@17.0.4
  testable: 0
  isDirectDependency: 1
  version: 17.0.4
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.render-pipelines.universal@82fb1398b3b8
  assetPath: Packages/com.unity.render-pipelines.universal
  name: com.unity.render-pipelines.universal
  displayName: Universal RP
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Universal Render Pipeline (URP) is a prebuilt Scriptable Render
    Pipeline, made by Unity. URP provides artist-friendly workflows that let you
    quickly and easily create optimized graphics across a range of platforms, from
    mobile to high-end consoles and PCs.
  errors: []
  versions:
    all:
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.35
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.26
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.4
    compatible:
    - 17.0.4
    recommended: 17.0.4
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.shadergraph
    version: 17.0.4
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.shadergraph
    version: 17.0.4
  - name: com.unity.searcher
    version: 4.9.3
  - name: com.unity.render-pipelines.universal-config
    version: 17.0.3
  keywords:
  - graphics
  - performance
  - rendering
  - mobile
  - render
  - pipeline
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 82fb1398b3b87f0a576206e3d91879e37ae62797
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.4
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.services.cloud-diagnostics@1.0.10
  testable: 0
  isDirectDependency: 1
  version: 1.0.10
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.services.cloud-diagnostics@bb0159d0509a
  assetPath: Packages/com.unity.services.cloud-diagnostics
  name: com.unity.services.cloud-diagnostics
  displayName: Cloud Diagnostics
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: This package is a suite of cloud enabled tools that help you collect
    and identify issues that users encounter with your apps.
  errors: []
  versions:
    all:
    - 1.0.3
    - 1.0.5
    - 1.0.6
    - 1.0.7
    - 1.0.9
    - 1.0.10
    compatible:
    - 1.0.10
    recommended: 1.0.10
    deprecated: []
  dependencies:
  - name: com.unity.services.core
    version: 1.12.5
  resolvedDependencies:
  - name: com.unity.services.core
    version: 1.14.0
  - name: com.unity.modules.androidjni
    version: 1.0.0
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638539875400000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/operate-services-sdk.git
    revision: 1414ef72e01b124ad0140fe99462fec2071717da
    path: 
  unityLifecycle:
    version: 1.0.10
    nextVersion: 
    recommendedVersion: 1.0.6
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Modified \n- Adjusted documentation","gameService":{"configurePath":"Project/Services/Cloud
    Diagnostics","genericDashboardUrl":"https://dashboard.unity3d.com/gaming/cloud-diagnostics","projectDashboardUrl":"https://developer.cloud.unity3d.com/diagnostics/orgs/{0}/projects/{1}/crashes","projectDashboardUrlType":"OrganizationKeyAndProjectGuid"},"supportedPlatforms":["Android","iOS","Linux","Mac","PC","WebGL","Windows
    8 Universal","Windows 10 Universal"]}'
  assetStore:
    productId: 
  fingerprint: bb0159d0509ac7234fecd7580ab26b918630e197
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.10
    minimumUnityVersion: 2022.1.0a1
- packageId: com.unity.splines@2.6.1
  testable: 0
  isDirectDependency: 1
  version: 2.6.1
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.splines@930eaf55e68b
  assetPath: Packages/com.unity.splines
  name: com.unity.splines
  displayName: Splines
  author:
    name: 
    email: 
    url: 
  category: Tool
  type: tool
  description: Work with curves and paths. Use the Splines package to generate objects
    and behaviors along paths, create trajectories, and draw shapes.
  errors: []
  versions:
    all:
    - 0.1.0-preview.1
    - 1.0.0-pre.5
    - 1.0.0-pre.7
    - 1.0.0-pre.8
    - 1.0.0-pre.9
    - 1.0.0
    - 1.0.1
    - 2.0.0-pre.2
    - 2.0.0-pre.4
    - 2.0.0
    - 2.1.0
    - 2.2.0
    - 2.2.1
    - 2.3.0
    - 2.4.0
    - 2.5.1
    - 2.5.2
    - 2.6.0
    - 2.6.1
    - 2.7.1
    - 2.7.2
    - 2.8.0
    - 2.8.1
    compatible:
    - 2.7.2
    - 2.8.0
    - 2.8.1
    recommended: 2.7.2
    deprecated: []
  dependencies:
  - name: com.unity.ugui
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.settings-manager
    version: 1.0.3
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords:
  - spline
  - curve
  - path
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638522292040000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.splines.git
    revision: f77ddc46ebeef1a8dca1264827b93dbec50d3772
    path: 
  unityLifecycle:
    version: 2.7.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Bug Fixes\n\n- [SPLB-275] Fixed a bug where the
    `SplineContainer` component would throw warning when entering playmode.\n- [SPLB-266]
    Fixed a bug where instantiating a `SplineExtrude` component at runtime would
    throw errors.\n- [SPLB-269] Fixed a bug where instantiating a `SplineAnimate`
    component at runtime would throw errors.\n- [SPLB-260] Fixed a bug where playing
    a `SplineAnimate` from a start method would not work.\n- [SPLB-267] Fixed a bug
    where undoing a spline insertion in a container would throw errors from the `SplineInstantiate`
    component.\n- [SPLB-261] Fixed a bug where the knot placement tool would not
    work when multiple scene views were opened.\n- [SPLB-253] Fixed a bug where using
    the knot placement tool in 2D mode would not work if the grids are turned off.\n-
    [SPLB-258] Fixed a bug where destroying a component would throw errors in the
    console."}'
  assetStore:
    productId: 
  fingerprint: 930eaf55e68be9f5d58f63035b2a96d9c3c3e695
  editorCompatibility:
    compatibilityLevel: 1
    minimumPackageVersion: 2.7.2
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.timeline@1.8.7
  testable: 0
  isDirectDependency: 1
  version: 1.8.7
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.timeline@c58b4ee65782
  assetPath: Packages/com.unity.timeline
  name: com.unity.timeline
  displayName: Timeline
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use Unity Timeline to create cinematic content, game-play sequences,
    audio sequences, and complex particle effects.
  errors: []
  versions:
    all:
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.2.7
    - 1.2.9
    - 1.2.10
    - 1.2.11
    - 1.2.12
    - 1.2.13
    - 1.2.14
    - 1.2.15
    - 1.2.16
    - 1.2.17
    - 1.2.18
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.5
    - 1.4.0-preview.6
    - 1.4.0-preview.7
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.5.0-pre.2
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.1-pre.1
    - 1.5.1-pre.2
    - 1.5.1-pre.3
    - 1.5.2
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.5.7
    - 1.6.0-pre.1
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0-pre.5
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.7.0-pre.1
    - 1.7.0-pre.2
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.7.5
    - 1.7.6
    - 1.7.7
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.5
    - 1.8.6
    - 1.8.7
    - 1.8.8
    - 1.8.9
    compatible:
    - 1.8.7
    - 1.8.8
    - 1.8.9
    recommended: 1.8.9
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.director
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.particlesystem
    version: 1.0.0
  keywords:
  - unity
  - animation
  - editor
  - timeline
  - tools
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638524833900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.timeline@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.timeline.git
    revision: d6432ca638481c3d0e4c01f87f9cdbe3c4b1d529
    path: 
  unityLifecycle:
    version: 1.8.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Released ronl-workflow-custom-marker.md
    Added a new workflow to the Timeline Workflows documentation:\n- Released ronl-workflow-custom-marker.md
    The `Create a custom Notes marker` workflow demonstrates how to create a custom
    marker for adding notes to Timeline instances. This workflow also demonstrates
    how to change the default appearance of a custom marker with scripting and a
    Unity Style Sheet (USS).\n\n### Fixed\n\n- Fixed bug where using , and . (<>)
    to step frames in the Animation Window while the Timeline Window was linked would
    sometimes not work. [IN-56667](https://unity3d.atlassian.net/servicedesk/customer/portal/2/IN-56667)\n-
    When the Timeline and Animation windows are linked and the Timeline Window is
    active, moving the playhead in the Timeline Window will cause the animation window
    to repaint immediately."}'
  assetStore:
    productId: 
  fingerprint: c58b4ee65782ad38338e29f7ee67787cb6998f04
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.7
    minimumUnityVersion: 2019.3.0a1
- packageId: com.unity.ugui@2.0.0
  testable: 0
  isDirectDependency: 1
  version: 2.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.ugui@a9eaa85fb1b8
  assetPath: Packages/com.unity.ugui
  name: com.unity.ugui
  displayName: Unity UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "Unity UI is a set of tools for developing user interfaces for games
    and applications. It is a GameObject-based UI system that uses Components and
    the Game View to arrange, position, and style user interfaces. \u200B You cannot
    use Unity UI to create or change user interfaces in the Unity Editor."
  errors: []
  versions:
    all:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    compatible:
    - 2.0.0
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
    recommended: 2.0.0
    deprecated:
    - 3.0.0-exp.1
    - 3.0.0-exp.3
    - 3.0.0-exp.4
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords:
  - UI
  - ugui
  - Unity UI
  - Canvas
  - TextMeshPro
  - TextMesh Pro
  - Text
  - TMP
  - SDF
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: a9eaa85fb1b8a4bffea4aee6857e08e57a222aef
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.0
    minimumUnityVersion: 2019.2.0a1
- packageId: com.unity.modules.accessibility@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.accessibility
  assetPath: Packages/com.unity.modules.accessibility
  name: com.unity.modules.accessibility
  displayName: Accessibility
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Accessibility module includes utilities to facilitate the development
    of accessible user experiences in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AccessibilityModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ai@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ai
  assetPath: Packages/com.unity.modules.ai
  name: com.unity.modules.ai
  displayName: AI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AI module implements the path finding features in Unity. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.androidjni@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.androidjni
  assetPath: Packages/com.unity.modules.androidjni
  name: com.unity.modules.androidjni
  displayName: Android JNI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'AndroidJNI module allows you to call Java code. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AndroidJNIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.animation@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.animation
  assetPath: Packages/com.unity.modules.animation
  name: com.unity.modules.animation
  displayName: Animation
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Animation module implements Unity''s animation system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.AnimationModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.assetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.assetbundle
  assetPath: Packages/com.unity.modules.assetbundle
  name: com.unity.modules.assetbundle
  displayName: Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The AssetBundle module implements the AssetBundle class and related
    APIs to load data from AssetBundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.AssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.audio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.audio
  assetPath: Packages/com.unity.modules.audio
  name: com.unity.modules.audio
  displayName: Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Audio module implements Unity''s audio system. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.AudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.cloth@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.cloth
  assetPath: Packages/com.unity.modules.cloth
  name: com.unity.modules.cloth
  displayName: Cloth
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Cloth module implements cloth physics simulation through the
    Cloth component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ClothModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.director@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.director
  assetPath: Packages/com.unity.modules.director
  name: com.unity.modules.director
  displayName: Director
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Director module implements the PlayableDirector class. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.DirectorModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imageconversion@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imageconversion
  assetPath: Packages/com.unity.modules.imageconversion
  name: com.unity.modules.imageconversion
  displayName: Image Conversion
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ImageConversion module implements the ImageConversion class which
    provides helper methods for converting image data. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ImageConversionModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.imgui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.imgui
  assetPath: Packages/com.unity.modules.imgui
  name: com.unity.modules.imgui
  displayName: IMGUI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The IMGUI module provides Unity''s immediate mode GUI solution for
    creating in-game and editor user interfaces. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.IMGUIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.jsonserialize@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.jsonserialize
  assetPath: Packages/com.unity.modules.jsonserialize
  name: com.unity.modules.jsonserialize
  displayName: JSONSerialize
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The JSONSerialize module provides the JsonUtility class which lets
    you serialize Unity Objects to JSON format. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.JSONSerializeModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.particlesystem@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.particlesystem
  assetPath: Packages/com.unity.modules.particlesystem
  name: com.unity.modules.particlesystem
  displayName: Particle System
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ParticleSystem module implements Unity''s Particle System. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.ParticleSystemModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics
  assetPath: Packages/com.unity.modules.physics
  name: com.unity.modules.physics
  displayName: Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics module implements 3D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.PhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.physics2d@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.physics2d
  assetPath: Packages/com.unity.modules.physics2d
  name: com.unity.modules.physics2d
  displayName: Physics 2D
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Physics2d module implements 2D physics in Unity. Scripting API:
    https://docs.unity3d.com/ScriptReference/UnityEngine.Physics2DModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.screencapture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.screencapture
  assetPath: Packages/com.unity.modules.screencapture
  name: com.unity.modules.screencapture
  displayName: Screen Capture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The ScreenCapture module provides functionality to take screen shots
    using the ScreenCapture class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.ScreenCaptureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrain@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrain
  assetPath: Packages/com.unity.modules.terrain
  name: com.unity.modules.terrain
  displayName: Terrain
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Terrain module implements Unity''s Terrain rendering engine available
    through the Terrain component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.terrainphysics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.terrainphysics
  assetPath: Packages/com.unity.modules.terrainphysics
  name: com.unity.modules.terrainphysics
  displayName: Terrain Physics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The TerrainPhysics module connects the Terrain and Physics modules
    by implementing the TerrainCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TerrainPhysicsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.tilemap@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.tilemap
  assetPath: Packages/com.unity.modules.tilemap
  name: com.unity.modules.tilemap
  displayName: Tilemap
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Tilemap module implements the Tilemap class. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.TilemapModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics2d
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.ui@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.ui
  assetPath: Packages/com.unity.modules.ui
  name: com.unity.modules.ui
  displayName: UI
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UI module implements basic components required for Unity''s UI
    system Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.uielements@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.uielements
  assetPath: Packages/com.unity.modules.uielements
  name: com.unity.modules.uielements
  displayName: UIElements
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UIElements module implements the UIElements retained mode UI
    framework. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UIElementsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.umbra@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.umbra
  assetPath: Packages/com.unity.modules.umbra
  name: com.unity.modules.umbra
  displayName: Umbra
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Umbra module implements Unity''s occlusion culling system. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.UmbraModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unityanalytics@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unityanalytics
  assetPath: Packages/com.unity.modules.unityanalytics
  name: com.unity.modules.unityanalytics
  displayName: Unity Analytics
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequest@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequest
  assetPath: Packages/com.unity.modules.unitywebrequest
  name: com.unity.modules.unitywebrequest
  displayName: Unity Web Request
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequest module lets you communicate with http services.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestassetbundle@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestassetbundle
  assetPath: Packages/com.unity.modules.unitywebrequestassetbundle
  name: com.unity.modules.unitywebrequestassetbundle
  displayName: Unity Web Request Asset Bundle
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAssetBundle module provides the DownloadHandlerAssetBundle
    class to use UnityWebRequest to download Asset Bundles. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAssetBundleModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestaudio@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestaudio
  assetPath: Packages/com.unity.modules.unitywebrequestaudio
  name: com.unity.modules.unitywebrequestaudio
  displayName: Unity Web Request Audio
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestAudio module provides the DownloadHandlerAudioClip
    class to use UnityWebRequest to download AudioClips. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestAudioModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequesttexture@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequesttexture
  assetPath: Packages/com.unity.modules.unitywebrequesttexture
  name: com.unity.modules.unitywebrequesttexture
  displayName: Unity Web Request Texture
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestTexture module provides the DownloadHandlerTexture
    class to use UnityWebRequest to download Textures. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestTextureModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.unitywebrequestwww@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.unitywebrequestwww
  assetPath: Packages/com.unity.modules.unitywebrequestwww
  name: com.unity.modules.unitywebrequestwww
  displayName: Unity Web Request WWW
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The UnityWebRequestWWW module implements the legacy WWW lets you
    communicate with http services. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.UnityWebRequestWWWModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestassetbundle
    version: 1.0.0
  - name: com.unity.modules.assetbundle
    version: 1.0.0
  - name: com.unity.modules.unitywebrequestaudio
    version: 1.0.0
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.imageconversion
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vehicles@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vehicles
  assetPath: Packages/com.unity.modules.vehicles
  name: com.unity.modules.vehicles
  displayName: Vehicles
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Vehicles module implements vehicle physics simulation through
    the WheelCollider component. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VehiclesModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.video@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.video
  assetPath: Packages/com.unity.modules.video
  name: com.unity.modules.video
  displayName: Video
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Video module lets you play back video files in your content.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VideoModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.audio
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.vr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.vr
  assetPath: Packages/com.unity.modules.vr
  name: com.unity.modules.vr
  displayName: VR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The VR module implements support for virtual reality devices in Unity.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.VRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.xr
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.wind@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.wind
  assetPath: Packages/com.unity.modules.wind
  name: com.unity.modules.wind
  displayName: Wind
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Wind module implements the WindZone component which can affect
    terrain rendering and particle simulations. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.WindModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.xr@1.0.0
  testable: 0
  isDirectDependency: 1
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.xr
  assetPath: Packages/com.unity.modules.xr
  name: com.unity.modules.xr
  displayName: XR
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The XR module contains the VR and AR related platform support functionality.
    Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.XRModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.modules.subsystems
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.subsystems@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.subsystems
  assetPath: Packages/com.unity.modules.subsystems
  name: com.unity.modules.subsystems
  displayName: Subsystems
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'The Subsystem module contains the definitions and runtime support
    for general subsystems in Unity. Scripting API: https://docs.unity3d.com/ScriptReference/UnityEngine.SubsystemsModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.modules.hierarchycore@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 2
  resolvedPath: C:\Program Files\6000.0.44f1\Editor\Data\Resources\PackageManager\BuiltInPackages\com.unity.modules.hierarchycore
  assetPath: Packages/com.unity.modules.hierarchycore
  name: com.unity.modules.hierarchycore
  displayName: Hierarchy Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: module
  description: 'Module that contains a high-performance hierarchy container. Scripting
    API: https://docs.unity3d.com/ScriptReference/UnityEngine.HierarchyCoreModule.html'
  errors: []
  versions:
    all:
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 
- packageId: com.unity.mathematics@1.3.2
  testable: 0
  isDirectDependency: 0
  version: 1.3.2
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.mathematics@8017b507cc74
  assetPath: Packages/com.unity.mathematics
  name: com.unity.mathematics
  displayName: Mathematics
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Unity's C# SIMD math library providing vector types and math functions
    with a shader like syntax.
  errors: []
  versions:
    all:
    - 0.0.12-preview.2
    - 0.0.12-preview.5
    - 0.0.12-preview.8
    - 0.0.12-preview.10
    - 0.0.12-preview.11
    - 0.0.12-preview.13
    - 0.0.12-preview.17
    - 0.0.12-preview.19
    - 0.0.12-preview.20
    - 1.0.0-preview.1
    - 1.0.1
    - 1.1.0-preview.1
    - 1.1.0
    - 1.2.1
    - 1.2.4
    - 1.2.5
    - 1.2.6
    - 1.3.1
    - 1.3.2
    compatible:
    - 1.3.2
    recommended: 1.3.2
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638409134840000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.mathematics@1.3/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/Unity.Mathematics.git
    revision: 1695a8503482a3131be78cc26308a93f82c05b04
    path: 
  unityLifecycle:
    version: 1.3.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n* Fixed `math.hash` crash when using IL2CPP
    builds on Arm 32 bit devices.\n* Fixed obsolete method usage warnings for `MatrixDrawer.CanCacheInspectorGUI`
    and `PrimitiveVectorDrawer.CanCacheInspectorGUI` in UNITY_2023_2_OR_NEWER.\n*
    Updated minimum editor version to 2021.3"}'
  assetStore:
    productId: 
  fingerprint: 8017b507cc74bf0a1dd14b18aa860569f807314d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.3.2
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.settings-manager@2.0.1
  testable: 0
  isDirectDependency: 0
  version: 2.0.1
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.settings-manager@56a930affa1e
  assetPath: Packages/com.unity.settings-manager
  name: com.unity.settings-manager
  displayName: Settings Manager
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: A framework for making any serializable field a setting, complete
    with a pre-built settings interface.
  errors: []
  versions:
    all:
    - 0.1.0-preview.4
    - 0.1.0-preview.8
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 2.0.0
    - 2.0.1
    - 2.1.0
    compatible:
    - 2.0.1
    - 2.1.0
    recommended: 2.0.1
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637744099390000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.com/Unity-Technologies/com.unity.settings-manager.git
    revision: 9b7c12d04ef880168151c127fd6b37b5931197dc
    path: 
  unityLifecycle:
    version: 2.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 56a930affa1e4462d3595b5c19a1c2a4bf2015d2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.1
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.services.core@1.14.0
  testable: 0
  isDirectDependency: 0
  version: 1.14.0
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.services.core@9e81a5e38245
  assetPath: Packages/com.unity.services.core
  name: com.unity.services.core
  displayName: Services Core
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: 'This package defines common components used by multiple Game Service
    packages.

    These are standardized and aim to unify the overall experience
    of working with Game Services SDK.'
  errors: []
  versions:
    all:
    - 1.0.1
    - 1.1.0-pre.7
    - 1.1.0-pre.8
    - 1.1.0-pre.9
    - 1.1.0-pre.10
    - 1.1.0-pre.11
    - 1.1.0-pre.41
    - 1.1.0-pre.69
    - 1.2.0
    - 1.3.1
    - 1.3.2
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.5.2
    - 1.6.0
    - 1.7.0
    - 1.7.1
    - 1.8.1
    - 1.8.2
    - 1.9.0
    - 1.10.1
    - 1.11.0
    - 1.12.0
    - 1.12.1
    - 1.12.2
    - 1.12.4
    - 1.12.5
    - 1.13.0
    - 1.14.0
    - 1.15.1
    compatible:
    - 1.14.0
    - 1.15.1
    recommended: 1.15.1
    deprecated: []
  dependencies:
  - name: com.unity.modules.androidjni
    version: 1.0.0
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.modules.androidjni
    version: 1.0.0
  - name: com.unity.nuget.newtonsoft-json
    version: 3.2.1
  - name: com.unity.modules.unitywebrequest
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638684041320270000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.services.core@1.14/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/operate-services-sdk.git
    revision: 64962022211031c9e26ceec024d3587ea8e05a17
    path: 
  unityLifecycle:
    version: 1.14.0
    nextVersion: 
    recommendedVersion: 1.15.1
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Added\n\n- Added `ServicesInitialization` monobehaviour
    to manage services in the scene authoring.\n- Added `ServicesBehaviour` abstract
    monobehaviour as a base for behaviours built on top of services.\n- Added unique
    identifier for custom service registry creation (`UnityServices.CreateServices`).\n-
    Added custom services registries dictionary in `UnityServices.Services`.\n- Added
    `GetIdentifier()` method to `IUnityServices` interface with a default implementation.\n\n###
    Changed\n\n- Updated the minimum supported Editor version to 2021.3."}'
  assetStore:
    productId: 
  fingerprint: 9e81a5e382457b799480b311ce51187be99abbbf
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.14.0
    minimumUnityVersion: 2021.3.0a1
- packageId: com.unity.render-pipelines.core@17.0.4
  testable: 0
  isDirectDependency: 0
  version: 17.0.4
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.render-pipelines.core@405083404c88
  assetPath: Packages/com.unity.render-pipelines.core
  name: com.unity.render-pipelines.core
  displayName: Core RP Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: SRP Core makes it easier to create or customize a Scriptable Render
    Pipeline (SRP). SRP Core contains reusable code, including boilerplate code for
    working with platform-specific graphics APIs, utility functions for common rendering
    operations, and  shader libraries. The code in SRP Core is use by the High Definition
    Render Pipeline (HDRP) and Universal Render Pipeline (URP). If you are creating
    a custom SRP from scratch or customizing a prebuilt SRP, using SRP Core will
    save you time.
  errors: []
  versions:
    all:
    - 0.1.21
    - 0.1.27
    - 0.1.28
    - 1.0.0-beta
    - 1.0.1-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.8-preview
    - 1.1.10-preview
    - 1.1.11-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.13
    - 9.0.0-preview.35
    - 9.0.0-preview.38
    - 9.0.0-preview.60
    - 9.0.0-preview.77
    - 10.0.0-preview.30
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.4
    compatible:
    - 17.0.4
    recommended: 17.0.4
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.14
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.collections
    version: 2.4.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 405083404c884ae2ef34e36ee955c00901a97d82
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.4
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.shadergraph@17.0.4
  testable: 0
  isDirectDependency: 0
  version: 17.0.4
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.shadergraph@bbf164badec6
  assetPath: Packages/com.unity.shadergraph
  name: com.unity.shadergraph
  displayName: Shader Graph
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The Shader Graph package adds a visual Shader editing tool to Unity.
    You can use this tool to create Shaders in a visual way instead of writing code.
    Specific render pipelines can implement specific graph features. Currently, both
    the High Definition Rendering Pipeline and the Universal Rendering Pipeline support
    Shader Graph.
  errors: []
  versions:
    all:
    - 0.1.8
    - 0.1.9
    - 0.1.17
    - 1.0.0-beta
    - 1.1.1-preview
    - 1.1.2-preview
    - 1.1.3-preview
    - 1.1.4-preview
    - 1.1.5-preview
    - 1.1.6-preview
    - 1.1.8-preview
    - 1.1.9-preview
    - 2.0.1-preview
    - 2.0.3-preview
    - 2.0.4-preview
    - 2.0.4-preview.1
    - 2.0.5-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 3.0.0-preview
    - 3.1.0-preview
    - 3.3.0-preview
    - 4.0.0-preview
    - 4.0.1-preview
    - 4.1.0-preview
    - 4.2.0-preview
    - 4.3.0-preview
    - 4.6.0-preview
    - 4.8.0-preview
    - 4.9.0-preview
    - 4.10.0-preview
    - 5.0.0-preview
    - 5.1.0
    - 5.2.0
    - 5.2.1
    - 5.2.2
    - 5.2.3
    - 5.3.1
    - 5.6.1
    - 5.7.2
    - 5.8.2
    - 5.9.0
    - 5.10.0
    - 5.13.0
    - 5.16.1
    - 6.5.2
    - 6.5.3
    - 6.7.1
    - 6.9.0
    - 6.9.1
    - 6.9.2
    - 7.0.0
    - 7.0.1
    - 7.1.1
    - 7.1.2
    - 7.1.5
    - 7.1.6
    - 7.1.7
    - 7.1.8
    - 7.2.0
    - 7.2.1
    - 7.3.1
    - 7.4.1
    - 7.4.2
    - 7.4.3
    - 7.5.1
    - 7.5.2
    - 7.5.3
    - 7.6.0
    - 7.7.0
    - 7.7.1
    - 8.0.1
    - 8.1.0
    - 8.2.0
    - 8.3.1
    - 9.0.0-preview.14
    - 9.0.0-preview.34
    - 9.0.0-preview.55
    - 9.0.0-preview.72
    - 10.0.0-preview.27
    - 10.1.0
    - 10.2.0
    - 10.2.1
    - 10.2.2
    - 10.3.1
    - 10.3.2
    - 10.4.0
    - 10.5.0
    - 10.5.1
    - 10.6.0
    - 10.7.0
    - 10.8.0
    - 10.8.1
    - 10.9.0
    - 10.10.0
    - 10.10.1
    - 17.0.4
    compatible:
    - 17.0.4
    recommended: 17.0.4
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.searcher
    version: 4.9.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  - name: com.unity.searcher
    version: 4.9.3
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: bbf164badec6da0a945c5b009463f82b6aaf7934
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.4
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.render-pipelines.universal-config@17.0.3
  testable: 0
  isDirectDependency: 0
  version: 17.0.3
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.render-pipelines.universal-config@fa63c96d3e1a
  assetPath: Packages/com.unity.render-pipelines.universal-config
  name: com.unity.render-pipelines.universal-config
  displayName: Universal RP Config
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Configuration files for the Universal Render Pipeline.
  errors: []
  versions:
    all:
    - 17.0.3
    compatible:
    - 17.0.3
    recommended: 17.0.3
    deprecated: []
  dependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.3
  resolvedDependencies:
  - name: com.unity.render-pipelines.core
    version: 17.0.4
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.physics
    version: 1.0.0
  - name: com.unity.modules.terrain
    version: 1.0.0
  - name: com.unity.rendering.light-transport
    version: 1.0.1
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 17.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: fa63c96d3e1af317d84d171cd6c0e0658ab159df
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 17.0.3
    minimumUnityVersion: 6000.0.0a1
- packageId: com.unity.ext.nunit@2.0.5
  testable: 0
  isDirectDependency: 0
  version: 2.0.5
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.ext.nunit@031a54704bff
  assetPath: Packages/com.unity.ext.nunit
  name: com.unity.ext.nunit
  displayName: Custom NUnit
  author:
    name: 
    email: 
    url: 
  category: Libraries
  type: 
  description: A custom version of NUnit used by Unity Test Framework. Based on NUnit
    version 3.5 and works with all platforms, il2cpp and Mono AOT.
  errors: []
  versions:
    all:
    - 0.1.5-preview
    - 0.1.6-preview
    - 0.1.9-preview
    - 1.0.0
    - 1.0.5
    - 1.0.6
    - 2.0.2
    - 2.0.3
    - 2.0.4
    - 2.0.5
    compatible:
    - 2.0.5
    recommended: 2.0.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - nunit
  - unittest
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ext.nunit@2.0/manual/index.html
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 2.0.5
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 031a54704bffe39e6a0324909f8eaa4565bdebf2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.5
    minimumUnityVersion: 2019.4.0a1
- packageId: com.unity.ide.visualstudio@2.0.23
  testable: 0
  isDirectDependency: 0
  version: 2.0.23
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.ide.visualstudio@198cdf337d13
  assetPath: Packages/com.unity.ide.visualstudio
  name: com.unity.ide.visualstudio
  displayName: Visual Studio Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Code editor integration for supporting Visual Studio as code editor
    for unity. Adds support for generating csproj files for intellisense purposes,
    auto discovery of installations, etc.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.3
    - 1.0.4
    - 1.0.9
    - 1.0.10
    - 1.0.11
    - 2.0.0
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 2.0.8
    - 2.0.9
    - 2.0.11
    - 2.0.12
    - 2.0.14
    - 2.0.15
    - 2.0.16
    - 2.0.17
    - 2.0.18
    - 2.0.20
    - 2.0.21
    - 2.0.22
    - 2.0.23
    compatible:
    - 2.0.22
    - 2.0.23
    recommended: 2.0.23
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.9
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638786696173840000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.visualstudio@2.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.visualstudio.git
    revision: 0fe3b29f9aff2b90b9f0962ae35036a824d3dd6b
    path: 
  unityLifecycle:
    version: 2.0.22
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"Integration:\n\n- Monitor `additionalfile` extension
    by default.\n- Try opening a Visual Studio Code workspace if there''s one (`.code-workspace`
    file in the Unity project).\n\nProject generation:\n\n- Identify `asset`, `meta`,
    `prefab` and `unity` files as `yaml` (Visual Studio Code).\n- Add `sln`/`csproj`
    file nesting (Visual Studio Code).\n- Improve SDK style project generation."}'
  assetStore:
    productId: 
  fingerprint: 198cdf337d13c83ca953581515630d66b779e92b
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.0.22
    minimumUnityVersion: 2019.4.25f1
- packageId: com.unity.ide.rider@3.0.36
  testable: 0
  isDirectDependency: 1
  version: 3.0.36
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.ide.rider@4d374c7eb6db
  assetPath: Packages/com.unity.ide.rider
  name: com.unity.ide.rider
  displayName: JetBrains Rider Editor
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: The JetBrains Rider Editor package provides an integration for using
    the JetBrains Rider IDE as a code editor for Unity. It adds support for generating
    .csproj files for code completion and auto-discovery of installations.
  errors: []
  versions:
    all:
    - 1.0.2
    - 1.0.6
    - 1.0.8
    - 1.1.0
    - 1.1.1
    - 1.1.2-preview
    - 1.1.2-preview.2
    - 1.1.3-preview.1
    - 1.1.4-preview
    - 1.1.4
    - 1.2.0-preview
    - 1.2.1
    - 2.0.0-preview
    - 2.0.1
    - 2.0.2
    - 2.0.3
    - 2.0.5
    - 2.0.7
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.9
    - 3.0.10
    - 3.0.12
    - 3.0.13
    - 3.0.14
    - 3.0.15
    - 3.0.16
    - 3.0.17
    - 3.0.18
    - 3.0.20
    - 3.0.21
    - 3.0.22
    - 3.0.24
    - 3.0.25
    - 3.0.26
    - 3.0.27
    - 3.0.28
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    compatible:
    - 3.0.31
    - 3.0.34
    - 3.0.35
    - 3.0.36
    recommended: 3.0.36
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 1.0.6
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638799562419060000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.ide.rider@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.ide.rider.git
    revision: b5315083ab3861d21f6ab2ed0d9514daf04bf208
    path: 
  unityLifecycle:
    version: 3.0.31
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- fix RIDER-124592 Avoid affecting \"Strip Engine Code\"
    while IL2CPP debug enabled"}'
  assetStore:
    productId: 
  fingerprint: 4d374c7eb6db6907c7e6925e3086c3c73f926e13
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.31
    minimumUnityVersion: 2019.4.6f1
- packageId: com.unity.editorcoroutines@1.0.0
  testable: 0
  isDirectDependency: 0
  version: 1.0.0
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.editorcoroutines@7d48783e7b8c
  assetPath: Packages/com.unity.editorcoroutines
  name: com.unity.editorcoroutines
  displayName: Editor Coroutines
  author:
    name: 
    email: 
    url: 
  category: 
  type: asset
  description: 'The editor coroutines package allows developers to start constructs
    similar to Unity''s monobehaviour based coroutines within the editor using abitrary
    objects. '
  errors: []
  versions:
    all:
    - 0.0.1-preview.3
    - 0.0.1-preview.4
    - 0.0.1-preview.5
    - 0.0.2-preview.1
    - 0.1.0-preview.1
    - 0.1.0-preview.2
    - 1.0.0
    compatible:
    - 1.0.0
    recommended: 1.0.0
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - coroutine
  - coroutines
  - editor
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637232611380000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.editorcoroutines.git
    revision: f67fc9992bbc7a553b17375de53a8b2db136528e
    path: 
  unityLifecycle:
    version: 1.0.0
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 7d48783e7b8cfcee5f8ef9ba787ed0d9dad4ebca
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.0
    minimumUnityVersion: 2018.1.0a1
- packageId: com.unity.performance.profile-analyzer@1.2.3
  testable: 0
  isDirectDependency: 0
  version: 1.2.3
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.performance.profile-analyzer@a68e7bc84997
  assetPath: Packages/com.unity.performance.profile-analyzer
  name: com.unity.performance.profile-analyzer
  displayName: Profile Analyzer
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: "The Profile Analyzer tool supports the standard Unity Profiler. You
    can use it to analyze multiple frames and multiple data sets of the CPU data
    in the Profiler.\n\nMain features: \n\u25AA Multi-frame analysis of a single
    set of Profiler CPU data \n\u25AA Comparison of two multi-frame profile scans
    \n\n"
  errors: []
  versions:
    all:
    - 0.4.0-preview.3
    - 0.4.0-preview.5
    - 0.4.0-preview.6
    - 0.5.0-preview.1
    - 0.6.0-preview.1
    - 0.7.0-preview.4
    - 1.0.0
    - 1.0.1
    - 1.0.2
    - 1.0.3
    - 1.1.0-pre.2
    - 1.1.0
    - 1.1.1
    - 1.2.2
    - 1.2.3
    compatible:
    - 1.2.3
    recommended: 1.2.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638699545898200000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.performance.profile-analyzer@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.performance.profile-analyzer.git
    revision: 835e61d94bd201d0dea5763dff75e86b6b61de29
    path: 
  unityLifecycle:
    version: 1.2.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n\n* Fixed minimum supported version in documentation.\n*
    Fixed PROFB-199; Unchecking ''Hide Removed Markers'' doesn''t work."}'
  assetStore:
    productId: 
  fingerprint: a68e7bc849973d943853204178d08a2bc7656ffe
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.3
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.test-framework@1.5.1
  testable: 0
  isDirectDependency: 0
  version: 1.5.1
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.test-framework@388540dd9ce6
  assetPath: Packages/com.unity.test-framework
  name: com.unity.test-framework
  displayName: Test Framework
  author:
    name: 
    email: 
    url: 
  category: Unity Test Framework
  type: 
  description: Test framework for running Edit mode and Play mode tests in Unity.
  errors: []
  versions:
    all:
    - 0.0.4-preview
    - 0.0.29-preview
    - 1.0.0
    - 1.0.7
    - 1.0.9
    - 1.0.11
    - 1.0.12
    - 1.0.13
    - 1.0.14
    - 1.0.16
    - 1.0.17
    - 1.0.18
    - 1.1.0
    - 1.1.1
    - 1.1.2
    - 1.1.3
    - 1.1.5
    - 1.1.8
    - 1.1.9
    - 1.1.11
    - 1.1.13
    - 1.1.14
    - 1.1.16
    - 1.1.18
    - 1.1.19
    - 1.1.20
    - 1.1.22
    - 1.1.24
    - 1.1.26
    - 1.1.27
    - 1.1.29
    - 1.1.30
    - 1.1.31
    - 1.1.33
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.5.1
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    compatible:
    - 1.5.1
    - 2.0.1-exp.1
    - 2.0.1-exp.2
    - 2.0.1-pre.12
    - 2.0.1-pre.18
    recommended: 1.5.1
    deprecated: []
  dependencies:
  - name: com.unity.ext.nunit
    version: 2.0.3
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - Test
  - TestFramework
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 388540dd9ce60b7f4463a23bfdf0d2dafa9a6f3e
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.5.1
    minimumUnityVersion: 2022.3.0a1
- packageId: com.unity.testtools.codecoverage@1.2.6
  testable: 0
  isDirectDependency: 0
  version: 1.2.6
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.testtools.codecoverage@205a02cbcb39
  assetPath: Packages/com.unity.testtools.codecoverage
  name: com.unity.testtools.codecoverage
  displayName: Code Coverage
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Use this package to export code coverage data and reports from your
    automated tests. Additionally, the Code Coverage package offers a Coverage Recording
    feature which allows capturing coverage data on demand, for manual testing or
    when there are no automated tests in the project.
  errors: []
  versions:
    all:
    - 0.2.0-preview
    - 0.2.1-preview
    - 0.2.2-preview
    - 0.2.3-preview
    - 0.3.0-preview
    - 0.3.1-preview
    - 0.4.0-preview
    - 0.4.1-preview
    - 0.4.2-preview
    - 0.4.3-preview
    - 1.0.0-pre.1
    - 1.0.0-pre.2
    - 1.0.0-pre.3
    - 1.0.0-pre.4
    - 1.0.0
    - 1.0.1
    - 1.1.0
    - 1.1.1
    - 1.2.0-exp.1
    - 1.2.0-exp.2
    - 1.2.0-exp.3
    - 1.2.0-exp.4
    - 1.2.0-exp.5
    - 1.2.0-exp.6
    - 1.2.0-exp.7
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.2.5
    - 1.2.6
    compatible:
    - 1.2.6
    recommended: 1.2.6
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.0.16
  - name: com.unity.settings-manager
    version: 1.0.1
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.settings-manager
    version: 2.0.1
  keywords:
  - test
  - coverage
  - testing
  - opencover
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638575857900000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.testtools.codecoverage.git
    revision: 959db8ff97eedf9b211ad5cf320b87ac01f1e90f
    path: 
  unityLifecycle:
    version: 1.2.6
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixes\n- Documentation: Fixed formatting in [Using
    Code Coverage in batchmode](https://docs.unity3d.com/Packages/com.unity.testtools.codecoverage@1.2/manual/CoverageBatchmode.html)
    page (case [COV-40](https://issuetracker.unity3d.com/issues/docs-formatting-in-using-code-coverage-in-batchmode-docs-page-is-incorrect)).\n-
    Removed the references to the deprecated FindObjectOfType method in the *Asteroids
    sample project* (case [COV-42](https://issuetracker.unity3d.com/issues/sample-project-is-using-obsolete-findobjectoftype-method-which-causes-multiple-warnings-in-console-when-it-is-imported)).\n-
    Added missing logs for the ReportGenerator (case [COV-46](https://issuetracker.unity3d.com/issues/code-coverage-package-does-not-report-some-of-the-internal-reportgenerator-errors))."}'
  assetStore:
    productId: 
  fingerprint: 205a02cbcb39584f20b51c49b853047aceb3a3a7
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.2.6
    minimumUnityVersion: 2019.3.0a1
- packageId: com.unity.2d.common@9.0.7
  testable: 0
  isDirectDependency: 0
  version: 9.0.7
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.2d.common@bb1fc9b3d81b
  assetPath: Packages/com.unity.2d.common
  name: com.unity.2d.common
  displayName: 2D Common
  author:
    name: 
    email: 
    url: 
  category: 2D
  type: 
  description: 2D Common is a package that contains shared functionalities that are
    used by most of the other 2D packages.
  errors: []
  versions:
    all:
    - 1.0.9-preview.1
    - 1.0.9-preview.2
    - 1.0.10-preview
    - 1.0.11-preview.1
    - 1.1.0-preview.1
    - 1.1.0-preview.2
    - 1.2.0-preview.1
    - 2.0.1
    - 2.0.2
    - 2.1.0
    - 2.1.1
    - 2.1.2
    - 3.0.0
    - 3.0.1
    - 4.0.0
    - 4.0.1
    - 4.0.2
    - 4.0.3
    - 4.0.4
    - 4.1.0
    - 4.2.0
    - 4.2.1
    - 5.0.0-pre.1
    - 5.0.0-pre.2
    - 5.0.0
    - 6.0.0-pre.2
    - 6.0.0-pre.3
    - 6.0.0-pre.4
    - 6.0.0
    - 6.0.1
    - 6.0.2
    - 6.0.3
    - 6.0.4
    - 6.0.5
    - 6.0.6
    - 6.0.7
    - 6.0.8
    - 6.1.0
    - 7.0.0-pre.3
    - 7.0.0-pre.4
    - 7.0.0
    - 7.0.1
    - 7.0.2
    - 7.0.3
    - 8.0.0-pre.1
    - 8.0.0-pre.2
    - 8.0.0
    - 8.0.1
    - 8.0.2
    - 8.0.3
    - 8.0.4
    - 8.1.0
    - 8.1.1
    - 9.0.0-pre.1
    - 9.0.0-pre.2
    - 9.0.0
    - 9.0.1
    - 9.0.2
    - 9.0.3
    - 9.0.4
    - 9.0.5
    - 9.0.6
    - 9.0.7
    - 9.1.0
    - 9.1.1
    - 10.0.0
    - 11.0.0
    - 11.0.1
    - 12.0.0
    compatible:
    - 9.0.7
    - 9.1.0
    - 9.1.1
    recommended: 9.0.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.4
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.mathematics
    version: 1.1.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.2d.sprite
    version: 1.0.0
  - name: com.unity.modules.animation
    version: 1.0.0
  - name: com.unity.modules.uielements
    version: 1.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.hierarchycore
    version: 1.0.0
  keywords:
  - 2d
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638654459039420000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.2d.common@9.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/2d.git
    revision: c393ad93bdba3e78ebd30a5ccd2e6da5d8b92aba
    path: 
  unityLifecycle:
    version: 9.0.7
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Fixed\n- DANB-638 Fixed Error \"InvalidOperationException:
    HTTP/1.1 404 Not Found\" logged when entering Play Mode in 2D Common Sample Scene\n-
    DANB-637 Fixed Sprite Atlases included in the 2D Common Package Sample \"Sprite
    Atlas Samples\" are blurry even though they are uncompressed"}'
  assetStore:
    productId: 
  fingerprint: bb1fc9b3d81b3bb452c6708e8c088fe4224a0369
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 9.0.7
    minimumUnityVersion: 2023.1.0a1
- packageId: com.unity.textmeshpro@5.0.0
  testable: 0
  isDirectDependency: 0
  version: 5.0.0
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.textmeshpro@53f43edfd2e0
  assetPath: Packages/com.unity.textmeshpro
  name: com.unity.textmeshpro
  displayName: TextMeshPro
  author:
    name: 
    email: 
    url: 
  category: Text Rendering
  type: shim
  description: This package is no longer supported. TextMeshPro functionalities are
    now included in the com.unity.ugui package. To continue using them, consider
    adding it to your project dependencies.
  errors: []
  versions:
    all:
    - 0.1.2
    - 1.0.21
    - 1.0.23
    - 1.0.25
    - 1.0.26
    - 1.1.0
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.2.4
    - 1.3.0-preview
    - 1.3.0
    - 1.4.0-preview.1b
    - 1.4.0-preview.2a
    - 1.4.0-preview.3a
    - 1.4.0
    - 1.4.1-preview.1
    - 1.4.1
    - 1.5.0-preview.1
    - 1.5.0-preview.2
    - 1.5.0-preview.3
    - 1.5.0-preview.4
    - 1.5.0-preview.5
    - 1.5.0-preview.6
    - 1.5.0-preview.7
    - 1.5.0-preview.8
    - 1.5.0-preview.10
    - 1.5.0-preview.11
    - 1.5.0-preview.12
    - 1.5.0-preview.13
    - 1.5.0-preview.14
    - 1.5.0
    - 1.5.1
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6
    - 1.6.0-preview.1
    - 2.0.0
    - 2.0.1-preview.1
    - 2.0.1
    - 2.1.0-preview.1
    - 2.1.0-preview.2
    - 2.1.0-preview.3
    - 2.1.0-preview.4
    - 2.1.0-preview.5
    - 2.1.0-preview.7
    - 2.1.0-preview.8
    - 2.1.0-preview.10
    - 2.1.0-preview.11
    - 2.1.0-preview.12
    - 2.1.0-preview.13
    - 2.1.0-preview.14
    - 2.1.0
    - 2.1.1
    - 2.1.3
    - 2.1.4
    - 2.1.5
    - 2.1.6
    - 2.2.0-preview.1
    - 2.2.0-preview.2
    - 2.2.0-preview.3
    - 3.0.0-preview.1
    - 3.0.0-preview.3
    - 3.0.0-preview.4
    - 3.0.0-preview.5
    - 3.0.0-preview.7
    - 3.0.0-preview.8
    - 3.0.0-preview.10
    - 3.0.0-preview.11
    - 3.0.0-preview.12
    - 3.0.0-preview.13
    - 3.0.0-preview.14
    - 3.0.0
    - 3.0.1
    - 3.0.3
    - 3.0.4
    - 3.0.5
    - 3.0.6
    - 3.0.7
    - 3.0.8
    - 3.0.9
    - 3.2.0-pre.1
    - 3.2.0-pre.2
    - 3.2.0-pre.3
    - 3.2.0-pre.4
    - 3.2.0-pre.5
    - 3.2.0-pre.6
    - 3.2.0-pre.7
    - 3.2.0-pre.8
    - 3.2.0-pre.9
    - 3.2.0-pre.10
    - 3.2.0-pre.11
    - 3.2.0-pre.12
    - 4.0.0-pre.1
    - 4.0.0-pre.2
    - 5.0.0
    compatible: []
    recommended: 
    deprecated: []
  dependencies:
  - name: com.unity.ugui
    version: 2.0.0
  resolvedDependencies:
  - name: com.unity.ugui
    version: 2.0.0
  - name: com.unity.modules.ui
    version: 1.0.0
  - name: com.unity.modules.imgui
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 1
    deprecationMessage: This package is no longer supported. TextMeshPro functionalities
      are now included in the com.unity.ugui package. To continue using them, consider
      adding it to your project dependencies.
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 53f43edfd2e0bf2438ca6e2038ba12107a181253
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 
    minimumUnityVersion: 2023.2.0a1
- packageId: com.unity.searcher@4.9.3
  testable: 0
  isDirectDependency: 0
  version: 4.9.3
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.searcher@1e17ce91558d
  assetPath: Packages/com.unity.searcher
  name: com.unity.searcher
  displayName: Searcher
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: General search window for use in the Editor. First target use is for
    GraphView node search.
  errors: []
  versions:
    all:
    - 4.0.0-preview
    - 4.0.0
    - 4.0.7-preview
    - 4.0.7
    - 4.0.8-preview
    - 4.0.9
    - 4.1.0-preview
    - 4.1.0
    - 4.2.0
    - 4.3.0
    - 4.3.1
    - 4.3.2
    - 4.6.0-preview
    - 4.7.0-preview
    - 4.9.1
    - 4.9.2
    - 4.9.3
    compatible:
    - 4.9.2
    - 4.9.3
    recommended: 4.9.3
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords:
  - search
  - searcher
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638731478077990000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.searcher@4.9/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.searcher.git
    revision: 6fad693b6604ae7175b59ebb4990d9a0b6c1d012
    path: 
  unityLifecycle:
    version: 4.9.2
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"- Fixed a bug where spaces are removed when highlighted.\n-
    Changed VisualSplitter to twoPaneSplitView and updated indentation"}'
  assetStore:
    productId: 
  fingerprint: 1e17ce91558d1d9127554adc03d275f39a7466a2
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 4.9.2
    minimumUnityVersion: 2019.1.0a1
- packageId: com.unity.burst@1.8.19
  testable: 0
  isDirectDependency: 0
  version: 1.8.19
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.burst@7a907cf5a459
  assetPath: Packages/com.unity.burst
  name: com.unity.burst
  displayName: Burst
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Burst is a compiler that translates from IL/.NET bytecode to highly
    optimized native code using LLVM.
  errors: []
  versions:
    all:
    - 0.2.4-preview.5
    - 0.2.4-preview.7
    - 0.2.4-preview.11
    - 0.2.4-preview.12
    - 0.2.4-preview.13
    - 0.2.4-preview.14
    - 0.2.4-preview.15
    - 0.2.4-preview.16
    - 0.2.4-preview.17
    - 0.2.4-preview.18
    - 0.2.4-preview.19
    - 0.2.4-preview.20
    - 0.2.4-preview.21
    - 0.2.4-preview.22
    - 0.2.4-preview.23
    - 0.2.4-preview.24
    - 0.2.4-preview.25
    - 0.2.4-preview.30
    - 0.2.4-preview.31
    - 0.2.4-preview.33
    - 0.2.4-preview.34
    - 0.2.4-preview.37
    - 0.2.4-preview.41
    - 0.2.4-preview.45
    - 0.2.4-preview.48
    - 0.2.4-preview.50
    - 1.1.0-preview.2
    - 1.1.0-preview.3
    - 1.1.0-preview.4
    - 1.1.1
    - 1.1.2
    - 1.1.3-preview.3
    - 1.2.0-preview.1
    - 1.2.0-preview.5
    - 1.2.0-preview.6
    - 1.2.0-preview.8
    - 1.2.0-preview.9
    - 1.2.0-preview.10
    - 1.2.0-preview.11
    - 1.2.0-preview.12
    - 1.2.0
    - 1.2.1
    - 1.2.2
    - 1.2.3
    - 1.3.0-preview.1
    - 1.3.0-preview.2
    - 1.3.0-preview.3
    - 1.3.0-preview.4
    - 1.3.0-preview.5
    - 1.3.0-preview.6
    - 1.3.0-preview.7
    - 1.3.0-preview.8
    - 1.3.0-preview.9
    - 1.3.0-preview.10
    - 1.3.0-preview.11
    - 1.3.0-preview.12
    - 1.3.0-preview.13
    - 1.3.0
    - 1.3.1
    - 1.3.2
    - 1.3.3
    - 1.3.4
    - 1.3.5
    - 1.3.6
    - 1.3.7
    - 1.3.8
    - 1.3.9
    - 1.4.0-pre.1
    - 1.4.0-preview.1
    - 1.4.0-preview.2
    - 1.4.0-preview.3
    - 1.4.0-preview.4
    - 1.4.0-preview.5
    - 1.4.1-pre.1
    - 1.4.1-pre.2
    - 1.4.1
    - 1.4.2
    - 1.4.3
    - 1.4.4-preview.1
    - 1.4.4-preview.2
    - 1.4.4
    - 1.4.5
    - 1.4.6
    - 1.4.7
    - 1.4.8
    - 1.4.9
    - 1.4.11
    - 1.5.0-pre.3
    - 1.5.0-pre.4
    - 1.5.0-pre.5
    - 1.5.0
    - 1.5.1
    - 1.5.2
    - 1.5.3
    - 1.5.4
    - 1.5.5
    - 1.5.6-preview.1
    - 1.5.6
    - 1.6.0-pre.2
    - 1.6.0-pre.3
    - 1.6.0-pre.4
    - 1.6.0
    - 1.6.1
    - 1.6.2
    - 1.6.3
    - 1.6.4
    - 1.6.5
    - 1.6.6
    - 1.7.0-pre.1
    - 1.7.0
    - 1.7.1
    - 1.7.2
    - 1.7.3
    - 1.7.4
    - 1.8.0-pre.1
    - 1.8.0-pre.2
    - 1.8.0
    - 1.8.1
    - 1.8.2
    - 1.8.3
    - 1.8.4
    - 1.8.7
    - 1.8.8
    - 1.8.9
    - 1.8.10
    - 1.8.11
    - 1.8.12
    - 1.8.13
    - 1.8.14
    - 1.8.15
    - 1.8.16
    - 1.8.17
    - 1.8.18
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    - 1.8.24
    compatible:
    - 1.8.19
    - 1.8.20
    - 1.8.21
    - 1.8.22
    - 1.8.23
    - 1.8.24
    recommended: 1.8.24
    deprecated: []
  dependencies:
  - name: com.unity.mathematics
    version: 1.2.1
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638731397665570000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.burst@1.8/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/burst.git
    revision: 6498e90e8843e51fdb3a37b709a1f1e4b86d3e57
    path: 
  unityLifecycle:
    version: 1.8.19
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n- Source file checksums are now included
    in pdb debugging files, so that Visual Studio can warn when source files differ
    from when the pdb was built\n- Added support for `HashCode.Combine`\n\n### Fixed\n-
    Fixed hash generation error for derived default interface methods\n- Fixed resolution
    of local variable types in generic default interface methods\n- Fixed crash that
    could occur when a target method of `BurstCompiler.CompileFunctionPointer` was
    already decorated with `[MonoPInvokeCallback]`. If this existing attribute existed
    in a namespace other than `AOT`, Burst''s IL postprocessor would add a second
    `[MonoPInvokeCallback]`, resulting in a runtime crash on IL2CPP.\n- Fix crash
    when trying to Burst compile a project without any Bursted code with debug info
    enabled, when it has already been compiled without debug info before.\n- Fixed
    `BC1055: Unable to resolve the definition of the method ...` errors when compiling
    code using `in` method parameters for multiple CPU targets\n- Fixed an issue
    preventing debugging of managed methods that use direct call, regardless of whether
    Burst compilation is manually disabled\n- Fixed a rare concurrency issue in the
    entry point finder.\n\n### Added\n- [Android] Support for 16Kb page sizes\n\n###
    Removed\n\n### Known Issues"}'
  assetStore:
    productId: 
  fingerprint: 7a907cf5a4591e4e25f4203e3be84f240058c34d
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.8.19
    minimumUnityVersion: 2020.3.0a1
- packageId: com.unity.collections@2.5.1
  testable: 0
  isDirectDependency: 0
  version: 2.5.1
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.collections@56bff8827a7e
  assetPath: Packages/com.unity.collections
  name: com.unity.collections
  displayName: Collections
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: A C# collections library providing data structures that can be used
    in jobs, and optimized by Burst compiler.
  errors: []
  versions:
    all:
    - 0.0.9-preview.1
    - 0.0.9-preview.2
    - 0.0.9-preview.3
    - 0.0.9-preview.4
    - 0.0.9-preview.5
    - 0.0.9-preview.6
    - 0.0.9-preview.7
    - 0.0.9-preview.8
    - 0.0.9-preview.9
    - 0.0.9-preview.10
    - 0.0.9-preview.11
    - 0.0.9-preview.12
    - 0.0.9-preview.13
    - 0.0.9-preview.14
    - 0.0.9-preview.15
    - 0.0.9-preview.16
    - 0.0.9-preview.17
    - 0.0.9-preview.18
    - 0.0.9-preview.19
    - 0.0.9-preview.20
    - 0.1.0-preview
    - 0.1.1-preview
    - 0.2.0-preview.13
    - 0.3.0-preview.0
    - 0.4.0-preview.6
    - 0.5.0-preview.9
    - 0.5.1-preview.11
    - 0.5.2-preview.8
    - 0.6.0-preview.9
    - 0.7.0-preview.2
    - 0.7.1-preview.3
    - 0.8.0-preview.5
    - 0.9.0-preview.5
    - 0.9.0-preview.6
    - 0.11.0-preview.17
    - 0.12.0-preview.13
    - 0.14.0-preview.16
    - 0.15.0-preview.21
    - 0.17.0-preview.18
    - 1.0.0-pre.3
    - 1.0.0-pre.5
    - 1.0.0-pre.6
    - 1.1.0
    - 1.2.3-pre.1
    - 1.2.3
    - 1.2.4
    - 1.3.1
    - 1.4.0
    - 1.5.1
    - 1.5.2
    - 2.1.0-exp.4
    - 2.1.0-pre.2
    - 2.1.0-pre.6
    - 2.1.0-pre.11
    - 2.1.0-pre.18
    - 2.1.1
    - 2.1.4
    - 2.2.0
    - 2.2.1
    - 2.3.0-exp.1
    - 2.3.0-pre.3
    - 2.4.0-exp.2
    - 2.4.0-pre.2
    - 2.4.0-pre.5
    - 2.4.0
    - 2.4.1
    - 2.4.2
    - 2.4.3
    - 2.5.0-exp.1
    - 2.5.0-pre.2
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    compatible:
    - 2.5.1
    - 2.5.2
    - 2.5.3
    - 2.5.7
    - 2.6.0-exp.2
    - 2.6.0-pre.3
    - 2.6.0-pre.4
    recommended: 2.5.7
    deprecated: []
  dependencies:
  - name: com.unity.burst
    version: 1.8.17
  - name: com.unity.test-framework
    version: 1.4.5
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  resolvedDependencies:
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  keywords:
  - dots
  - collections
  - unity
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638621282722800000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.collections@2.5/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/dots.git
    revision: 5b0dea6b455f5df005c19fa984ddfa237d6cd707
    path: 
  unityLifecycle:
    version: 2.5.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"### Changed\n* Updated Burst dependency to version
    1.8.17\n* Updated Unity Test Framework dependency to version 1.4.5\n* Updated
    entities packages dependencies\n\n### Fixed\n* Certain cases would cause an ILPostProcessor
    to fail, blocking compilation, but no more."}'
  assetStore:
    productId: 
  fingerprint: 56bff8827a7ef6d44fcee4f36e558a74da89c1a0
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 2.5.1
    minimumUnityVersion: 2022.3.11f1
- packageId: com.unity.rendering.light-transport@1.0.1
  testable: 0
  isDirectDependency: 0
  version: 1.0.1
  source: 2
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.rendering.light-transport@307bc27a498f
  assetPath: Packages/com.unity.rendering.light-transport
  name: com.unity.rendering.light-transport
  displayName: Unity Light Transport Library
  author:
    name: 
    email: 
    url: 
  category: 
  type: 
  description: Unity Light Transport Library exposes reusable code for writing light
    transport algorithms such as raytracing or pathtracing
  errors: []
  versions:
    all:
    - 1.0.1
    compatible:
    - 1.0.1
    recommended: 1.0.1
    deprecated: []
  dependencies:
  - name: com.unity.collections
    version: 2.2.0
  - name: com.unity.mathematics
    version: 1.2.4
  - name: com.unity.modules.terrain
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.collections
    version: 2.5.1
  - name: com.unity.burst
    version: 1.8.19
  - name: com.unity.mathematics
    version: 1.3.2
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.nuget.mono-cecil
    version: 1.11.4
  - name: com.unity.test-framework.performance
    version: 3.0.3
  - name: com.unity.modules.terrain
    version: 1.0.0
  keywords:
  - raytracing
  - pathtracing
  - monte-carlo
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 0
  isAssetStorePackage: 0
  datePublishedTicks: 0
  documentationUrl: 
  hasRepository: 0
  repository:
    type: 
    url: 
    revision: 
    path: 
  unityLifecycle:
    version: 1.0.1
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: 307bc27a498fc9cd409bbd426c85d8dc7f140bc1
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.0.1
    minimumUnityVersion: 2023.3.0b1
- packageId: com.unity.nuget.mono-cecil@1.11.4
  testable: 0
  isDirectDependency: 0
  version: 1.11.4
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.nuget.mono-cecil@d6f9955a5d5f
  assetPath: Packages/com.unity.nuget.mono-cecil
  name: com.unity.nuget.mono-cecil
  displayName: Mono Cecil
  author:
    name: 
    email: 
    url: 
  category: 
  type: library
  description: 'The mono cecil library from https://www.nuget.org/packages/Mono.Cecil/


    This
    package is intended for internal Unity use only. Most Unity users will be better
    suite using the existing community tooling.

    To avoid assembly clashes, please
    use this package if you intend to use Mono.Cecil.'
  errors: []
  versions:
    all:
    - 0.1.6-preview.2
    - 1.0.0-preview.1
    - 1.10.0-preview.1
    - 1.10.1-preview.1
    - 1.10.1
    - 1.10.2
    - 1.11.4
    - 1.11.5
    compatible:
    - 1.11.4
    - 1.11.5
    recommended: 1.11.5
    deprecated: []
  dependencies: []
  resolvedDependencies: []
  keywords: []
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 637852971930000000
  documentationUrl: 
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.nuget.mono-cecil.git
    revision: d0133ce672d724694b56bfd20672acf6f8737fec
    path: 
  unityLifecycle:
    version: 1.11.4
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: 
  assetStore:
    productId: 
  fingerprint: d6f9955a5d5f84d45442ff1ad0fb694cc6e2fd62
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 1.11.4
    minimumUnityVersion: 2018.4.0a1
- packageId: com.unity.test-framework.performance@3.0.3
  testable: 0
  isDirectDependency: 0
  version: 3.0.3
  source: 1
  resolvedPath: C:\Users\<USER>\Documents\btv-genex-golf-app\unity\Library\PackageCache\com.unity.test-framework.performance@fb0dc592af8b
  assetPath: Packages/com.unity.test-framework.performance
  name: com.unity.test-framework.performance
  displayName: Performance testing API
  author:
    name: 
    email: 
    url: 
  category: 
  type: assets
  description: Package that extends Unity Test Framework package. Adds performance
    testing capabilities and collects configuration metadata.
  errors: []
  versions:
    all:
    - 0.1.27-preview
    - 0.1.29-preview
    - 0.1.31-preview
    - 0.1.33-preview
    - 0.1.34-preview
    - 0.1.36-preview
    - 0.1.37-preview
    - 0.1.39-preview
    - 0.1.40-preview
    - 0.1.41-preview
    - 0.1.42-preview
    - 0.1.44-preview
    - 0.1.45-preview
    - 0.1.47-preview
    - 0.1.48-preview
    - 0.1.49-preview
    - 0.1.50-preview
    - 1.0.4-preview
    - 1.0.6-preview
    - 1.0.9-preview
    - 1.1.2-preview
    - 1.2.0-preview
    - 1.2.1-preview
    - 1.2.3-preview
    - 1.2.5-preview
    - 1.2.6-preview
    - 1.3.0-preview
    - 1.3.1-preview
    - 1.3.2-preview
    - 1.3.3-preview
    - 2.0.1-preview
    - 2.0.2-preview
    - 2.0.3-preview
    - 2.0.6-preview
    - 2.0.7-preview
    - 2.0.8-preview
    - 2.0.9-preview
    - 2.1.0-preview
    - 2.2.0-preview
    - 2.3.1-preview
    - 2.4.1-preview
    - 2.5.1-preview
    - 2.6.0-preview
    - 2.7.0-preview
    - 2.8.0-preview
    - 2.8.1-preview
    - 3.0.0-pre.1
    - 3.0.0-pre.2
    - 3.0.1
    - 3.0.2
    - 3.0.3
    - 3.1.0
    compatible:
    - 3.0.3
    - 3.1.0
    recommended: 3.0.3
    deprecated: []
  dependencies:
  - name: com.unity.test-framework
    version: 1.1.31
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  resolvedDependencies:
  - name: com.unity.test-framework
    version: 1.5.1
  - name: com.unity.ext.nunit
    version: 2.0.5
  - name: com.unity.modules.imgui
    version: 1.0.0
  - name: com.unity.modules.jsonserialize
    version: 1.0.0
  keywords:
  - performance
  - test
  registry:
    id: main
    name: 
    url: https://packages.unity.com
    scopes: []
    isDefault: 1
    capabilities: 7
    configSource: 0
  hideInEditor: 1
  entitlements:
    licensingModel: 1
  isAssetStorePackage: 0
  datePublishedTicks: 638301960470000000
  documentationUrl: https://docs.unity3d.com/Packages/com.unity.test-framework.performance@3.0/manual/index.html
  hasRepository: 1
  repository:
    type: git
    url: https://github.cds.internal.unity3d.com/unity/com.unity.test-framework.performance.git
    revision: 10b82691b2d9f4e4aa8385c8d797c7e544bac548
    path: 
  unityLifecycle:
    version: 3.0.3
    nextVersion: 
    recommendedVersion: 
    isDeprecated: 0
    deprecationMessage: 
  isDeprecated: 0
  deprecationMessage: 
  git:
    hash: 
    revision: 
  upmReserved: '{"changelog":"## Fixed \n- Fixed issue where exception in OnTestEnded
    callback would result in EndTest method not finalising properly\n### Changed\n-
    Temporarily removed \"Open Script\" from Performance Benchmark Window\n- Some
    clarifications in documentation were added (\"Extension\" naming changed to \"Package\",
    Package limitations clarified)"}'
  assetStore:
    productId: 
  fingerprint: fb0dc592af8b524f1a06c50d7caf95552f1bcd2f
  editorCompatibility:
    compatibilityLevel: 0
    minimumPackageVersion: 3.0.3
    minimumUnityVersion: 2020.3.0a1
m_BuiltInPackagesHash: cd3053706fe07253cd80a38990116f4c9a4918ce
