using System;
using System.Collections.Generic;
using BallManager.StrokeData.Data;
using UnityEngine;

namespace BallManager.StrokeData.Utils
{
	public static class TrajectoryUtilities 
	{
		private const float TimeStep = 0.03f;

		public static Vector3[] CreateTrajectoryPoints(TrajectoryData trajectory, HoleDetailsData holeDetails)
		{
			var trajectoryPoints = new List<Vector3>();
			
			var timeStart = trajectory.BallTrajectory[0].TimeInterval[0];
			var timeEnd = trajectory.BallTrajectory[0].TimeInterval[1];

			var xPoints = ExpandPolynomial(trajectory.BallTrajectory[0].XFit, timeStart, timeEnd, TimeStep);
			var yPoints = ExpandPolynomial(trajectory.BallTrajectory[0].YFit, timeStart, timeEnd, TimeStep);
			var zPoints = ExpandPolynomial(trajectory.BallTrajectory[0].ZFit, timeStart, timeEnd, TimeStep);

			for (var counter = 0; counter < xPoints.Length; counter++)
			{
				var point = TrackmanToShotlink(holeDetails, xPoints[counter], yPoints[counter], zPoints[counter]);
				trajectoryPoints.Add(point);
			}

			return trajectoryPoints.ToArray();
		}

		public static Vector3 CalculateFinalRadarSpeed(Vector3[] pointArray)
		{
			var arrayLength = pointArray.Length;
			var point1 = pointArray[arrayLength - 2];
			var point2 = pointArray[arrayLength - 1];
			
			var finalRadarSpeed = (point2 - point1) / TimeStep;
			return finalRadarSpeed;
		}

		private static float[] ExpandPolynomial(IReadOnlyList<float> fitArray, float timeStart, float timeEnd, float timeInterval) 
		{
			var pointsList = new List<float>();
			
			for (var time = timeStart; time < timeEnd; time += timeInterval) 
			{
				pointsList.Add(EvaluatePolynomial(fitArray, time));
			}
			pointsList.Add(EvaluatePolynomial(fitArray, timeEnd));
			
			return pointsList.ToArray();
		}

		private static float EvaluatePolynomial(IReadOnlyList<float> fitArray, float time) 
		{
			var fitArrayLength = fitArray.Count;
			var point = fitArray[fitArrayLength - 1];

			for (var counter = 1; counter < fitArrayLength; counter++) 
			{
				point = fitArray[fitArrayLength - 1 - counter] + (point * time);
			}

			return point;
		}
		
		private static Vector3 TrackmanToShotlink(HoleDetailsData hole, double trackmanX, double trackmanY, double trackmanZ)
		{
			double z = hole.TrajectoryAlignmentPosition.z - hole.TeePosition.z;
			double x = hole.TeePosition.x - hole.TrajectoryAlignmentPosition.x;

			var alpha = Math.Atan2(x, z);
			var cosAlpha = Math.Cos(alpha);
			var sinAlpha = Math.Sin(alpha);

			trackmanZ *= -1;   // Other side of TM's x axis (line from tee to center of fairway)
			var pointZ = (trackmanX * cosAlpha) - (trackmanZ * sinAlpha) + (hole.TeePosition.z);            // Shotlink northing
			var pointX = -1 * ((trackmanX * sinAlpha) + (trackmanZ * cosAlpha)) + (hole.TeePosition.x);    // Shotlink easting
			var pointY = trackmanY + (hole.TeePosition.y);        
			
			return new Vector3((float) pointX, (float) pointY, (float) pointZ);
		}
	}
}
