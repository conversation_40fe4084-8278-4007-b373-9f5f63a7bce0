import { sendGraphQLRequest } from "@/utils/graphql";

type AddUserToLeaderboard = {
  gameId: string;
  userId: string;
  apiUrl: string;
  apiId: string;
};

export const addUserToLeaderboard = async (
  props: AddUserToLeaderboard,
): Promise<void> => {
  const { gameId, userId, apiUrl, apiId } = props;

  const input = {
    gameId,
    userId,
    score: 0,
  };

  const mutation = {
    query: `
    mutation AddScore($gameId: String!, $userId: String!, $score: Int!) {
      addScore(gameId: $gameId, userId: $userId, score: $score)
    }
  `,
    operationName: "AddScore",
    variables: input,
  };

  await sendGraphQLRequest({
    body: mutation,
    apiUrl,
    apiId,
  });
};
