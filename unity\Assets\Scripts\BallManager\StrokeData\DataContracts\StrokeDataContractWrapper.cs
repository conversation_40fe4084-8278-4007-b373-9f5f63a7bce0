using Newtonsoft.Json;

namespace BallManager.StrokeData.DataContracts
{
    [JsonObject(MemberSerialization.OptIn)]
    public class StrokeDataContractWrapper

    {
        [JsonProperty("onNextHomeStroke")] 
        public StrokeDataContract StrokeDataContract;

        public bool HasValidStroke() {
            if (StrokeDataContract == null || StrokeDataContract.IsValid() == false) {
                return false;
            }
            return true;
        }
    }
}