﻿using BallManager.StrokeData.Data;
using UnityEngine;

namespace BallManager.StrokeData
{
    public interface IStrokeData
    {
        GolferData GolferData { get; }
        int RoundNumber { get; }
        int StrokeNumber { get; }
        int StrokeId { get; }
        HoleDetailsData HoleDetails { get; }
        CoordinateData From { get; }
        CoordinateData To { get; }
        string FromLocationShort { get; }
        string ToLocationShort { get; }
        RadarData Radar { get; }
        bool IsValid { get; }
        Vector3 BallStartingPosition { get; }
    }
}