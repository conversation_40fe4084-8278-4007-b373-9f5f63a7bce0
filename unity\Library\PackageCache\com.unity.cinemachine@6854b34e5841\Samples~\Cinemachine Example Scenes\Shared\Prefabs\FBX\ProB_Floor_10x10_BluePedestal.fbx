; FBX 7.4.0 project file
; Copyright (C) 1997-2015 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7400
	CreationTimeStamp:  {
		Version: 1000
		Year: 2017
		Month: 10
		Day: 19
		Hour: 14
		Minute: 25
		Second: 54
		Millisecond: 964
	}
	Creator: "FBX SDK/FBX Plugins version 2017.1"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "exports static meshes with materials and textures"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "export mesh materials textures uvs"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp65a7bd06.tmp"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp65a7bd06.tmp"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1145914320, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 6
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 1
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "Path", "KString", "XRefUrl", "", ""
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "InterlaceMode", "enum", "", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1143820080, "Geometry::Scene", "Mesh" {
		Vertices: *774 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,100,-1000,-200,0,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,100,-1000,-300,0,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,100,-1000,-400,0,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-457.172870635986,300,-500,-457.172870635986,300,-457.172822952271,-500,300,-500,-500,300,-457.172822952271,-500,0,-500,-457.172870635986,300,-542.827177047729,-500,300,-542.827177047729,-500,0,-600,-500,100,-700,-500,100,-600,-500,0,-700,
-500,100,-800,-500,0,-800,-500,100,-900,-500,0,-900,-500,100,-1000,-500,0,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,-600,0,-300,-600,100,-400,-600,0,-400,-542.827129364014,300,-500,-542.827129364014,300,-457.172822952271,-600,0,-500,-542.827129364014,300,-542.827177047729,-600,0,-600,-600,100,-700,-600,100,-600,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,100,-1000,-600,0,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-600,100,-500,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,100,-1000,-700,0,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,100,-1000,-800,0,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,100,-1000,-900,0,-1000,-400,200,-600,-400,200,-500,-600,200,-500,-600,200,-600,-500,200,-400,-600,200,-400,-400,200,-400,-500,200,-600
		} 
		PolygonVertexIndex: *1536 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,62,11,-64,63,11,-11,1,66,-16,15,66,-68,3,68,-2,1,68,-67,21,69,-15,14,69,-71,14,70,-4,3,70,-69,15,67,-17,16,67,-72,27,72,-22,21,72,-70,16,71,-23,22,71,-74,33,74,-28,27,74,-73,22,73,-29,28,73,-76,39,76,-34,33,76,-75,28,75,-35,34,75,-78,45,78,-40,39,78,-77,34,77,-41,40,77,-80,51,80,-46,45,80,-79,40,79,-47,46,79,-82,57,82,-52,51,82,-81,46,81,-53,52,81,-84,63,84,-58,57,84,-83,52,83,-59,58,83,-86,10,86,-64,63,86,-85,58,85,-9,8,85,-88,8,87,-11,10,87,-87,66,88,-68,67,88,-90,68,90,-67,66,90,-89,69,91,-71,70,91,-93,70,92,-69,68,92,-91,67,89,-72,71,89,-94,72,94,-70,69,94,-92,71,93,-74,73,93,-96,74,96,-73,72,96,-95,73,95,-76,75,95,-98,76,98,-75,74,98,-97,75,97,-78,77,97,-100,78,100,-77,76,100,-99,77,99,-80,79,99,-102,80,102,-79,78,102,-101,79,101,-82,81,101,-104,82,104,-81,80,104,-103,81,103,-84,83,103,-106,84,106,-83,82,106,-105,83,105,-86,85,105,-108,86,108,-85,84,108,-107,85,107,-88,87,107,-110,87,109,-87,86,109,-109,88,110,-90,89,110,-112,90,112,-89,88,112,-111,91,113,-93,92,113,-115,92,114,-91,90,114,-113,89,111,-94,93,111,-116,94,116,-92,91,116,-114,93,115,-96,95,115,-118,96,118,-95,94,118,-117,95,117,-98,97,117,-120,98,120,-97,96,120,-119,97,119,-100,99,119,-122,100,122,-99,98,122,-121,99,121,-102,101,121,-124,102,124,-101,100,
124,-123,101,123,-104,103,123,-126,104,126,-103,102,126,-125,103,125,-106,105,125,-128,106,128,-105,104,128,-127,105,127,-108,107,127,-130,108,130,-107,106,130,-129,107,129,-110,109,129,-132,109,131,-109,108,131,-131,110,132,-112,111,132,-134,112,134,-111,110,134,-133,113,135,-115,114,135,-137,114,136,-113,112,136,-135,111,133,-116,115,133,-138,116,138,-114,113,138,-136,115,137,-118,117,137,-140,118,140,-117,116,140,-139,117,139,-120,119,139,-142,142,144,-144,143,144,-146,119,141,-122,121,141,-147,147,148,-143,142,148,-145,121,146,-124,123,146,-150,124,150,-123,122,150,-152,123,149,-126,125,149,-153,126,153,-125,124,153,-151,125,152,-128,127,152,-155,128,155,-127,126,155,-154,127,154,-130,129,154,-157,130,157,-129,128,157,-156,129,156,-132,131,156,-159,131,158,-131,130,158,-158,132,159,-134,133,159,-161,134,161,-133,132,161,-160,135,162,-137,136,162,-164,136,163,-135,134,163,-162,133,160,-138,137,160,-165,138,165,-136,135,165,-163,137,164,-140,139,164,-167,140,167,-139,138,167,-166,139,166,-142,141,166,-169,144,169,-146,145,169,-171,141,168,-147,146,168,-172,148,172,-145,144,172,-170,146,171,-150,149,171,-174,150,174,-152,151,174,-176,149,173,-153,152,173,-177,153,177,-151,150,177,-175,152,176,-155,154,176,-179,155,179,-154,153,179,-178,154,178,-157,156,178,-181,157,181,-156,155,181,-180,156,180,-159,158,180,-183,158,182,-158,157,182,-182,159,183,-161,160,183,-185,161,185,-160,159,185,-184,162,186,-164,163,186,-188,163,187,-162,161,187,-186,160,184,-165,164,184,-189,165,189,-163,162,189,-187,164,188,-167,166,188,-191,167,191,-166,165,191,-190,166,190,-169,168,190,-193,193,194,-168,167,194,-192,168,192,-172,171,192,-196,175,196,-194,193,196,-195,171,195,-174,173,195,-198,174,198,-176,175,198,-197,173,197,-177,176,197,-200,177,200,-175,174,200,-199,176,199,-179,178,199,-202,179,202,-178,177,202,-201,178,201,-181,180,201,-204,181,204,-180,179,204,-203,180,203,-183,182,203,-206,182,205,-182,181,205,-205,183,206,-185,184,206,-208,185,208,-184,183,208,-207,186,209,-188,187,209,-211,187,210,-186,185,210,-209,184,207,-189,
188,207,-212,189,212,-187,186,212,-210,188,211,-191,190,211,-214,191,214,-190,189,214,-213,190,213,-193,192,213,-216,194,216,-192,191,216,-215,192,215,-196,195,215,-218,196,218,-195,194,218,-217,195,217,-198,197,217,-220,198,220,-197,196,220,-219,197,219,-200,199,219,-222,200,222,-199,198,222,-221,199,221,-202,201,221,-224,202,224,-201,200,224,-223,201,223,-204,203,223,-226,204,226,-203,202,226,-225,203,225,-206,205,225,-228,205,227,-205,204,227,-227,206,228,-208,207,228,-230,208,230,-207,206,230,-229,209,231,-211,210,231,-233,210,232,-209,208,232,-231,207,229,-212,211,229,-234,212,234,-210,209,234,-232,211,233,-214,213,233,-236,214,236,-213,212,236,-235,213,235,-216,215,235,-238,216,238,-215,214,238,-237,215,237,-218,217,237,-240,218,240,-217,216,240,-239,217,239,-220,219,239,-242,220,242,-219,218,242,-241,219,241,-222,221,241,-244,222,244,-221,220,244,-243,221,243,-224,223,243,-246,224,246,-223,222,246,-245,223,245,-226,225,245,-248,226,248,-225,224,248,-247,225,247,-228,227,247,-250,227,249,-227,226,249,-249,228,4,-230,229,4,-6,230,6,-229,228,6,-5,231,18,-233,232,18,-8,232,7,-231,230,7,-7,229,5,-234,233,5,-20,234,24,-232,231,24,-19,233,19,-236,235,19,-26,236,30,-235,234,30,-25,235,25,-238,237,25,-32,238,36,-237,236,36,-31,237,31,-240,239,31,-38,240,42,-239,238,42,-37,239,37,-242,241,37,-44,242,48,-241,240,48,-43,241,43,-244,243,43,-50,244,54,-243,242,54,-49,243,49,-246,245,49,-56,246,60,-245,244,60,-55,245,55,-248,247,55,-62,248,64,-247,246,64,-61,247,61,-250,249,61,-66,249,65,-249,248,65,-65,122,250,-121,120,250,-252,193,252,-176,175,252,-254,140,254,-168,167,254,-256,167,255,-194,193,255,-253,120,251,-119,118,251,-257,175,253,-152,151,253,-258,118,256,-141,140,256,-255,151,257,-123,122,257,-251,250,147,-252,251,147,-143,252,169,-254,253,169,-173,254,145,-256,255,145,-171,255,170,-253,252,170,-170,251,142,-257,256,142,-144,253,172,-258,257,172,-149,256,143,-255,254,143,-146,257,148,-251,250,148,-148
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4608 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,0.86813086271286,0.496335327625275,0,0.868130922317505,0.496335387229919,0,0.868130922317505,0.496335387229919,0,0.868130922317505,0.496335387229919,0,0.868130922317505,0.496335387229919,0,0.86813086271286,0.496335327625275,0,-0.86813086271286,0.496335327625275,0,-0.868130922317505,0.496335387229919,0,-0.868130922317505,0.496335387229919,0,-0.868130922317505,0.496335387229919,0,-0.868130922317505,0.496335387229919,0,-0.86813086271286,0.496335297822952,0,-0,0.496335029602051,0.868131041526794,-0,0.496334999799728,0.86813098192215,-0,0.496334999799728,0.86813098192215,-0,0.496334999799728,0.86813098192215,-0,0.496334999799728,0.86813098192215,-0,0.496334969997406,0.86813098192215,-0.86813086271286,0.496335327625275,0,-0.868130922317505,0.496335387229919,0,-0.86813086271286,0.496335327625275,0,-0.86813086271286,0.496335327625275,0,-0.868130922317505,0.496335387229919,0,-0.868130922317505,0.496335387229919,0,0.868130922317505,0.496335387229919,0,0.86813086271286,0.496335327625275,0,0.868130922317505,0.496335387229919,0,0.868130922317505,0.496335387229919,0,0.86813086271286,0.496335327625275,0,0.86813086271286,0.496335297822952,0,-0,0.496335029602051,-0.868131041526794,-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,
-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496335029602051,0.868131041526794,-0,0.496334999799728,0.86813098192215,-0,0.496335029602051,0.868131041526794,-0,0.496335029602051,0.868131041526794,-0,0.496334999799728,0.86813098192215,-0,0.496334999799728,0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496334999799728,-0.86813098192215,-0,0.496334969997406,-0.86813098192215
			} 
			NormalsW: *1536 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *4608 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,
0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0.496335327625275,0.86813086271286,0,-0.496335387229919,0.868130922317505,0,-0.496335387229919,0.868130922317505,0,-0.496335387229919,0.868130922317505,0,-0.496335387229919,0.868130922317505,0,-0.496335327625275,0.86813086271286,0,0.496335327625275,0.86813086271286,-0,0.496335387229919,0.868130922317505,-0,0.496335387229919,0.868130922317505,-0,0.496335387229919,0.868130922317505,-0,0.496335387229919,0.868130922317505,-0,0.496335297822952,0.86813086271286,-0,0,0.868131041526794,-0.496335029602051,0,0.86813098192215,-0.496334999799728,0,0.86813098192215,-0.496334999799728,0,0.86813098192215,-0.496334999799728,0,0.86813098192215,-0.496334999799728,0,0.86813098192215,-0.496334969997406,0.496335327625275,0.86813086271286,-0,0.496335387229919,0.868130922317505,-0,0.496335327625275,0.86813086271286,-0,0.496335327625275,0.86813086271286,-0,0.496335387229919,0.868130922317505,-0,0.496335387229919,0.868130922317505,-0,-0.496335387229919,0.868130922317505,0,-0.496335327625275,0.86813086271286,0,-0.496335387229919,0.868130922317505,0,-0.496335387229919,0.868130922317505,0,-0.496335327625275,0.86813086271286,0,-0.496335297822952,0.86813086271286,0,0,0.868131041526794,0.496335029602051,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.868131041526794,-0.496335029602051,0,0.86813098192215,-0.496334999799728,0,0.868131041526794,-0.496335029602051,0,0.868131041526794,-0.496335029602051,0,0.86813098192215,-0.496334999799728,0,0.86813098192215,-0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334999799728,0,0.86813098192215,0.496334969997406
			} 
			BinormalsW: *1536 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *4608 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *1536 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *1544 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *1536 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,186,188,187,187,188,189,159,185,161,161,185,190,191,192,186,186,192,188,161,190,163,163,190,193,164,194,162,162,194,195,163,193,165,165,193,196,166,197,164,164,197,194,165,196,167,167,196,198,168,199,166,166,199,197,167,198,169,169,198,200,170,201,168,168,201,199,169,200,171,171,200,202,172,203,173,173,203,204,174,205,175,175,205,206,176,207,177,177,207,208,178,209,179,179,209,210,179,210,180,180,210,211,175,206,181,181,206,212,182,213,178,178,213,209,181,212,183,183,212,214,184,215,182,182,215,213,183,214,185,185,214,216,188,217,189,189,217,218,185,216,190,190,216,219,192,220,188,188,220,217,190,219,193,193,219,221,194,222,195,195,222,223,193,221,196,196,221,224,197,225,194,194,225,222,196,224,198,198,224,226,199,227,197,197,227,225,198,226,200,200,226,228,201,229,199,199,229,227,200,228,202,202,228,230,203,231,204,204,231,232,205,233,206,206,233,234,207,235,208,208,235,236,209,237,210,210,237,238,210,238,211,211,238,239,206,234,212,212,234,240,213,241,209,209,241,237,212,240,214,214,240,242,215,243,213,213,243,241,214,242,216,216,242,244,245,246,215,215,246,243,216,244,219,219,244,247,223,248,245,245,248,246,219,247,221,221,247,249,222,250,223,223,250,248,221,249,224,224,249,251,225,252,222,222,252,250,224,251,226,226,251,253,227,254,225,225,254,252,226,253,228,228,253,255,229,256,227,227,256,254,228,255,230,230,255,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,246,272,243,243,272,270,244,271,247,247,
271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,248,248,276,274,249,275,251,251,275,277,252,278,250,250,278,276,251,277,253,253,277,279,254,280,252,252,280,278,253,279,255,255,279,281,256,282,254,254,282,280,255,281,257,257,281,283,258,284,259,259,284,285,260,286,261,261,286,287,262,288,263,263,288,289,264,290,265,265,290,291,265,291,266,266,291,292,261,287,267,267,287,293,268,294,264,264,294,290,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,274,274,302,300,275,301,277,277,301,303,278,304,276,276,304,302,277,303,279,279,303,305,280,306,278,278,306,304,279,305,281,281,305,307,282,308,280,280,308,306,281,307,283,283,307,309,284,310,285,285,310,311,286,312,287,287,312,313,288,314,289,289,314,315,290,316,291,291,316,317,291,317,292,292,317,318,287,313,293,293,313,319,294,320,290,290,320,316,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,300,300,328,326,301,327,303,303,327,329,304,330,302,302,330,328,303,329,305,305,329,331,306,332,304,304,332,330,305,331,307,307,331,333,308,334,306,306,334,332,307,333,309,309,333,335,310,336,311,311,336,337,338,340,339,339,340,341,342,344,343,343,344,345,346,348,347,347,348,349,350,351,342,342,351,344,339,341,352,352,341,353,354,356,355,355,356,357,358,359,346,346,359,348,355,357,360,360,357,361,362,364,363,363,364,365,366,368,367,367,368,369,370,372,371,371,372,373,374,375,366,366,375,368,363,365,376,376,365,377,378,380,379,379,380,381,382,383,370,370,383,372,379,381,384,384,381,385
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *772 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,4.57172870635986,-5,4.57172870635986,-4.57172822952271,5,-5,5,-4.57172822952271,-5,-5,4.57172870635986,-5.42827177047729,5,-5.42827177047729,-5,-6,5,-7,5,-6,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,5.42827129364014,-5,5.42827129364014,-4.57172822952271,-6,-5,5.42827129364014,-5.42827177047729,-6,-6,6,-7,6,-6,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,6,-5,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,10,-2,10,-1,10,0,-10,-2,10,-3,-10,-3,10,-4,-10,-4,10,-5,-10,-5,10,-6,-10,-6,10,-7,-10,-7,10,-8,-10,-8,10,-9,-10,-9,10,-10,-10,-10,10,0,10,1,6,1,5,1,6,2,5,2,-5,1,-6,1,-5,2,-6,2,-5,1,-6,1,-5,2,-6,2,-4,1,-4,
2,4,1,4,2,6,1,5,1,6,2,5,2,-4,1,-4,2,4,1,4,2,6,3.72160339355469,5,3.72160339355469,5.42827177047729,4.8735032081604,5,4.8735032081604,-5,-1.24175024032593,-6,-1.24175024032593,-5,-0.0898502022027969,-5.42827177047729,-0.0898502022027969,-5,3.72160243988037,-6,3.72160243988037,-5,4.87350225448608,-5.42827129364014,4.87350225448608,-4,-1.24175024032593,-4.57172822952271,-0.0898502022027969,4,3.72160339355469,4.57172822952271,4.8735032081604,6,-1.24174809455872,5,-1.24174809455872,5.42827129364014,-0.0898482874035835,5,-0.0898482874035835,-4,3.72160243988037,-4.57172870635986,4.87350225448608,4,-1.24174809455872,4.57172870635986,-0.0898482874035835
			} 
			UVIndex: *1536 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,186,188,187,187,188,189,159,185,161,161,185,190,191,192,186,186,192,188,161,190,163,163,190,193,164,194,162,162,194,195,163,193,165,165,193,196,166,197,164,164,197,194,165,196,167,167,196,198,168,199,166,166,199,197,167,198,169,169,198,200,170,201,168,168,201,199,169,200,171,171,200,202,172,203,173,173,203,204,174,205,175,175,205,206,176,207,177,177,207,208,178,209,179,179,209,210,179,210,180,180,210,211,175,206,181,181,206,212,182,213,178,178,213,209,181,212,183,183,212,214,184,215,182,182,215,213,183,214,185,185,214,216,188,217,189,189,217,218,185,216,190,190,216,219,192,220,188,188,220,217,190,219,193,193,219,221,194,222,195,195,222,223,193,221,196,196,221,224,197,225,194,194,225,222,196,224,198,198,224,226,199,227,197,197,227,225,198,226,200,200,226,228,201,229,199,199,229,227,200,228,202,202,228,230,203,231,204,204,231,232,205,233,206,206,233,234,207,235,208,208,235,236,209,237,210,210,237,238,210,238,211,211,238,239,206,234,212,212,234,240,213,241,209,209,241,237,212,240,214,214,240,242,215,243,213,213,243,241,214,242,216,216,242,244,245,246,215,215,246,243,216,244,219,219,244,247,223,248,245,245,248,246,219,247,221,221,247,249,222,250,223,223,250,248,221,249,224,224,249,251,225,252,222,222,252,250,224,251,226,226,251,253,227,254,225,225,254,252,226,253,228,228,253,255,229,256,227,227,256,254,228,255,230,230,255,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,246,272,243,243,272,270,244,271,247,247,
271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,248,248,276,274,249,275,251,251,275,277,252,278,250,250,278,276,251,277,253,253,277,279,254,280,252,252,280,278,253,279,255,255,279,281,256,282,254,254,282,280,255,281,257,257,281,283,258,284,259,259,284,285,260,286,261,261,286,287,262,288,263,263,288,289,264,290,265,265,290,291,265,291,266,266,291,292,261,287,267,267,287,293,268,294,264,264,294,290,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,274,274,302,300,275,301,277,277,301,303,278,304,276,276,304,302,277,303,279,279,303,305,280,306,278,278,306,304,279,305,281,281,305,307,282,308,280,280,308,306,281,307,283,283,307,309,284,310,285,285,310,311,286,312,287,287,312,313,288,314,289,289,314,315,290,316,291,291,316,317,291,317,292,292,317,318,287,313,293,293,313,319,294,320,290,290,320,316,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,300,300,328,326,301,327,303,303,327,329,304,330,302,302,330,328,303,329,305,305,329,331,306,332,304,304,332,330,305,331,307,307,331,333,308,334,306,306,334,332,307,333,309,309,333,335,310,336,311,311,336,337,338,340,339,339,340,341,342,344,343,343,344,345,346,348,347,347,348,349,350,351,342,342,351,344,339,341,352,352,341,353,354,356,355,355,356,357,358,359,346,346,359,348,355,357,360,360,357,361,362,364,363,363,364,365,366,368,367,367,368,369,370,372,371,371,372,373,374,375,366,366,375,368,363,365,376,376,365,377,378,380,379,379,380,381,382,383,370,370,383,372,379,381,384,384,381,385
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *772 {
				a: 0.00400000018998981,0.657420337200165,0.0530322641134262,0.657420337200165,0.00400000065565109,0.706452548503876,0.0530322641134262,0.706452548503876,0.00400000484660268,0.498323649168015,0.0530322529375553,0.498323649168015,0.00400000018998981,0.547355771064758,0.0530322454869747,0.547355771064758,0.445289462804794,0.604388117790222,0.494321703910828,0.604388117790222,0.445289462804794,0.653420329093933,0.494321703910828,0.653420329093933,0.445289462804794,0.551355838775635,0.494321703910828,0.551355838775635,0.445289462804794,0.600387990474701,0.494321703910828,0.600387990474701,0.988644897937775,0.00400011613965034,0.988644957542419,0.0530323833227158,0.939612746238708,0.0040001873858273,0.939612746238708,0.0530323870480061,0.00400011800229549,0.053032923489809,0.0530323684215546,0.0530328378081322,0.00400000018998981,0.00400071917101741,0.0530322901904583,0.00400064280256629,0.0530324541032314,0.102065026760101,0.00400024047121406,0.102065101265907,0.102064415812492,0.547355830669403,0.102064423263073,0.498323649168015,0.396257311105728,0.551355838775635,0.396257311105728,0.600387990474701,0.890580534934998,0.00400022324174643,0.890580594539642,0.0530324019491673,0.0530325323343277,0.151097163558006,0.00400035781785846,0.151097223162651,0.151096597313881,0.547355830669403,0.151096597313881,0.498323649168015,0.34722512960434,0.551355838775635,0.34722512960434,0.600387990474701,0.841548442840576,0.00400024140253663,0.841548442840576,0.0530324131250381,0.0530325807631016,0.200129270553589,0.00400044722482562,0.200129300355911,0.200128778815269,0.547355830669403,0.200128778815269,0.498323649168015,0.298192918300629,0.551355838775635,0.298192948102951,0.600387990474701,0.79251629114151,0.00400021811947227,0.79251629114151,0.0530324131250381,0.0530325919389725,0.249161347746849,0.00400049285963178,0.249161332845688,0.249160900712013,0.547355830669403,0.249160900712013,0.498323619365692,0.249160826206207,0.551355838775635,0.249160826206207,0.600388050079346,0.743484139442444,0.00400019064545631,
0.743484139442444,0.0530324019491673,0.0530325621366501,0.298193365335464,0.00400048727169633,0.29819330573082,0.298193037509918,0.547355830669403,0.298193007707596,0.498323619365692,0.200128689408302,0.551355838775635,0.200128689408302,0.600388050079346,0.694451987743378,0.00400017015635967,0.694451987743378,0.0530323721468449,0.0530324876308441,0.347225397825241,0.00400043511763215,0.347225308418274,0.347225159406662,0.547355830669403,0.347225159406662,0.498323619365692,0.151096537709236,0.551355838775635,0.151096552610397,0.600388050079346,0.645419836044312,0.00400011334568262,0.645419776439667,0.0530323535203934,0.0530323833227158,0.396257370710373,0.00400035176426172,0.396257251501083,0.396257311105728,0.547355830669403,0.396257311105728,0.49832358956337,0.102064400911331,0.551355838775635,0.102064408361912,0.600388050079346,0.596387624740601,0.00400006305426359,0.596387624740601,0.0530323274433613,0.0530322305858135,0.445289433002472,0.00400020787492394,0.445289254188538,0.445289552211761,0.547355830669403,0.445289552211761,0.49832358956337,0.0530321560800076,0.551355838775635,0.0530321672558784,0.600388050079346,0.54735541343689,0.00400002626702189,0.547355353832245,0.0530322939157486,0.053032074123621,0.494321435689926,0.00400000018998981,0.494321227073669,0.49432173371315,0.547355771064758,0.494321703910828,0.498323559761047,0.00400000018998981,0.551355838775635,0.00400001183152199,0.60038810968399,0.498323231935501,0.00400000018998981,0.498323231935501,0.053032286465168,0.102064438164234,0.00400053383782506,0.102064527571201,0.0530327148735523,0.102064453065395,0.706452548503876,0.102064445614815,0.657420337200165,0.890580594539642,0.102064549922943,0.939612746238708,0.102064542472363,0.988644957542419,0.102064535021782,0.102064594626427,0.102064929902554,0.841548442840576,0.102064549922943,0.102064654231071,0.1510970890522,0.79251629114151,0.102064549922943,0.102064676582813,0.200129240751266,0.743484139442444,0.102064542472363,0.102064654231071,0.249161347746849,0.694451928138733,0.102064527571201,0.102064594626427,
0.298193454742432,0.645419716835022,0.102064520120621,0.10206451267004,0.347225487232208,0.596387565135956,0.102064490318298,0.102064400911331,0.396257519721985,0.547355353832245,0.102064460515976,0.10206426680088,0.445289552211761,0.498323202133179,0.102064453065395,0.102064102888107,0.494321554899216,0.396257251501083,0.604388117790222,0.396257251501083,0.653420329093933,0.15109658241272,0.00400040997192264,0.151096656918526,0.0530326403677464,0.151096552610397,0.706452548503876,0.151096552610397,0.657420337200165,0.890580594539642,0.151096642017365,0.939612746238708,0.151096642017365,0.988644897937775,0.151096642017365,0.151096701622009,0.102064870297909,0.841548383235931,0.151096656918526,0.151096731424332,0.151097059249878,0.792516231536865,0.151096671819687,0.151096746325493,0.200129240751266,0.743484079837799,0.151096686720848,0.151096701622009,0.249161377549171,0.694451868534088,0.151096671819687,0.151096627116203,0.298193484544754,0.645419716835022,0.151096642017365,0.151096522808075,0.347225576639175,0.596387505531311,0.151096627116203,0.151096388697624,0.396257638931274,0.5473552942276,0.151096612215042,0.151096254587173,0.445289701223373,0.498323172330856,0.151096597313881,0.151096075773239,0.494321703910828,0.347225159406662,0.604388117790222,0.347225159406662,0.653420329093933,0.200128778815269,0.00400029914453626,0.200128853321075,0.0530325695872307,0.200128749012947,0.706452548503876,0.200128749012947,0.657420337200165,0.890580594539642,0.200128778815269,0.939612746238708,0.200128749012947,0.988644897937775,0.200128719210625,0.200128883123398,0.102064825594425,0.841548442840576,0.200128793716431,0.200128898024559,0.151097044348717,0.792516231536865,0.200128823518753,0.200128883123398,0.200129255652428,0.743484079837799,0.200128853321075,0.200128823518753,0.249161422252655,0.694451868534088,0.200128853321075,0.200128734111786,0.298193573951721,0.645419657230377,0.200128823518753,0.200128614902496,0.347225695848465,0.596387505531311,0.200128823518753,0.200128465890884,0.396257787942886,0.5473552942276,
0.200128793716431,0.200128301978111,0.445289880037308,0.498323142528534,0.200128763914108,0.200128093361855,0.494321882724762,0.298192948102951,0.604388117790222,0.298192948102951,0.653420329093933,0.249161019921303,0.00400020042434335,0.249161064624786,0.0530325099825859,0.249160870909691,0.706452548503876,0.249160870909691,0.657420337200165,0.890580594539642,0.249160870909691,0.939612746238708,0.249160826206207,0.988644897937775,0.249160781502724,0.249161094427109,0.102064795792103,0.841548442840576,0.249160900712013,0.249161094427109,0.151097059249878,0.792516231536865,0.249160945415497,0.249161049723625,0.200129270553589,0.0249991193413734,0.914581418037415,0.0459982380270958,0.914581418037415,0.0249991193413734,0.935580492019653,0.0459982380270958,0.935580492019653,0.24916099011898,0.2491614818573,0.00400000112131238,0.914581418037415,0.00400000065565109,0.935580492019653,0.249160885810852,0.298193663358688,0.645419657230377,0.249161019921303,0.694451808929443,0.249161005020142,0.24916073679924,0.347225815057755,0.596387445926666,0.24916099011898,0.249160587787628,0.396257936954498,0.547355234622955,0.249160960316658,0.24916036427021,0.445290058851242,0.498323082923889,0.249160930514336,0.249160125851631,0.494322091341019,0.249160841107368,0.604388117790222,0.249160841107368,0.653420329093933,0.298193275928497,0.0040001142770052,0.29819330573082,0.0530324690043926,0.298192977905273,0.706452548503876,0.298192977905273,0.657420337200165,0.890580654144287,0.298193007707596,0.939612805843353,0.298192948102951,0.988644957542419,0.298192828893661,0.298193335533142,0.102064773440361,0.841548442840576,0.298193067312241,0.29819330573082,0.151097059249878,0.792516231536865,0.298193097114563,0.298193246126175,0.200129315257072,0.0249991193413734,0.956579566001892,0.0459982380270958,0.956579566001892,0.298193156719208,0.249161556363106,0.00400000018998981,0.956579566001892,0.298193037509918,0.298193752765656,0.645419597625732,0.29819318652153,0.694451808929443,0.298193156719208,0.298192888498306,0.347225964069366,0.596387386322021,
0.29819318652153,0.298192709684372,0.396258115768433,0.547355175018311,0.298193156719208,0.298192471265793,0.445290267467499,0.498322993516922,0.298193126916885,0.298192203044891,0.49432235956192,0.200128719210625,0.604388117790222,0.200128719210625,0.653420329093933,0.347225606441498,0.00400004349648952,0.34722563624382,0.0530324429273605,0.34722512960434,0.706452548503876,0.34722512960434,0.657420337200165,0.890580713748932,0.347225159406662,0.939612865447998,0.347225069999695,0.988645017147064,0.347224950790405,0.347225606441498,0.102064780890942,0.841548502445221,0.347225219011307,0.347225576639175,0.1510970890522,0.79251629114151,0.347225278615952,0.347225487232208,0.200129374861717,0.743484020233154,0.298193126916885,0.743484079837799,0.347225308418274,0.347225368022919,0.249161645770073,0.694451868534088,0.347225338220596,0.347225248813629,0.298193901777267,0.645419597625732,0.347225397825241,0.347225069999695,0.347226113080978,0.596387386322021,0.347225397825241,0.347224861383438,0.396258324384689,0.547355115413666,0.347225397825241,0.347224622964859,0.445290505886078,0.498322933912277,0.347225338220596,0.347224324941635,0.494322657585144,0.15109658241272,0.604388117790222,0.15109658241272,0.653420329093933,0.396257996559143,0.00400000158697367,0.396257966756821,0.0530324354767799,0.396257340908051,0.706452548503876,0.396257340908051,0.657420337200165,0.890580773353577,0.396257340908051,0.939612984657288,0.396257251501083,0.988645136356354,0.396257132291794,0.396257936954498,0.102064795792103,0.841548502445221,0.396257400512695,0.396257877349854,0.151097148656845,0.79251629114151,0.39625746011734,0.396257758140564,0.200129464268684,0.743484079837799,0.396257519721985,0.396257638931274,0.249161764979362,0.694451868534088,0.39625757932663,0.39625746011734,0.298194020986557,0.645419657230377,0.396257609128952,0.396257281303406,0.347226291894913,0.596387386322021,0.396257638931274,0.396257072687149,0.396258533000946,0.547355115413666,0.396257638931274,0.396256804466248,0.445290774106979,0.498322874307632,0.396257638931274,
0.396256506443024,0.494322955608368,0.102064363658428,0.604388117790222,0.102064363658428,0.653420329093933,0.445290416479111,0.00400000018998981,0.445290356874466,0.0530324503779411,0.445289582014084,0.706452548503876,0.445289582014084,0.657420337200165,0.890580832958221,0.445289552211761,0.939613044261932,0.445289462804794,0.988645255565643,0.445289373397827,0.445290297269821,0.102064855396748,0.841548562049866,0.445289611816406,0.445290178060532,0.151097223162651,0.792516350746155,0.445289671421051,0.445290058851242,0.200129568576813,0.743484079837799,0.445289731025696,0.44528990983963,0.249161899089813,0.694451868534088,0.445289790630341,0.445289731025696,0.298194199800491,0.645419657230377,0.445289820432663,0.445289522409439,0.347226500511169,0.596387386322021,0.445289880037308,0.44528928399086,0.396258771419525,0.547355115413666,0.44528990983963,0.445289045572281,0.445291042327881,0.498322814702988,0.445289939641953,0.445288747549057,0.494323253631592,0.0530321411788464,0.604388117790222,0.0530321337282658,0.653420329093933,0.494322806596756,0.00400005141273141,0.494322717189789,0.0530325174331665,0.494321703910828,0.706452548503876,0.494321703910828,0.657420337200165,0.890580832958221,0.49432173371315,0.939613103866577,0.49432161450386,0.988645374774933,0.494321525096893,0.494322627782822,0.102064952254295,0.84154862165451,0.494321823120117,0.494322508573532,0.151097312569618,0.792516350746155,0.494321882724762,0.494322389364243,0.200129687786102,0.743484139442444,0.494321912527084,0.494322210550308,0.249162048101425,0.694451928138733,0.494321972131729,0.494322001934052,0.298194378614426,0.645419716835022,0.494322031736374,0.494321793317795,0.347226679325104,0.596387445926666,0.494322091341019,0.494321525096893,0.396258980035782,0.547355175018311,0.494322180747986,0.494321227073669,0.445291310548782,0.49832284450531,0.494322270154953,0.494320929050446,0.494323551654816,0.00400000810623169,0.604388058185577,0.00400000018998981,0.653420329093933,0.113512396812439,0.710452556610107,0.11351240426302,0.759484827518463,
0.162544548511505,0.710452556610107,0.162544548511505,0.759484767913818,0.00400000903755426,0.861549258232117,0.00400000018998981,0.910581350326538,0.0530321523547173,0.861549198627472,0.0530321225523949,0.910581409931183,0.109512336552143,0.759484887123108,0.10951229929924,0.808516979217529,0.0604801960289478,0.759484767913818,0.0604801811277866,0.808516979217529,0.00400000018998981,0.812516987323761,0.053032148629427,0.812516987323761,0.113512396812439,0.808516919612885,0.162544518709183,0.808516979217529,0.113512396812439,0.910581409931183,0.11351240426302,0.861549139022827,0.162544548511505,0.910581409931183,0.162544548511505,0.861549198627472,0.109512396156788,0.710452616214752,0.060480248183012,0.710452556610107,0.113512396812439,0.812517046928406,0.162544518709183,0.812516987323761,0.162544548511505,0.710452556610107,0.162544548511505,0.759484767913818,0.219024762511253,0.73848569393158,0.219024747610092,0.759484827518463,0.0530321523547173,0.861549198627472,0.0530321225523949,0.910581409931183,0.109512351453304,0.861549198627472,0.109512336552143,0.882548332214355,0.0604801960289478,0.759484767913818,0.0604801811277866,0.808516979217529,0.00400001090019941,0.759484767913818,0.00400000018998981,0.780483841896057,0.053032148629427,0.812516987323761,0.109512366354465,0.840550124645233,0.162544518709183,0.808516979217529,0.219024732708931,0.780483901500702,0.162544548511505,0.910581409931183,0.162544548511505,0.861549198627472,0.219024747610092,0.882548272609711,0.219024732708931,0.861549198627472,0.060480248183012,0.710452556610107,0.00400001183152199,0.738485634326935,0.162544518709183,0.812516987323761,0.219024732708931,0.840550065040588
			} 
			UVIndex: *1536 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,186,188,187,187,188,189,159,185,161,161,185,190,191,192,186,186,192,188,161,190,163,163,190,193,164,194,162,162,194,195,163,193,165,165,193,196,166,197,164,164,197,194,165,196,167,167,196,198,168,199,166,166,199,197,167,198,169,169,198,200,170,201,168,168,201,199,169,200,171,171,200,202,172,203,173,173,203,204,174,205,175,175,205,206,176,207,177,177,207,208,178,209,179,179,209,210,179,210,180,180,210,211,175,206,181,181,206,212,182,213,178,178,213,209,181,212,183,183,212,214,184,215,182,182,215,213,183,214,185,185,214,216,188,217,189,189,217,218,185,216,190,190,216,219,192,220,188,188,220,217,190,219,193,193,219,221,194,222,195,195,222,223,193,221,196,196,221,224,197,225,194,194,225,222,196,224,198,198,224,226,199,227,197,197,227,225,198,226,200,200,226,228,201,229,199,199,229,227,200,228,202,202,228,230,203,231,204,204,231,232,205,233,206,206,233,234,207,235,208,208,235,236,209,237,210,210,237,238,210,238,211,211,238,239,206,234,212,212,234,240,213,241,209,209,241,237,212,240,214,214,240,242,215,243,213,213,243,241,214,242,216,216,242,244,245,246,215,215,246,243,216,244,219,219,244,247,223,248,245,245,248,246,219,247,221,221,247,249,222,250,223,223,250,248,221,249,224,224,249,251,225,252,222,222,252,250,224,251,226,226,251,253,227,254,225,225,254,252,226,253,228,228,253,255,229,256,227,227,256,254,228,255,230,230,255,257,231,258,232,232,258,259,233,260,234,234,260,261,235,262,236,236,262,263,237,264,238,238,264,265,238,265,239,239,265,266,234,261,240,240,261,267,241,268,237,237,268,264,240,267,242,242,267,269,243,270,241,241,270,268,242,269,244,244,269,271,246,272,243,243,272,270,244,271,247,247,
271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,248,248,276,274,249,275,251,251,275,277,252,278,250,250,278,276,251,277,253,253,277,279,254,280,252,252,280,278,253,279,255,255,279,281,256,282,254,254,282,280,255,281,257,257,281,283,258,284,259,259,284,285,260,286,261,261,286,287,262,288,263,263,288,289,264,290,265,265,290,291,265,291,266,266,291,292,261,287,267,267,287,293,268,294,264,264,294,290,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,274,274,302,300,275,301,277,277,301,303,278,304,276,276,304,302,277,303,279,279,303,305,280,306,278,278,306,304,279,305,281,281,305,307,282,308,280,280,308,306,281,307,283,283,307,309,284,310,285,285,310,311,286,312,287,287,312,313,288,314,289,289,314,315,290,316,291,291,316,317,291,317,292,292,317,318,287,313,293,293,313,319,294,320,290,290,320,316,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,300,300,328,326,301,327,303,303,327,329,304,330,302,302,330,328,303,329,305,305,329,331,306,332,304,304,332,330,305,331,307,307,331,333,308,334,306,306,334,332,307,333,309,309,333,335,310,336,311,311,336,337,338,340,339,339,340,341,342,344,343,343,344,345,346,348,347,347,348,349,350,351,342,342,351,344,339,341,352,352,341,353,354,356,355,355,356,357,358,359,346,346,359,348,355,357,360,360,357,361,362,364,363,363,364,365,366,368,367,367,368,369,370,372,371,371,372,373,374,375,366,366,375,368,363,365,376,376,365,377,378,380,379,379,380,381,382,383,370,370,383,372,379,381,384,384,381,385
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Model: 1145317840, "Model::ProB_Floor_10x10", "Mesh" {
		Version: 232
		Properties70:  {
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 1145451712, "Material::Default_Prototype_Blue", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 1145452512, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_blue.png"
		}
		UseMipMap: 0
		Filename: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_blue.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_blue.png"
	}
	Texture: 294702400, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_blue.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_blue.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::ProB_Floor_10x10, Model::RootNode
	C: "OO",1145317840,0
	
	;Material::Default_Prototype_Blue, Model::ProB_Floor_10x10
	C: "OO",1145451712,1145317840
	
	;Geometry::Scene, Model::ProB_Floor_10x10
	C: "OO",1143820080,1145317840
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype_Blue
	C: "OO",294702400,1145451712
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype_Blue
	C: "OP",294702400,1145451712, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",1145452512,294702400
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
