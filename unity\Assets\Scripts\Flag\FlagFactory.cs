using UnityEngine;

namespace Flag
{
	public class FlagFactory
	{
		private readonly GameObject prefab;
		private readonly Transform container;
		
		public FlagFactory(GameObject flagPrefab, Transform flagContainer)
		{
			prefab = flagPrefab;
			container = flagContainer;
		}

		public GameObject GenerateFlag(Vector3 flagPosition)
		{
			return Object.Instantiate(prefab, flagPosition, Quaternion.identity,container);
		}
	}
}
