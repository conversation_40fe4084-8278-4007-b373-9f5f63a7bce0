import {
  DynamoDBClient,
  TransactWriteItemsCommand,
} from "@aws-sdk/client-dynamodb";
import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";

const dynamoDbClient = new DynamoDBClient();

type HouseholdProps = {
  tableName: string;
  maxHouseholdSize: number;
  deviceId: string;
  signInAlias: string;
};

export const incrementHouseholdSize = async (
  props: HouseholdProps,
): Promise<void> => {
  try {
    const { tableName, maxHouseholdSize, deviceId, signInAlias } = props;
    await dynamoDbClient.send(
      new TransactWriteItemsCommand({
        TransactItems: [
          {
            Update: {
              TableName: tableName,
              Key: {
                pk: { S: deviceId },
                signInAlias: { S: "META#COUNTER" },
              },
              UpdateExpression:
                "SET userCount = if_not_exists(userCount, :zero) + :inc",
              ConditionExpression:
                "attribute_not_exists(userCount) OR userCount < :max",
              ExpressionAttributeValues: {
                ":inc": { N: "1" },
                ":max": { N: maxHouseholdSize.toString() },
                ":zero": { N: "0" },
              },
              ReturnValuesOnConditionCheckFailure: "NONE",
            },
          },
          {
            Put: {
              TableName: tableName,
              Item: {
                pk: { S: deviceId },
                signInAlias: { S: signInAlias },
              },
              ConditionExpression: "attribute_not_exists(signInAlias)",
              ReturnValuesOnConditionCheckFailure: "NONE",
            },
          },
        ],
      }),
    );

    logger.info("Household updated", { deviceId, signInAlias });
  } catch (error: any) {
    const message = error?.message || "";
    if (
      message.includes("ConditionalCheckFailed") &&
      message.includes("userCount")
    ) {
      logger.error("Household capacity of has been reached", { error });
      throw new Error("Household capacity has been reached");
    }

    logger.error("Update item failed", { error });
    throw new Error("Update item failed");
  }
};
