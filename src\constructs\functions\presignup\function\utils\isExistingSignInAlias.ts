import { DynamoDBClient, QueryCommand } from "@aws-sdk/client-dynamodb";
import { logger } from "@sky-uk/coins-o11y-utils/src/lib/logger";

const dynamoDbClient = new DynamoDBClient();

type GetHouseholdProps = {
  tableName: string;
  signInAlias: string;
};

export const isExistingSignInAlias = async (
  props: GetHouseholdProps,
): Promise<boolean> => {
  try {
    const { tableName, signInAlias } = props;
    const response = await dynamoDbClient.send(
      new QueryCommand({
        TableName: tableName,
        IndexName: "SignInAliasIndex",
        KeyConditionExpression: "signInAlias = :signInAlias",
        ExpressionAttributeValues: {
          ":signInAlias": { S: signInAlias },
        },
        Select: "COUNT",
      }),
    );

    const exists = (response.Count ?? 0) > 0;

    logger.info("Sign In Alias Exists Result", { exists });

    return exists;
  } catch (error: any) {
    logger.error("isExistingSignInAlias failed", { error });
    throw new Error("isExistingSignInAlias failed");
  }
};
