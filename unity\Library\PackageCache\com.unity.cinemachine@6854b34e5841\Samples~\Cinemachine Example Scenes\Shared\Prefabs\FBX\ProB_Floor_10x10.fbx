; FBX 7.4.0 project file
; Copyright (C) 1997-2015 Autodesk Inc. and/or its licensors.
; All rights reserved.
; ----------------------------------------------------

FBXHeaderExtension:  {
	FBXHeaderVersion: 1003
	FBXVersion: 7400
	CreationTimeStamp:  {
		Version: 1000
		Year: 2017
		Month: 10
		Day: 19
		Hour: 13
		Minute: 22
		Second: 39
		Millisecond: 973
	}
	Creator: "FBX SDK/FBX Plugins version 2017.1"
	SceneInfo: "SceneInfo::GlobalInfo", "UserData" {
		Type: "UserData"
		Version: 100
		MetaData:  {
			Version: 100
			Title: "exports static meshes with materials and textures"
			Subject: ""
			Author: "Unity Technologies"
			Keywords: "export mesh materials textures uvs"
			Revision: "1.0"
			Comment: ""
		}
		Properties70:  {
			P: "DocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp416dff46.tmp"
			P: "SrcDocumentUrl", "KString", "Url", "", "C:\Users\<USER>\AppData\Local\Temp\tmp416dff46.tmp"
			P: "Original", "Compound", "", ""
			P: "Original|ApplicationVendor", "KString", "", "", ""
			P: "Original|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "Original|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "Original|DateTime_GMT", "DateTime", "", "", ""
			P: "Original|FileName", "KString", "", "", ""
			P: "LastSaved", "Compound", "", ""
			P: "LastSaved|ApplicationVendor", "KString", "", "", ""
			P: "LastSaved|ApplicationName", "KString", "", "", "Unity FBX Exporter"
			P: "LastSaved|ApplicationVersion", "KString", "", "", "1.0.0f1"
			P: "LastSaved|DateTime_GMT", "DateTime", "", "", ""
		}
	}
}
GlobalSettings:  {
	Version: 1000
	Properties70:  {
		P: "UpAxis", "int", "Integer", "",1
		P: "UpAxisSign", "int", "Integer", "",1
		P: "FrontAxis", "int", "Integer", "",2
		P: "FrontAxisSign", "int", "Integer", "",1
		P: "CoordAxis", "int", "Integer", "",0
		P: "CoordAxisSign", "int", "Integer", "",1
		P: "OriginalUpAxis", "int", "Integer", "",-1
		P: "OriginalUpAxisSign", "int", "Integer", "",1
		P: "UnitScaleFactor", "double", "Number", "",1
		P: "OriginalUnitScaleFactor", "double", "Number", "",1
		P: "AmbientColor", "ColorRGB", "Color", "",0,0,0
		P: "DefaultCamera", "KString", "", "", "Producer Perspective"
		P: "TimeMode", "enum", "", "",0
		P: "TimeProtocol", "enum", "", "",2
		P: "SnapOnFrameMode", "enum", "", "",0
		P: "TimeSpanStart", "KTime", "Time", "",0
		P: "TimeSpanStop", "KTime", "Time", "",46186158000
		P: "CustomFrameRate", "double", "Number", "",-1
		P: "TimeMarker", "Compound", "", ""
		P: "CurrentTimeMarker", "int", "Integer", "",-1
	}
}

; Documents Description
;------------------------------------------------------------------

Documents:  {
	Count: 1
	Document: 1147078720, "Scene", "Scene" {
		Properties70:  {
			P: "SourceObject", "object", "", ""
			P: "ActiveAnimStackName", "KString", "", "", ""
		}
		RootNode: 0
	}
}

; Document References
;------------------------------------------------------------------

References:  {
}

; Object definitions
;------------------------------------------------------------------

Definitions:  {
	Version: 100
	Count: 7
	ObjectType: "GlobalSettings" {
		Count: 1
	}
	ObjectType: "Model" {
		Count: 1
		PropertyTemplate: "FbxNode" {
			Properties70:  {
				P: "QuaternionInterpolate", "enum", "", "",0
				P: "RotationOffset", "Vector3D", "Vector", "",0,0,0
				P: "RotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "ScalingOffset", "Vector3D", "Vector", "",0,0,0
				P: "ScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "TranslationActive", "bool", "", "",0
				P: "TranslationMin", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMax", "Vector3D", "Vector", "",0,0,0
				P: "TranslationMinX", "bool", "", "",0
				P: "TranslationMinY", "bool", "", "",0
				P: "TranslationMinZ", "bool", "", "",0
				P: "TranslationMaxX", "bool", "", "",0
				P: "TranslationMaxY", "bool", "", "",0
				P: "TranslationMaxZ", "bool", "", "",0
				P: "RotationOrder", "enum", "", "",0
				P: "RotationSpaceForLimitOnly", "bool", "", "",0
				P: "RotationStiffnessX", "double", "Number", "",0
				P: "RotationStiffnessY", "double", "Number", "",0
				P: "RotationStiffnessZ", "double", "Number", "",0
				P: "AxisLen", "double", "Number", "",10
				P: "PreRotation", "Vector3D", "Vector", "",0,0,0
				P: "PostRotation", "Vector3D", "Vector", "",0,0,0
				P: "RotationActive", "bool", "", "",0
				P: "RotationMin", "Vector3D", "Vector", "",0,0,0
				P: "RotationMax", "Vector3D", "Vector", "",0,0,0
				P: "RotationMinX", "bool", "", "",0
				P: "RotationMinY", "bool", "", "",0
				P: "RotationMinZ", "bool", "", "",0
				P: "RotationMaxX", "bool", "", "",0
				P: "RotationMaxY", "bool", "", "",0
				P: "RotationMaxZ", "bool", "", "",0
				P: "InheritType", "enum", "", "",0
				P: "ScalingActive", "bool", "", "",0
				P: "ScalingMin", "Vector3D", "Vector", "",0,0,0
				P: "ScalingMax", "Vector3D", "Vector", "",1,1,1
				P: "ScalingMinX", "bool", "", "",0
				P: "ScalingMinY", "bool", "", "",0
				P: "ScalingMinZ", "bool", "", "",0
				P: "ScalingMaxX", "bool", "", "",0
				P: "ScalingMaxY", "bool", "", "",0
				P: "ScalingMaxZ", "bool", "", "",0
				P: "GeometricTranslation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricRotation", "Vector3D", "Vector", "",0,0,0
				P: "GeometricScaling", "Vector3D", "Vector", "",1,1,1
				P: "MinDampRangeX", "double", "Number", "",0
				P: "MinDampRangeY", "double", "Number", "",0
				P: "MinDampRangeZ", "double", "Number", "",0
				P: "MaxDampRangeX", "double", "Number", "",0
				P: "MaxDampRangeY", "double", "Number", "",0
				P: "MaxDampRangeZ", "double", "Number", "",0
				P: "MinDampStrengthX", "double", "Number", "",0
				P: "MinDampStrengthY", "double", "Number", "",0
				P: "MinDampStrengthZ", "double", "Number", "",0
				P: "MaxDampStrengthX", "double", "Number", "",0
				P: "MaxDampStrengthY", "double", "Number", "",0
				P: "MaxDampStrengthZ", "double", "Number", "",0
				P: "PreferedAngleX", "double", "Number", "",0
				P: "PreferedAngleY", "double", "Number", "",0
				P: "PreferedAngleZ", "double", "Number", "",0
				P: "LookAtProperty", "object", "", ""
				P: "UpVectorProperty", "object", "", ""
				P: "Show", "bool", "", "",1
				P: "NegativePercentShapeSupport", "bool", "", "",1
				P: "DefaultAttributeIndex", "int", "Integer", "",-1
				P: "Freeze", "bool", "", "",0
				P: "LODBox", "bool", "", "",0
				P: "Lcl Translation", "Lcl Translation", "", "A",0,0,0
				P: "Lcl Rotation", "Lcl Rotation", "", "A",0,0,0
				P: "Lcl Scaling", "Lcl Scaling", "", "A",1,1,1
				P: "Visibility", "Visibility", "", "A",1
				P: "Visibility Inheritance", "Visibility Inheritance", "", "",1
			}
		}
	}
	ObjectType: "Geometry" {
		Count: 2
		PropertyTemplate: "FbxMesh" {
			Properties70:  {
				P: "Color", "ColorRGB", "Color", "",0.8,0.8,0.8
				P: "BBoxMin", "Vector3D", "Vector", "",0,0,0
				P: "BBoxMax", "Vector3D", "Vector", "",0,0,0
				P: "Primary Visibility", "bool", "", "",1
				P: "Casts Shadows", "bool", "", "",1
				P: "Receive Shadows", "bool", "", "",1
			}
		}
	}
	ObjectType: "Material" {
		Count: 1
		PropertyTemplate: "FbxSurfaceLambert" {
			Properties70:  {
				P: "ShadingModel", "KString", "", "", "Lambert"
				P: "MultiLayer", "bool", "", "",0
				P: "EmissiveColor", "Color", "", "A",0,0,0
				P: "EmissiveFactor", "Number", "", "A",1
				P: "AmbientColor", "Color", "", "A",0.2,0.2,0.2
				P: "AmbientFactor", "Number", "", "A",1
				P: "DiffuseColor", "Color", "", "A",0.8,0.8,0.8
				P: "DiffuseFactor", "Number", "", "A",1
				P: "Bump", "Vector3D", "Vector", "",0,0,0
				P: "NormalMap", "Vector3D", "Vector", "",0,0,0
				P: "BumpFactor", "double", "Number", "",1
				P: "TransparentColor", "Color", "", "A",0,0,0
				P: "TransparencyFactor", "Number", "", "A",0
				P: "DisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "DisplacementFactor", "double", "Number", "",1
				P: "VectorDisplacementColor", "ColorRGB", "Color", "",0,0,0
				P: "VectorDisplacementFactor", "double", "Number", "",1
			}
		}
	}
	ObjectType: "Texture" {
		Count: 1
		PropertyTemplate: "FbxFileTexture" {
			Properties70:  {
				P: "TextureTypeUse", "enum", "", "",0
				P: "Texture alpha", "Number", "", "A",1
				P: "CurrentMappingType", "enum", "", "",0
				P: "WrapModeU", "enum", "", "",0
				P: "WrapModeV", "enum", "", "",0
				P: "UVSwap", "bool", "", "",0
				P: "PremultiplyAlpha", "bool", "", "",1
				P: "Translation", "Vector", "", "A",0,0,0
				P: "Rotation", "Vector", "", "A",0,0,0
				P: "Scaling", "Vector", "", "A",1,1,1
				P: "TextureRotationPivot", "Vector3D", "Vector", "",0,0,0
				P: "TextureScalingPivot", "Vector3D", "Vector", "",0,0,0
				P: "CurrentTextureBlendMode", "enum", "", "",1
				P: "UVSet", "KString", "", "", "default"
				P: "UseMaterial", "bool", "", "",0
				P: "UseMipMap", "bool", "", "",0
			}
		}
	}
	ObjectType: "Video" {
		Count: 1
		PropertyTemplate: "FbxVideo" {
			Properties70:  {
				P: "ImageSequence", "bool", "", "",0
				P: "ImageSequenceOffset", "int", "Integer", "",0
				P: "FrameRate", "double", "Number", "",0
				P: "LastFrame", "int", "Integer", "",0
				P: "Width", "int", "Integer", "",0
				P: "Height", "int", "Integer", "",0
				P: "Path", "KString", "XRefUrl", "", ""
				P: "StartFrame", "int", "Integer", "",0
				P: "StopFrame", "int", "Integer", "",0
				P: "PlaySpeed", "double", "Number", "",0
				P: "Offset", "KTime", "Time", "",0
				P: "InterlaceMode", "enum", "", "",0
				P: "FreeRunning", "bool", "", "",0
				P: "Loop", "bool", "", "",0
				P: "AccessMode", "enum", "", "",0
			}
		}
	}
}

; Object properties
;------------------------------------------------------------------

Objects:  {
	Geometry: 1143713584, "Geometry::Scene", "Mesh" {
		Vertices: *726 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,100,-1000,-200,0,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,100,-1000,-300,0,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,100,-1000,-400,0,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-500,100,-500,-500,0,-500,-500,100,-600,-500,0,-600,-500,100,-700,-500,0,-700,-500,100,-800,-500,0,-800,-500,100,-900,-500,0,-900,-500,100,-1000,-500,0,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,
-600,0,-300,-600,100,-400,-600,0,-400,-600,100,-500,-600,0,-500,-600,100,-600,-600,0,-600,-600,100,-700,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,100,-1000,-600,0,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,100,-1000,-700,0,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,100,-1000,-800,0,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,100,-1000,-900,0,-1000
		} 
		PolygonVertexIndex: *1440 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,62,11,-64,63,11,-11,1,66,-16,15,66,-68,3,68,-2,1,68,-67,21,69,-15,14,69,-71,14,70,-4,3,70,-69,15,67,-17,16,67,-72,27,72,-22,21,72,-70,16,71,-23,22,71,-74,33,74,-28,27,74,-73,22,73,-29,28,73,-76,39,76,-34,33,76,-75,28,75,-35,34,75,-78,45,78,-40,39,78,-77,34,77,-41,40,77,-80,51,80,-46,45,80,-79,40,79,-47,46,79,-82,57,82,-52,51,82,-81,46,81,-53,52,81,-84,63,84,-58,57,84,-83,52,83,-59,58,83,-86,10,86,-64,63,86,-85,58,85,-9,8,85,-88,8,87,-11,10,87,-87,66,88,-68,67,88,-90,68,90,-67,66,90,-89,69,91,-71,70,91,-93,70,92,-69,68,92,-91,67,89,-72,71,89,-94,72,94,-70,69,94,-92,71,93,-74,73,93,-96,74,96,-73,72,96,-95,73,95,-76,75,95,-98,76,98,-75,74,98,-97,75,97,-78,77,97,-100,78,100,-77,76,100,-99,77,99,-80,79,99,-102,80,102,-79,78,102,-101,79,101,-82,81,101,-104,82,104,-81,80,104,-103,81,103,-84,83,103,-106,84,106,-83,82,106,-105,83,105,-86,85,105,-108,86,108,-85,84,108,-107,85,107,-88,87,107,-110,87,109,-87,86,109,-109,88,110,-90,89,110,-112,90,112,-89,88,112,-111,91,113,-93,92,113,-115,92,114,-91,90,114,-113,89,111,-94,93,111,-116,94,116,-92,91,116,-114,93,115,-96,95,115,-118,96,118,-95,94,118,-117,95,117,-98,97,117,-120,98,120,-97,96,120,-119,97,119,-100,99,119,-122,100,122,-99,98,122,-121,99,121,-102,101,121,-124,102,124,-101,100,
124,-123,101,123,-104,103,123,-126,104,126,-103,102,126,-125,103,125,-106,105,125,-128,106,128,-105,104,128,-127,105,127,-108,107,127,-130,108,130,-107,106,130,-129,107,129,-110,109,129,-132,109,131,-109,108,131,-131,110,132,-112,111,132,-134,112,134,-111,110,134,-133,113,135,-115,114,135,-137,114,136,-113,112,136,-135,111,133,-116,115,133,-138,116,138,-114,113,138,-136,115,137,-118,117,137,-140,118,140,-117,116,140,-139,117,139,-120,119,139,-142,120,142,-119,118,142,-141,119,141,-122,121,141,-144,122,144,-121,120,144,-143,121,143,-124,123,143,-146,124,146,-123,122,146,-145,123,145,-126,125,145,-148,126,148,-125,124,148,-147,125,147,-128,127,147,-150,128,150,-127,126,150,-149,127,149,-130,129,149,-152,130,152,-129,128,152,-151,129,151,-132,131,151,-154,131,153,-131,130,153,-153,132,154,-134,133,154,-156,134,156,-133,132,156,-155,135,157,-137,136,157,-159,136,158,-135,134,158,-157,133,155,-138,137,155,-160,138,160,-136,135,160,-158,137,159,-140,139,159,-162,140,162,-139,138,162,-161,139,161,-142,141,161,-164,142,164,-141,140,164,-163,141,163,-144,143,163,-166,144,166,-143,142,166,-165,143,165,-146,145,165,-168,146,168,-145,144,168,-167,145,167,-148,147,167,-170,148,170,-147,146,170,-169,147,169,-150,149,169,-172,150,172,-149,148,172,-171,149,171,-152,151,171,-174,152,174,-151,150,174,-173,151,173,-154,153,173,-176,153,175,-153,152,175,-175,154,176,-156,155,176,-178,156,178,-155,154,178,-177,157,179,-159,158,179,-181,158,180,-157,156,180,-179,155,177,-160,159,177,-182,160,182,-158,157,182,-180,159,181,-162,161,181,-184,162,184,-161,160,184,-183,161,183,-164,163,183,-186,164,186,-163,162,186,-185,163,185,-166,165,185,-188,166,188,-165,164,188,-187,165,187,-168,167,187,-190,168,190,-167,166,190,-189,167,189,-170,169,189,-192,170,192,-169,168,192,-191,169,191,-172,171,191,-194,172,194,-171,170,194,-193,171,193,-174,173,193,-196,174,196,-173,172,196,-195,173,195,-176,175,195,-198,175,197,-175,174,197,-197,176,198,-178,177,198,-200,178,200,-177,176,200,-199,179,201,-181,180,201,-203,180,202,-179,178,202,-201,177,199,-182,
181,199,-204,182,204,-180,179,204,-202,181,203,-184,183,203,-206,184,206,-183,182,206,-205,183,205,-186,185,205,-208,186,208,-185,184,208,-207,185,207,-188,187,207,-210,188,210,-187,186,210,-209,187,209,-190,189,209,-212,190,212,-189,188,212,-211,189,211,-192,191,211,-214,192,214,-191,190,214,-213,191,213,-194,193,213,-216,194,216,-193,192,216,-215,193,215,-196,195,215,-218,196,218,-195,194,218,-217,195,217,-198,197,217,-220,197,219,-197,196,219,-219,198,220,-200,199,220,-222,200,222,-199,198,222,-221,201,223,-203,202,223,-225,202,224,-201,200,224,-223,199,221,-204,203,221,-226,204,226,-202,201,226,-224,203,225,-206,205,225,-228,206,228,-205,204,228,-227,205,227,-208,207,227,-230,208,230,-207,206,230,-229,207,229,-210,209,229,-232,210,232,-209,208,232,-231,209,231,-212,211,231,-234,212,234,-211,210,234,-233,211,233,-214,213,233,-236,214,236,-213,212,236,-235,213,235,-216,215,235,-238,216,238,-215,214,238,-237,215,237,-218,217,237,-240,218,240,-217,216,240,-239,217,239,-220,219,239,-242,219,241,-219,218,241,-241,220,4,-222,221,4,-6,222,6,-221,220,6,-5,223,18,-225,224,18,-8,224,7,-223,222,7,-7,221,5,-226,225,5,-20,226,24,-224,223,24,-19,225,19,-228,227,19,-26,228,30,-227,226,30,-25,227,25,-230,229,25,-32,230,36,-229,228,36,-31,229,31,-232,231,31,-38,232,42,-231,230,42,-37,231,37,-234,233,37,-44,234,48,-233,232,48,-43,233,43,-236,235,43,-50,236,54,-235,234,54,-49,235,49,-238,237,49,-56,238,60,-237,236,60,-55,237,55,-240,239,55,-62,240,64,-239,238,64,-61,239,61,-242,241,61,-66,241,65,-241,240,65,-65
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4320 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			NormalsW: *1440 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *4320 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *1440 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *4320 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *1440 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *1320 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *1440 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,282,308,283,283,308,309,283,309,284,284,309,310,279,305,285,285,305,311,286,312,282,282,312,308,285,311,287,287,311,313,288,314,286,286,314,312,287,313,289,289,313,315,290,316,288,288,316,314,289,315,291,291,315,317,292,318,290,290,318,316,291,317,293,293,317,319,294,320,292,292,320,318,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,303,303,328,329
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *660 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,5,-5,-5,-5,5,-6,-5,-6,5,-7,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,6,-5,-6,-5,6,-6,-6,-6,6,-7,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,10,-2,10,-1,10,0,-10,-2,10,-3,-10,-3,10,-4,-10,-4,10,-5,-10,-5,10,-6,-10,-6,10,-7,-10,-7,10,-8,-10,-8,10,-9,-10,-9,10,-10,-10,-10,10,0,10,1
			} 
			UVIndex: *1440 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,282,308,283,283,308,309,283,309,284,284,309,310,279,305,285,285,305,311,286,312,282,282,312,308,285,311,287,287,311,313,288,314,286,286,314,312,287,313,289,289,313,315,290,316,288,288,316,314,289,315,291,291,315,317,292,318,290,290,318,316,291,317,293,293,317,319,294,320,292,292,320,318,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,303,303,328,329
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *660 {
				a: 0.00400000344961882,0.648957669734955,0.0523812398314476,0.648957669734955,0.00400000018998981,0.697338819503784,0.0523812361061573,0.697338819503784,0.00400000484660268,0.49181392788887,0.0523812435567379,0.49181392788887,0.00400000018998981,0.540195107460022,0.0523812398314476,0.540195107460022,0.439430415630341,0.544195175170898,0.487811654806137,0.544195175170898,0.439430415630341,0.592576324939728,0.487811654806137,0.592576324939728,0.439430415630341,0.596576392650604,0.487811654806137,0.596576392650604,0.439430415630341,0.644957542419434,0.487811654806137,0.644957542419434,0.00400000018998981,0.487810909748077,0.0523810498416424,0.487811148166656,0.00400024140253663,0.439429938793182,0.0523812286555767,0.439430147409439,0.491813182830811,0.0523819215595722,0.540194392204285,0.0523818284273148,0.491813063621521,0.00400071358308196,0.54019433259964,0.00400063395500183,0.540194511413574,0.100763022899628,0.4918133020401,0.100763097405434,0.100762404501438,0.540195107460022,0.100762404501438,0.491813957691193,0.391049236059189,0.596576392650604,0.391049236059189,0.644957542419434,0.00400042021647096,0.39104899764061,0.0523814037442207,0.391049146652222,0.540194571018219,0.149144142866135,0.49181342124939,0.149144217371941,0.149143576622009,0.540195107460022,0.149143576622009,0.49181392788887,0.342668086290359,0.596576392650604,0.342668086290359,0.644957542419434,0.00400054221972823,0.342668056488037,0.05238151922822,0.342668175697327,0.540194630622864,0.19752524793148,0.491813540458679,0.197525277733803,0.197524756193161,0.540195107460022,0.197524756193161,0.49181392788887,0.294286876916885,0.596576392650604,0.294286906719208,0.644957542419434,0.00400059763342142,0.294287145137787,0.0523815900087357,0.294287174940109,0.540194630622864,0.245906293392181,0.491813570261002,0.24590627849102,0.245905861258507,0.540195107460022,0.245905861258507,0.49181392788887,0.245905786752701,0.596576392650604,0.245905786752701,0.644957542419434,0.00400057295337319,0.245906218886375,0.0523816235363483,0.245906218886375,
0.540194630622864,0.294287294149399,0.491813570261002,0.294287234544754,0.294286996126175,0.540195107460022,0.294286966323853,0.49181392788887,0.197524651885033,0.596576392650604,0.197524666786194,0.644957602024078,0.00400050077587366,0.197525218129158,0.0523816011846066,0.197525173425674,0.540194511413574,0.342668294906616,0.491813510656357,0.342668205499649,0.342668116092682,0.540195107460022,0.342668116092682,0.491813898086548,0.149143517017365,0.596576392650604,0.149143531918526,0.644957602024078,0.00400039134547114,0.149144172668457,0.0523815341293812,0.149144098162651,0.540194392204285,0.391049295663834,0.49181342124939,0.391049146652222,0.391049265861511,0.540195107460022,0.391049265861511,0.491813898086548,0.100762382149696,0.596576392650604,0.100762397050858,0.644957602024078,0.00400026747956872,0.100763075053692,0.0523814558982849,0.100762993097305,0.540194272994995,0.439430326223373,0.491813272237778,0.439430147409439,0.439430505037308,0.540195107460022,0.439430475234985,0.491813868284225,0.0523811504244804,0.596576392650604,0.052381157875061,0.644957602024078,0.00400013709440827,0.0523819290101528,0.0523813553154469,0.0523818209767342,0.540194094181061,0.487811326980591,0.491813063621521,0.487811118364334,0.487811654806137,0.540195107460022,0.487811625003815,0.491813868284225,0.00400000018998981,0.596576392650604,0.00400001183152199,0.644957602024078,0.00400000018998981,0.00400078529492021,0.0523812435567379,0.00400068936869502,0.588575482368469,0.00400052685290575,0.588575541973114,0.0523817054927349,0.100762397050858,0.697338819503784,0.100762397050858,0.648957669734955,0.100762344896793,0.391049325466156,0.100762188434601,0.439430266618729,0.100762002170086,0.487811297178268,0.588575661182404,0.10076292604208,0.100762479007244,0.342668294906616,0.588575661182404,0.14914408326149,0.100762583315372,0.294287264347076,0.588575720787048,0.197525218129158,0.100762650370598,0.245906218886375,0.588575720787048,0.245906308293343,0.10076267272234,0.197525143623352,0.588575661182404,0.294287383556366,0.100762642920017,
0.149144008755684,0.588575541973114,0.342668414115906,0.100762583315372,0.100762903690338,0.588575422763824,0.391049414873123,0.100762508809566,0.0523816831409931,0.588575303554535,0.439430445432663,0.100762404501438,0.00400054967030883,0.588575124740601,0.48781144618988,0.391049236059189,0.544195175170898,0.391049236059189,0.592576384544373,0.636956632137299,0.00400042068213224,0.636956751346588,0.0523816235363483,0.149143561720848,0.697338819503784,0.149143561720848,0.64895761013031,0.14914333820343,0.391049474477768,0.149143144488335,0.439430475234985,0.149142935872078,0.487811475992203,0.636956751346588,0.100762851536274,0.149143487215042,0.342668414115906,0.636956810951233,0.149144038558006,0.149143621325493,0.294287353754044,0.636956810951233,0.197525203227997,0.14914371073246,0.245906263589859,0.636956751346588,0.245906338095665,0.149143770337105,0.197525143623352,0.636956691741943,0.294287443161011,0.149143770337105,0.149143978953362,0.636956632137299,0.342668503522873,0.149143725633621,0.100762829184532,0.636956453323364,0.391049563884735,0.149143680930138,0.0523815900087357,0.636956334114075,0.439430594444275,0.14914359152317,0.00400042906403542,0.63695615530014,0.487811595201492,0.342668056488037,0.544195175170898,0.342668056488037,0.592576384544373,0.685337841510773,0.00400030007585883,0.685337901115417,0.052381556481123,0.197524741292,0.697338819503784,0.197524741292,0.64895761013031,0.19752436876297,0.39104962348938,0.197524145245552,0.439430683851242,0.197523877024651,0.487811714410782,0.685337960720062,0.10076279938221,0.197524547576904,0.342668563127518,0.685337960720062,0.149144038558006,0.197524711489677,0.294287443161011,0.685337960720062,0.197525218129158,0.197524815797806,0.245906323194504,0.685337901115417,0.245906382799149,0.197524905204773,0.197525158524513,0.685337781906128,0.294287502765656,0.197524935007095,0.1491439640522,0.685337662696838,0.342668622732162,0.197524905204773,0.100762777030468,0.685337543487549,0.391049683094025,0.197524875402451,0.0523815117776394,0.685337364673615,0.439430773258209,
0.197524800896645,0.00400032056495547,0.685337126255035,0.487811774015427,0.294286876916885,0.544195175170898,0.294286876916885,0.592576384544373,0.733719050884247,0.00400019576773047,0.733719110488892,0.0523815043270588,0.245905861258507,0.697338819503784,0.245905861258507,0.64895761013031,0.245905429124832,0.391049832105637,0.245905160903931,0.439430922269821,0.245904862880707,0.487811982631683,0.733719110488892,0.100762769579887,0.24590565264225,0.34266871213913,0.733719170093536,0.149144023656845,0.245905831456184,0.2942875623703,0.733719110488892,0.19752524793148,0.245905965566635,0.24590639770031,0.733719050884247,0.245906442403793,0.245906069874763,0.197525203227997,0.733718931674957,0.294287592172623,0.245906114578247,0.149143993854523,0.733718812465668,0.342668741941452,0.245906129479408,0.100762732326984,0.733718633651733,0.391049861907959,0.245906099677086,0.0523814596235752,0.733718395233154,0.439430981874466,0.245906054973602,0.00400022091343999,0.733718156814575,0.487812012434006,0.245905786752701,0.544195175170898,0.245905786752701,0.592576384544373,0.78210037946701,0.0040001031011343,0.78210037946701,0.0523814596235752,0.294286966323853,0.697338819503784,0.294286966323853,0.64895761013031,0.294286519289017,0.391050070524216,0.294286251068115,0.439431220293045,0.294285893440247,0.48781231045723,0.78210037946701,0.100762754678726,0.294286787509918,0.342668920755386,0.78210037946701,0.149144038558006,0.294286966323853,0.294287711381912,0.782100319862366,0.197525292634964,0.294287145137787,0.245906516909599,0.782100200653076,0.245906516909599,0.294287264347076,0.197525277733803,0.782100081443787,0.294287711381912,0.294287353754044,0.149144023656845,0.782099962234497,0.342668890953064,0.294287383556366,0.100762732326984,0.782099723815918,0.391050040721893,0.294287383556366,0.0523814149200916,0.782099485397339,0.439431190490723,0.294287353754044,0.00400013010948896,0.78209924697876,0.487812280654907,0.197524666786194,0.544195175170898,0.197524651885033,0.592576384544373,0.830481708049774,0.00400003558024764,
0.830481708049774,0.0523814335465431,0.342668116092682,0.697338879108429,0.342668116092682,0.64895761013031,0.342667698860168,0.391050338745117,0.342667371034622,0.439431518316269,0.342666983604431,0.487812697887421,0.830481648445129,0.100762769579887,0.34266796708107,0.342669129371643,0.830481648445129,0.149144068360329,0.342668175697327,0.294287890195847,0.83048152923584,0.197525352239609,0.342668354511261,0.24590665102005,0.83048141002655,0.245906606316566,0.342668503522873,0.19752536714077,0.830481290817261,0.294287860393524,0.342668622732162,0.149144068360329,0.830481112003326,0.342669069766998,0.342668682336807,0.100762754678726,0.830480873584747,0.39105024933815,0.34266871213913,0.0523813962936401,0.830480635166168,0.439431458711624,0.34266871213913,0.00400005839765072,0.830480337142944,0.487812578678131,0.149143531918526,0.544195175170898,0.149143517017365,0.592576384544373,0.878863036632538,0.00400000018998981,0.878863036632538,0.0523814298212528,0.391049265861511,0.697338879108429,0.391049265861511,0.64895761013031,0.39104887843132,0.391050606966019,0.391048550605774,0.439431875944138,0.391048163175583,0.487813085317612,0.878862977027893,0.100762784481049,0.391049176454544,0.342669367790222,0.878862917423248,0.149144127964973,0.391049414873123,0.294288069009781,0.878862798213959,0.197525441646576,0.39104962348938,0.245906800031662,0.878862679004669,0.245906725525856,0.391049772500992,0.19752548635006,0.878862500190735,0.294288009405136,0.391049921512604,0.149144157767296,0.878862321376801,0.342669248580933,0.391050010919571,0.10076279938221,0.878862082958221,0.391050487756729,0.391050070524216,0.052381407469511,0.878861844539642,0.439431697130203,0.391050100326538,0.00400000018998981,0.878861546516418,0.487812876701355,0.100762389600277,0.544195115566254,0.100762382149696,0.592576384544373,0.927244424819946,0.00400000484660268,0.927244424819946,0.0523814521729946,0.439430475234985,0.697338879108429,0.439430505037308,0.64895761013031,0.439430147409439,0.391050904989243,0.43942978978157,0.439432233572006,0.439429432153702,
0.487813472747803,0.927244305610657,0.100762851536274,0.439430445432663,0.342669606208801,0.927244246006012,0.149144202470779,0.439430683851242,0.294288277626038,0.927244067192078,0.197525545954704,0.439430922269821,0.245906963944435,0.927243947982788,0.245906874537468,0.439431101083755,0.19752562046051,0.927243769168854,0.294288158416748,0.439431250095367,0.149144262075424,0.927243530750275,0.342669457197189,0.439431399106979,0.100762881338596,0.927243292331696,0.391050696372986,0.439431488513947,0.0523814484477043,0.927243053913116,0.439431965351105,0.439431548118591,0.00400000018998981,0.927242755889893,0.487813174724579,0.052381157875061,0.544195115566254,0.0523811466991901,0.592576384544373,0.975625872612,0.0040000625886023,0.97562575340271,0.0523815229535103,0.487811625003815,0.697338879108429,0.487811654806137,0.648957669734955,0.487811386585236,0.391051232814789,0.487811028957367,0.439432561397552,0.487810641527176,0.487813860177994,0.97562563419342,0.100762948393822,0.487811714410782,0.34266984462738,0.975625514984131,0.149144306778908,0.487811982631683,0.294288516044617,0.975625395774841,0.197525680065155,0.487812221050262,0.245907157659531,0.975625216960907,0.24590702354908,0.487812429666519,0.197525784373283,0.975625038146973,0.294288337230682,0.487812608480453,0.149144396185875,0.975624799728394,0.342669665813446,0.487812727689743,0.100763015449047,0.97562450170517,0.391050934791565,0.487812906503677,0.0523815639317036,0.975624203681946,0.439432263374329,0.487813055515289,0.00400007562711835,0.975623905658722,0.487813502550125,0.00400001229718328,0.544195115566254,0.00400000018998981,0.592576324939728
			} 
			UVIndex: *1440 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,282,308,283,283,308,309,283,309,284,284,309,310,279,305,285,285,305,311,286,312,282,282,312,308,285,311,287,287,311,313,288,314,286,286,314,312,287,313,289,289,313,315,290,316,288,288,316,314,289,315,291,291,315,317,292,318,290,290,318,316,291,317,293,293,317,319,294,320,292,292,320,318,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,303,303,328,329
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Geometry: 1143701296, "Geometry::Scene", "Mesh" {
		Vertices: *726 {
			a: -0,0,0,-100,0,0,-0,100,0,-100,100,0,-1000,0,0,-1000,0,-100,-1000,100,0,-1000,100,-100,-100,0,-1000,-0,0,-1000,-100,100,-1000,-0,100,-1000,-0,0,-100,-0,100,-100,-100,100,-100,-100,0,-100,-100,0,-200,-0,0,-200,-1000,100,-200,-1000,0,-200,-0,100,-200,-100,100,-200,-100,0,-300,-0,0,-300,-1000,100,-300,-1000,0,-300,-0,100,-300,-100,100,-300,-100,0,-400,-0,0,-400,-1000,100,-400,-1000,0,-400,-0,100,-400,-100,100,-400,-100,0,-500,-0,0,-500,-1000,100,-500,-1000,0,-500,-0,100,-500,-100,100,-500,-100,0,-600,-0,0,-600,-1000,100,-600,-1000,0,-600,-0,100,-600,-100,100,-600,-100,0,-700,-0,0,-700,-1000,100,-700,-1000,0,-700,-0,100,-700,-100,100,-700,-100,0,-800,-0,0,-800,-1000,100,-800,-1000,0,-800,-0,100,-800,-100,100,-800,-100,0,-900,-0,0,-900,-1000,100,-900,-1000,0,-900,-0,100,-900,-100,100,-900,-1000,100,-1000,-1000,0,-1000,-200,0,0,-200,0,-100,-200,100,0,-200,100,-200,-200,100,-100,-200,0,-200,-200,100,-300,-200,0,-300,-200,100,-400,-200,0,-400,-200,100,-500,-200,0,-500,-200,100,-600,-200,0,-600,-200,100,-700,-200,0,-700,-200,100,-800,-200,0,-800,-200,100,-900,-200,0,-900,-200,100,-1000,-200,0,-1000,-300,0,0,-300,0,-100,-300,100,0,-300,100,-200,-300,100,-100,-300,0,-200,-300,100,-300,-300,0,-300,-300,100,-400,-300,0,-400,-300,100,-500,-300,0,-500,-300,100,-600,-300,0,-600,-300,100,-700,-300,0,-700,-300,100,-800,-300,0,-800,-300,100,-900,-300,0,-900,-300,100,-1000,-300,0,-1000,-400,0,0,-400,0,-100,-400,100,0,-400,100,-200,-400,100,-100,-400,0,-200,-400,100,-300,-400,0,-300,-400,100,-400,-400,0,-400,-400,100,-500,-400,0,-500,-400,100,-600,-400,0,-600,-400,100,-700,-400,0,-700,-400,100,-800,-400,0,-800,-400,100,-900,-400,0,-900,-400,100,-1000,-400,0,-1000,-500,0,0,-500,0,-100,-500,100,0,-500,100,-200,-500,100,-100,-500,0,-200,-500,100,-300,-500,0,-300,-500,100,-400,-500,0,-400,-500,100,-500,-500,0,-500,-500,100,-600,-500,0,-600,-500,100,-700,-500,0,-700,-500,100,-800,-500,0,-800,-500,100,-900,-500,0,-900,-500,100,-1000,-500,0,-1000,-600,0,0,-600,0,-100,-600,100,0,-600,100,-200,-600,100,-100,-600,0,-200,-600,100,-300,
-600,0,-300,-600,100,-400,-600,0,-400,-600,100,-500,-600,0,-500,-600,100,-600,-600,0,-600,-600,100,-700,-600,0,-700,-600,100,-800,-600,0,-800,-600,100,-900,-600,0,-900,-600,100,-1000,-600,0,-1000,-700,0,0,-700,0,-100,-700,100,0,-700,100,-200,-700,100,-100,-700,0,-200,-700,100,-300,-700,0,-300,-700,100,-400,-700,0,-400,-700,100,-500,-700,0,-500,-700,100,-600,-700,0,-600,-700,100,-700,-700,0,-700,-700,100,-800,-700,0,-800,-700,100,-900,-700,0,-900,-700,100,-1000,-700,0,-1000,-800,0,0,-800,0,-100,-800,100,0,-800,100,-200,-800,100,-100,-800,0,-200,-800,100,-300,-800,0,-300,-800,100,-400,-800,0,-400,-800,100,-500,-800,0,-500,-800,100,-600,-800,0,-600,-800,100,-700,-800,0,-700,-800,100,-800,-800,0,-800,-800,100,-900,-800,0,-900,-800,100,-1000,-800,0,-1000,-900,0,0,-900,0,-100,-900,100,0,-900,100,-200,-900,100,-100,-900,0,-200,-900,100,-300,-900,0,-300,-900,100,-400,-900,0,-400,-900,100,-500,-900,0,-500,-900,100,-600,-900,0,-600,-900,100,-700,-900,0,-700,-900,100,-800,-900,0,-800,-900,100,-900,-900,0,-900,-900,100,-1000,-900,0,-1000
		} 
		PolygonVertexIndex: *1440 {
			a: 0,2,-2,1,2,-4,4,6,-6,5,6,-8,8,10,-10,9,10,-12,12,13,-1,0,13,-3,2,13,-4,3,13,-15,12,0,-16,15,0,-2,15,16,-13,12,16,-18,7,18,-6,5,18,-20,12,17,-14,13,17,-21,13,20,-15,14,20,-22,16,22,-18,17,22,-24,18,24,-20,19,24,-26,17,23,-21,20,23,-27,20,26,-22,21,26,-28,22,28,-24,23,28,-30,24,30,-26,25,30,-32,23,29,-27,26,29,-33,26,32,-28,27,32,-34,28,34,-30,29,34,-36,30,36,-32,31,36,-38,29,35,-33,32,35,-39,32,38,-34,33,38,-40,34,40,-36,35,40,-42,36,42,-38,37,42,-44,35,41,-39,38,41,-45,38,44,-40,39,44,-46,40,46,-42,41,46,-48,42,48,-44,43,48,-50,41,47,-45,44,47,-51,44,50,-46,45,50,-52,46,52,-48,47,52,-54,48,54,-50,49,54,-56,47,53,-51,50,53,-57,50,56,-52,51,56,-58,52,58,-54,53,58,-60,54,60,-56,55,60,-62,53,59,-57,56,59,-63,56,62,-58,57,62,-64,58,8,-60,59,8,-10,60,64,-62,61,64,-66,59,9,-63,62,9,-12,62,11,-64,63,11,-11,1,66,-16,15,66,-68,3,68,-2,1,68,-67,21,69,-15,14,69,-71,14,70,-4,3,70,-69,15,67,-17,16,67,-72,27,72,-22,21,72,-70,16,71,-23,22,71,-74,33,74,-28,27,74,-73,22,73,-29,28,73,-76,39,76,-34,33,76,-75,28,75,-35,34,75,-78,45,78,-40,39,78,-77,34,77,-41,40,77,-80,51,80,-46,45,80,-79,40,79,-47,46,79,-82,57,82,-52,51,82,-81,46,81,-53,52,81,-84,63,84,-58,57,84,-83,52,83,-59,58,83,-86,10,86,-64,63,86,-85,58,85,-9,8,85,-88,8,87,-11,10,87,-87,66,88,-68,67,88,-90,68,90,-67,66,90,-89,69,91,-71,70,91,-93,70,92,-69,68,92,-91,67,89,-72,71,89,-94,72,94,-70,69,94,-92,71,93,-74,73,93,-96,74,96,-73,72,96,-95,73,95,-76,75,95,-98,76,98,-75,74,98,-97,75,97,-78,77,97,-100,78,100,-77,76,100,-99,77,99,-80,79,99,-102,80,102,-79,78,102,-101,79,101,-82,81,101,-104,82,104,-81,80,104,-103,81,103,-84,83,103,-106,84,106,-83,82,106,-105,83,105,-86,85,105,-108,86,108,-85,84,108,-107,85,107,-88,87,107,-110,87,109,-87,86,109,-109,88,110,-90,89,110,-112,90,112,-89,88,112,-111,91,113,-93,92,113,-115,92,114,-91,90,114,-113,89,111,-94,93,111,-116,94,116,-92,91,116,-114,93,115,-96,95,115,-118,96,118,-95,94,118,-117,95,117,-98,97,117,-120,98,120,-97,96,120,-119,97,119,-100,99,119,-122,100,122,-99,98,122,-121,99,121,-102,101,121,-124,102,124,-101,100,
124,-123,101,123,-104,103,123,-126,104,126,-103,102,126,-125,103,125,-106,105,125,-128,106,128,-105,104,128,-127,105,127,-108,107,127,-130,108,130,-107,106,130,-129,107,129,-110,109,129,-132,109,131,-109,108,131,-131,110,132,-112,111,132,-134,112,134,-111,110,134,-133,113,135,-115,114,135,-137,114,136,-113,112,136,-135,111,133,-116,115,133,-138,116,138,-114,113,138,-136,115,137,-118,117,137,-140,118,140,-117,116,140,-139,117,139,-120,119,139,-142,120,142,-119,118,142,-141,119,141,-122,121,141,-144,122,144,-121,120,144,-143,121,143,-124,123,143,-146,124,146,-123,122,146,-145,123,145,-126,125,145,-148,126,148,-125,124,148,-147,125,147,-128,127,147,-150,128,150,-127,126,150,-149,127,149,-130,129,149,-152,130,152,-129,128,152,-151,129,151,-132,131,151,-154,131,153,-131,130,153,-153,132,154,-134,133,154,-156,134,156,-133,132,156,-155,135,157,-137,136,157,-159,136,158,-135,134,158,-157,133,155,-138,137,155,-160,138,160,-136,135,160,-158,137,159,-140,139,159,-162,140,162,-139,138,162,-161,139,161,-142,141,161,-164,142,164,-141,140,164,-163,141,163,-144,143,163,-166,144,166,-143,142,166,-165,143,165,-146,145,165,-168,146,168,-145,144,168,-167,145,167,-148,147,167,-170,148,170,-147,146,170,-169,147,169,-150,149,169,-172,150,172,-149,148,172,-171,149,171,-152,151,171,-174,152,174,-151,150,174,-173,151,173,-154,153,173,-176,153,175,-153,152,175,-175,154,176,-156,155,176,-178,156,178,-155,154,178,-177,157,179,-159,158,179,-181,158,180,-157,156,180,-179,155,177,-160,159,177,-182,160,182,-158,157,182,-180,159,181,-162,161,181,-184,162,184,-161,160,184,-183,161,183,-164,163,183,-186,164,186,-163,162,186,-185,163,185,-166,165,185,-188,166,188,-165,164,188,-187,165,187,-168,167,187,-190,168,190,-167,166,190,-189,167,189,-170,169,189,-192,170,192,-169,168,192,-191,169,191,-172,171,191,-194,172,194,-171,170,194,-193,171,193,-174,173,193,-196,174,196,-173,172,196,-195,173,195,-176,175,195,-198,175,197,-175,174,197,-197,176,198,-178,177,198,-200,178,200,-177,176,200,-199,179,201,-181,180,201,-203,180,202,-179,178,202,-201,177,199,-182,
181,199,-204,182,204,-180,179,204,-202,181,203,-184,183,203,-206,184,206,-183,182,206,-205,183,205,-186,185,205,-208,186,208,-185,184,208,-207,185,207,-188,187,207,-210,188,210,-187,186,210,-209,187,209,-190,189,209,-212,190,212,-189,188,212,-211,189,211,-192,191,211,-214,192,214,-191,190,214,-213,191,213,-194,193,213,-216,194,216,-193,192,216,-215,193,215,-196,195,215,-218,196,218,-195,194,218,-217,195,217,-198,197,217,-220,197,219,-197,196,219,-219,198,220,-200,199,220,-222,200,222,-199,198,222,-221,201,223,-203,202,223,-225,202,224,-201,200,224,-223,199,221,-204,203,221,-226,204,226,-202,201,226,-224,203,225,-206,205,225,-228,206,228,-205,204,228,-227,205,227,-208,207,227,-230,208,230,-207,206,230,-229,207,229,-210,209,229,-232,210,232,-209,208,232,-231,209,231,-212,211,231,-234,212,234,-211,210,234,-233,211,233,-214,213,233,-236,214,236,-213,212,236,-235,213,235,-216,215,235,-238,216,238,-215,214,238,-237,215,237,-218,217,237,-240,218,240,-217,216,240,-239,217,239,-220,219,239,-242,219,241,-219,218,241,-241,220,4,-222,221,4,-6,222,6,-221,220,6,-5,223,18,-225,224,18,-8,224,7,-223,222,7,-7,221,5,-226,225,5,-20,226,24,-224,223,24,-19,225,19,-228,227,19,-26,228,30,-227,226,30,-25,227,25,-230,229,25,-32,230,36,-229,228,36,-31,229,31,-232,231,31,-38,232,42,-231,230,42,-37,231,37,-234,233,37,-44,234,48,-233,232,48,-43,233,43,-236,235,43,-50,236,54,-235,234,54,-49,235,49,-238,237,49,-56,238,60,-237,236,60,-55,237,55,-240,239,55,-62,240,64,-239,238,64,-61,239,61,-242,241,61,-66,241,65,-241,240,65,-65
		} 
		GeometryVersion: 124
		LayerElementNormal: 0 {
			Version: 102
			Name: "Normals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Normals: *4320 {
				a: -0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,
-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,
-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,-1,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1
			} 
			NormalsW: *1440 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementBinormal: 0 {
			Version: 102
			Name: "Binormals"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Binormals: *4320 {
				a: 0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,
0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,
-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,
-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,0,-0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0
			} 
			BinormalsW: *1440 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 

		}
		LayerElementTangent: 0 {
			Version: 102
			Name: "Tangents"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "Direct"
			Tangents: *4320 {
				a: 1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-0,0,-1,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,
-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,
1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0,-1,0,0
			} 
			TangentsW: *1440 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
		}
		LayerElementColor: 0 {
			Version: 101
			Name: "VertexColors"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			Colors: *1320 {
				a: 1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,
1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1,1
			} 
			ColorIndex: *1440 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,282,308,283,283,308,309,283,309,284,284,309,310,279,305,285,285,305,311,286,312,282,282,312,308,285,311,287,287,311,313,288,314,286,286,314,312,287,313,289,289,313,315,290,316,288,288,316,314,289,315,291,291,315,317,292,318,290,290,318,316,291,317,293,293,317,319,294,320,292,292,320,318,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,303,303,328,329
			} 
		}
		LayerElementUV: 0 {
			Version: 101
			Name: "UVSet0"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *660 {
				a: 0,0,-1,0,0,1,-1,1,0,0,-1,0,0,1,-1,1,1,0,0,0,1,1,0,1,1,0,0,0,1,1,0,1,0,0,1,0,0,-1,1,-1,0,-1,-1,-1,0,0,-1,0,-1,-2,0,-2,-2,1,-2,0,2,0,2,1,0,-2,1,-2,-1,-3,0,-3,-3,1,-3,0,3,0,3,1,0,-3,1,-3,-1,-4,0,-4,-4,1,-4,0,4,0,4,1,0,-4,1,-4,-1,-5,0,-5,-5,1,-5,0,5,0,5,1,0,-5,1,-5,-1,-6,0,-6,-6,1,-6,0,6,0,6,1,0,-6,1,-6,-1,-7,0,-7,-7,1,-7,0,7,0,7,1,0,-7,1,-7,-1,-8,0,-8,-8,1,-8,0,8,0,8,1,0,-8,1,-8,-1,-9,0,-9,-9,1,-9,0,9,0,9,1,0,-9,1,-9,-1,-10,0,-10,-10,1,-10,0,10,0,10,1,0,-10,1,-10,-2,0,-2,-1,-2,1,-2,0,2,-2,2,-1,2,0,-2,-2,2,-3,-2,-3,2,-4,-2,-4,2,-5,-2,-5,2,-6,-2,-6,2,-7,-2,-7,2,-8,-2,-8,2,-9,-2,-9,2,-10,-2,-10,2,0,2,1,-3,0,-3,-1,-3,1,-3,0,3,-2,3,-1,3,0,-3,-2,3,-3,-3,-3,3,-4,-3,-4,3,-5,-3,-5,3,-6,-3,-6,3,-7,-3,-7,3,-8,-3,-8,3,-9,-3,-9,3,-10,-3,-10,3,0,3,1,-4,0,-4,-1,-4,1,-4,0,4,-2,4,-1,4,0,-4,-2,4,-3,-4,-3,4,-4,-4,-4,4,-5,-4,-5,4,-6,-4,-6,4,-7,-4,-7,4,-8,-4,-8,4,-9,-4,-9,4,-10,-4,-10,4,0,4,1,-5,0,-5,-1,-5,1,-5,0,5,-2,5,-1,5,0,-5,-2,5,-3,-5,-3,5,-4,-5,-4,5,-5,-5,-5,5,-6,-5,-6,5,-7,-5,-7,5,-8,-5,-8,5,-9,-5,-9,5,-10,-5,-10,5,0,5,1,-6,0,-6,-1,-6,1,-6,0,6,-2,6,-1,6,0,-6,-2,6,-3,-6,-3,6,-4,-6,-4,6,-5,-6,-5,6,-6,-6,-6,6,-7,-6,-7,6,-8,-6,-8,6,-9,-6,-9,6,-10,-6,-10,6,0,6,1,-7,0,-7,-1,-7,1,-7,0,7,-2,7,-1,7,0,-7,-2,7,-3,-7,-3,7,-4,-7,-4,7,-5,-7,-5,7,-6,-7,-6,7,-7,-7,-7,7,-8,-7,-8,7,-9,-7,-9,7,-10,-7,-10,7,0,7,1,-8,0,-8,-1,-8,1,-8,0,8,-2,8,-1,8,0,-8,-2,8,-3,-8,-3,8,-4,-8,-4,8,-5,-8,-5,8,-6,-8,-6,8,-7,-8,-7,8,-8,-8,-8,8,-9,-8,-9,8,-10,-8,-10,8,0,8,1,-9,0,-9,-1,-9,1,-9,0,9,-2,9,-1,9,0,-9,-2,9,-3,-9,-3,9,-4,-9,-4,9,-5,-9,-5,9,-6,-9,-6,9,-7,-9,-7,9,-8,-9,-8,9,-9,-9,-9,9,-10,-9,-10,9,0,9,1,-10,0,-10,-1,-10,1,-10,0,10,-2,10,-1,10,0,-10,-2,10,-3,-10,-3,10,-4,-10,-4,10,-5,-10,-5,10,-6,-10,-6,10,-7,-10,-7,10,-8,-10,-8,10,-9,-10,-9,10,-10,-10,-10,10,0,10,1
			} 
			UVIndex: *1440 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,282,308,283,283,308,309,283,309,284,284,309,310,279,305,285,285,305,311,286,312,282,282,312,308,285,311,287,287,311,313,288,314,286,286,314,312,287,313,289,289,313,315,290,316,288,288,316,314,289,315,291,291,315,317,292,318,290,290,318,316,291,317,293,293,317,319,294,320,292,292,320,318,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,303,303,328,329
			} 
		}
		LayerElementUV: 1 {
			Version: 101
			Name: "UVSet1"
			MappingInformationType: "ByPolygonVertex"
			ReferenceInformationType: "IndexToDirect"
			UV: *660 {
				a: 0.00400000344961882,0.648957669734955,0.0523812398314476,0.648957669734955,0.00400000018998981,0.697338819503784,0.0523812361061573,0.697338819503784,0.00400000484660268,0.49181392788887,0.0523812435567379,0.49181392788887,0.00400000018998981,0.540195107460022,0.0523812398314476,0.540195107460022,0.439430415630341,0.544195175170898,0.487811654806137,0.544195175170898,0.439430415630341,0.592576324939728,0.487811654806137,0.592576324939728,0.439430415630341,0.596576392650604,0.487811654806137,0.596576392650604,0.439430415630341,0.644957542419434,0.487811654806137,0.644957542419434,0.00400000018998981,0.487810909748077,0.0523810498416424,0.487811148166656,0.00400024140253663,0.439429938793182,0.0523812286555767,0.439430147409439,0.491813182830811,0.0523819215595722,0.540194392204285,0.0523818284273148,0.491813063621521,0.00400071358308196,0.54019433259964,0.00400063395500183,0.540194511413574,0.100763022899628,0.4918133020401,0.100763097405434,0.100762404501438,0.540195107460022,0.100762404501438,0.491813957691193,0.391049236059189,0.596576392650604,0.391049236059189,0.644957542419434,0.00400042021647096,0.39104899764061,0.0523814037442207,0.391049146652222,0.540194571018219,0.149144142866135,0.49181342124939,0.149144217371941,0.149143576622009,0.540195107460022,0.149143576622009,0.49181392788887,0.342668086290359,0.596576392650604,0.342668086290359,0.644957542419434,0.00400054221972823,0.342668056488037,0.05238151922822,0.342668175697327,0.540194630622864,0.19752524793148,0.491813540458679,0.197525277733803,0.197524756193161,0.540195107460022,0.197524756193161,0.49181392788887,0.294286876916885,0.596576392650604,0.294286906719208,0.644957542419434,0.00400059763342142,0.294287145137787,0.0523815900087357,0.294287174940109,0.540194630622864,0.245906293392181,0.491813570261002,0.24590627849102,0.245905861258507,0.540195107460022,0.245905861258507,0.49181392788887,0.245905786752701,0.596576392650604,0.245905786752701,0.644957542419434,0.00400057295337319,0.245906218886375,0.0523816235363483,0.245906218886375,
0.540194630622864,0.294287294149399,0.491813570261002,0.294287234544754,0.294286996126175,0.540195107460022,0.294286966323853,0.49181392788887,0.197524651885033,0.596576392650604,0.197524666786194,0.644957602024078,0.00400050077587366,0.197525218129158,0.0523816011846066,0.197525173425674,0.540194511413574,0.342668294906616,0.491813510656357,0.342668205499649,0.342668116092682,0.540195107460022,0.342668116092682,0.491813898086548,0.149143517017365,0.596576392650604,0.149143531918526,0.644957602024078,0.00400039134547114,0.149144172668457,0.0523815341293812,0.149144098162651,0.540194392204285,0.391049295663834,0.49181342124939,0.391049146652222,0.391049265861511,0.540195107460022,0.391049265861511,0.491813898086548,0.100762382149696,0.596576392650604,0.100762397050858,0.644957602024078,0.00400026747956872,0.100763075053692,0.0523814558982849,0.100762993097305,0.540194272994995,0.439430326223373,0.491813272237778,0.439430147409439,0.439430505037308,0.540195107460022,0.439430475234985,0.491813868284225,0.0523811504244804,0.596576392650604,0.052381157875061,0.644957602024078,0.00400013709440827,0.0523819290101528,0.0523813553154469,0.0523818209767342,0.540194094181061,0.487811326980591,0.491813063621521,0.487811118364334,0.487811654806137,0.540195107460022,0.487811625003815,0.491813868284225,0.00400000018998981,0.596576392650604,0.00400001183152199,0.644957602024078,0.00400000018998981,0.00400078529492021,0.0523812435567379,0.00400068936869502,0.588575482368469,0.00400052685290575,0.588575541973114,0.0523817054927349,0.100762397050858,0.697338819503784,0.100762397050858,0.648957669734955,0.100762344896793,0.391049325466156,0.100762188434601,0.439430266618729,0.100762002170086,0.487811297178268,0.588575661182404,0.10076292604208,0.100762479007244,0.342668294906616,0.588575661182404,0.14914408326149,0.100762583315372,0.294287264347076,0.588575720787048,0.197525218129158,0.100762650370598,0.245906218886375,0.588575720787048,0.245906308293343,0.10076267272234,0.197525143623352,0.588575661182404,0.294287383556366,0.100762642920017,
0.149144008755684,0.588575541973114,0.342668414115906,0.100762583315372,0.100762903690338,0.588575422763824,0.391049414873123,0.100762508809566,0.0523816831409931,0.588575303554535,0.439430445432663,0.100762404501438,0.00400054967030883,0.588575124740601,0.48781144618988,0.391049236059189,0.544195175170898,0.391049236059189,0.592576384544373,0.636956632137299,0.00400042068213224,0.636956751346588,0.0523816235363483,0.149143561720848,0.697338819503784,0.149143561720848,0.64895761013031,0.14914333820343,0.391049474477768,0.149143144488335,0.439430475234985,0.149142935872078,0.487811475992203,0.636956751346588,0.100762851536274,0.149143487215042,0.342668414115906,0.636956810951233,0.149144038558006,0.149143621325493,0.294287353754044,0.636956810951233,0.197525203227997,0.14914371073246,0.245906263589859,0.636956751346588,0.245906338095665,0.149143770337105,0.197525143623352,0.636956691741943,0.294287443161011,0.149143770337105,0.149143978953362,0.636956632137299,0.342668503522873,0.149143725633621,0.100762829184532,0.636956453323364,0.391049563884735,0.149143680930138,0.0523815900087357,0.636956334114075,0.439430594444275,0.14914359152317,0.00400042906403542,0.63695615530014,0.487811595201492,0.342668056488037,0.544195175170898,0.342668056488037,0.592576384544373,0.685337841510773,0.00400030007585883,0.685337901115417,0.052381556481123,0.197524741292,0.697338819503784,0.197524741292,0.64895761013031,0.19752436876297,0.39104962348938,0.197524145245552,0.439430683851242,0.197523877024651,0.487811714410782,0.685337960720062,0.10076279938221,0.197524547576904,0.342668563127518,0.685337960720062,0.149144038558006,0.197524711489677,0.294287443161011,0.685337960720062,0.197525218129158,0.197524815797806,0.245906323194504,0.685337901115417,0.245906382799149,0.197524905204773,0.197525158524513,0.685337781906128,0.294287502765656,0.197524935007095,0.1491439640522,0.685337662696838,0.342668622732162,0.197524905204773,0.100762777030468,0.685337543487549,0.391049683094025,0.197524875402451,0.0523815117776394,0.685337364673615,0.439430773258209,
0.197524800896645,0.00400032056495547,0.685337126255035,0.487811774015427,0.294286876916885,0.544195175170898,0.294286876916885,0.592576384544373,0.733719050884247,0.00400019576773047,0.733719110488892,0.0523815043270588,0.245905861258507,0.697338819503784,0.245905861258507,0.64895761013031,0.245905429124832,0.391049832105637,0.245905160903931,0.439430922269821,0.245904862880707,0.487811982631683,0.733719110488892,0.100762769579887,0.24590565264225,0.34266871213913,0.733719170093536,0.149144023656845,0.245905831456184,0.2942875623703,0.733719110488892,0.19752524793148,0.245905965566635,0.24590639770031,0.733719050884247,0.245906442403793,0.245906069874763,0.197525203227997,0.733718931674957,0.294287592172623,0.245906114578247,0.149143993854523,0.733718812465668,0.342668741941452,0.245906129479408,0.100762732326984,0.733718633651733,0.391049861907959,0.245906099677086,0.0523814596235752,0.733718395233154,0.439430981874466,0.245906054973602,0.00400022091343999,0.733718156814575,0.487812012434006,0.245905786752701,0.544195175170898,0.245905786752701,0.592576384544373,0.78210037946701,0.0040001031011343,0.78210037946701,0.0523814596235752,0.294286966323853,0.697338819503784,0.294286966323853,0.64895761013031,0.294286519289017,0.391050070524216,0.294286251068115,0.439431220293045,0.294285893440247,0.48781231045723,0.78210037946701,0.100762754678726,0.294286787509918,0.342668920755386,0.78210037946701,0.149144038558006,0.294286966323853,0.294287711381912,0.782100319862366,0.197525292634964,0.294287145137787,0.245906516909599,0.782100200653076,0.245906516909599,0.294287264347076,0.197525277733803,0.782100081443787,0.294287711381912,0.294287353754044,0.149144023656845,0.782099962234497,0.342668890953064,0.294287383556366,0.100762732326984,0.782099723815918,0.391050040721893,0.294287383556366,0.0523814149200916,0.782099485397339,0.439431190490723,0.294287353754044,0.00400013010948896,0.78209924697876,0.487812280654907,0.197524666786194,0.544195175170898,0.197524651885033,0.592576384544373,0.830481708049774,0.00400003558024764,
0.830481708049774,0.0523814335465431,0.342668116092682,0.697338879108429,0.342668116092682,0.64895761013031,0.342667698860168,0.391050338745117,0.342667371034622,0.439431518316269,0.342666983604431,0.487812697887421,0.830481648445129,0.100762769579887,0.34266796708107,0.342669129371643,0.830481648445129,0.149144068360329,0.342668175697327,0.294287890195847,0.83048152923584,0.197525352239609,0.342668354511261,0.24590665102005,0.83048141002655,0.245906606316566,0.342668503522873,0.19752536714077,0.830481290817261,0.294287860393524,0.342668622732162,0.149144068360329,0.830481112003326,0.342669069766998,0.342668682336807,0.100762754678726,0.830480873584747,0.39105024933815,0.34266871213913,0.0523813962936401,0.830480635166168,0.439431458711624,0.34266871213913,0.00400005839765072,0.830480337142944,0.487812578678131,0.149143531918526,0.544195175170898,0.149143517017365,0.592576384544373,0.878863036632538,0.00400000018998981,0.878863036632538,0.0523814298212528,0.391049265861511,0.697338879108429,0.391049265861511,0.64895761013031,0.39104887843132,0.391050606966019,0.391048550605774,0.439431875944138,0.391048163175583,0.487813085317612,0.878862977027893,0.100762784481049,0.391049176454544,0.342669367790222,0.878862917423248,0.149144127964973,0.391049414873123,0.294288069009781,0.878862798213959,0.197525441646576,0.39104962348938,0.245906800031662,0.878862679004669,0.245906725525856,0.391049772500992,0.19752548635006,0.878862500190735,0.294288009405136,0.391049921512604,0.149144157767296,0.878862321376801,0.342669248580933,0.391050010919571,0.10076279938221,0.878862082958221,0.391050487756729,0.391050070524216,0.052381407469511,0.878861844539642,0.439431697130203,0.391050100326538,0.00400000018998981,0.878861546516418,0.487812876701355,0.100762389600277,0.544195115566254,0.100762382149696,0.592576384544373,0.927244424819946,0.00400000484660268,0.927244424819946,0.0523814521729946,0.439430475234985,0.697338879108429,0.439430505037308,0.64895761013031,0.439430147409439,0.391050904989243,0.43942978978157,0.439432233572006,0.439429432153702,
0.487813472747803,0.927244305610657,0.100762851536274,0.439430445432663,0.342669606208801,0.927244246006012,0.149144202470779,0.439430683851242,0.294288277626038,0.927244067192078,0.197525545954704,0.439430922269821,0.245906963944435,0.927243947982788,0.245906874537468,0.439431101083755,0.19752562046051,0.927243769168854,0.294288158416748,0.439431250095367,0.149144262075424,0.927243530750275,0.342669457197189,0.439431399106979,0.100762881338596,0.927243292331696,0.391050696372986,0.439431488513947,0.0523814484477043,0.927243053913116,0.439431965351105,0.439431548118591,0.00400000018998981,0.927242755889893,0.487813174724579,0.052381157875061,0.544195115566254,0.0523811466991901,0.592576384544373,0.975625872612,0.0040000625886023,0.97562575340271,0.0523815229535103,0.487811625003815,0.697338879108429,0.487811654806137,0.648957669734955,0.487811386585236,0.391051232814789,0.487811028957367,0.439432561397552,0.487810641527176,0.487813860177994,0.97562563419342,0.100762948393822,0.487811714410782,0.34266984462738,0.975625514984131,0.149144306778908,0.487811982631683,0.294288516044617,0.975625395774841,0.197525680065155,0.487812221050262,0.245907157659531,0.975625216960907,0.24590702354908,0.487812429666519,0.197525784373283,0.975625038146973,0.294288337230682,0.487812608480453,0.149144396185875,0.975624799728394,0.342669665813446,0.487812727689743,0.100763015449047,0.97562450170517,0.391050934791565,0.487812906503677,0.0523815639317036,0.975624203681946,0.439432263374329,0.487813055515289,0.00400007562711835,0.975623905658722,0.487813502550125,0.00400001229718328,0.544195115566254,0.00400000018998981,0.592576324939728
			} 
			UVIndex: *1440 {
				a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
164,136,136,164,162,137,163,139,139,163,165,140,166,138,138,166,164,139,165,141,141,165,167,142,168,140,140,168,166,141,167,143,143,167,169,144,170,142,142,170,168,143,169,145,145,169,171,146,172,147,147,172,173,148,174,149,149,174,175,150,176,151,151,176,177,152,178,153,153,178,179,153,179,154,154,179,180,149,175,155,155,175,181,156,182,152,152,182,178,155,181,157,157,181,183,158,184,156,156,184,182,157,183,159,159,183,185,160,186,158,158,186,184,159,185,161,161,185,187,162,188,160,160,188,186,161,187,163,163,187,189,164,190,162,162,190,188,163,189,165,165,189,191,166,192,164,164,192,190,165,191,167,167,191,193,168,194,166,166,194,192,167,193,169,169,193,195,170,196,168,168,196,194,169,195,171,171,195,197,172,198,173,173,198,199,174,200,175,175,200,201,176,202,177,177,202,203,178,204,179,179,204,205,179,205,180,180,205,206,175,201,181,181,201,207,182,208,178,178,208,204,181,207,183,183,207,209,184,210,182,182,210,208,183,209,185,185,209,211,186,212,184,184,212,210,185,211,187,187,211,213,188,214,186,186,214,212,187,213,189,189,213,215,190,216,188,188,216,214,189,215,191,191,215,217,192,218,190,190,218,216,191,217,193,193,217,219,194,220,192,192,220,218,193,219,195,195,219,221,196,222,194,194,222,220,195,221,197,197,221,223,198,224,199,199,224,225,200,226,201,201,226,227,202,228,203,203,228,229,204,230,205,205,230,231,205,231,206,206,231,232,201,227,207,207,227,233,208,234,204,204,234,230,207,233,209,209,233,235,210,236,208,208,236,234,209,235,211,211,235,237,212,238,210,210,238,236,211,237,213,213,237,239,214,240,212,212,240,238,213,239,215,215,239,241,216,242,214,214,242,240,215,241,217,217,241,243,218,244,216,216,244,242,217,243,219,219,243,245,220,246,218,218,246,244,219,245,221,221,245,247,222,248,220,220,248,246,221,247,223,223,247,249,224,250,225,225,250,251,226,252,227,227,252,253,228,254,229,229,254,255,230,256,231,231,256,257,231,257,232,232,257,258,227,253,233,233,253,259,234,260,230,230,260,256,233,259,235,235,259,261,236,262,234,234,262,260,235,261,237,237,261,263,238,264,236,236,264,262,237,263,239,239,
263,265,240,266,238,238,266,264,239,265,241,241,265,267,242,268,240,240,268,266,241,267,243,243,267,269,244,270,242,242,270,268,243,269,245,245,269,271,246,272,244,244,272,270,245,271,247,247,271,273,248,274,246,246,274,272,247,273,249,249,273,275,250,276,251,251,276,277,252,278,253,253,278,279,254,280,255,255,280,281,256,282,257,257,282,283,257,283,258,258,283,284,253,279,259,259,279,285,260,286,256,256,286,282,259,285,261,261,285,287,262,288,260,260,288,286,261,287,263,263,287,289,264,290,262,262,290,288,263,289,265,265,289,291,266,292,264,264,292,290,265,291,267,267,291,293,268,294,266,266,294,292,267,293,269,269,293,295,270,296,268,268,296,294,269,295,271,271,295,297,272,298,270,270,298,296,271,297,273,273,297,299,274,300,272,272,300,298,273,299,275,275,299,301,276,302,277,277,302,303,278,304,279,279,304,305,280,306,281,281,306,307,282,308,283,283,308,309,283,309,284,284,309,310,279,305,285,285,305,311,286,312,282,282,312,308,285,311,287,287,311,313,288,314,286,286,314,312,287,313,289,289,313,315,290,316,288,288,316,314,289,315,291,291,315,317,292,318,290,290,318,316,291,317,293,293,317,319,294,320,292,292,320,318,293,319,295,295,319,321,296,322,294,294,322,320,295,321,297,297,321,323,298,324,296,296,324,322,297,323,299,299,323,325,300,326,298,298,326,324,299,325,301,301,325,327,302,328,303,303,328,329
			} 
		}
		LayerElementMaterial: 0 {
			Version: 101
			Name: "Material"
			MappingInformationType: "AllSame"
			ReferenceInformationType: "IndexToDirect"
			Materials: *1 {
				a: 0
			} 
		}
		Layer: 0 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementNormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementBinormal"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementTangent"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementMaterial"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementColor"
				TypedIndex: 0
			}
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 0
			}
		}
		Layer: 1 {
			Version: 100
			LayerElement:  {
				Type: "LayerElementUV"
				TypedIndex: 1
			}
		}
	}
	Model: 1145400560, "Model::ProB_Floor_10x10", "Mesh" {
		Version: 232
		Properties70:  {
			P: "ScalingMax", "Vector3D", "Vector", "",0,0,0
			P: "DefaultAttributeIndex", "int", "Integer", "",0
		}
		Shading: W
		Culling: "CullingOff"
	}
	Material: 1148826896, "Material::Default_Prototype", "" {
		Version: 102
		ShadingModel: "lambert"
		MultiLayer: 0
		Properties70:  {
			P: "AmbientColor", "Color", "", "A",0,0,0
			P: "DiffuseColor", "Color", "", "A",1,1,1
			P: "BumpFactor", "double", "Number", "",0
			P: "Emissive", "Vector3D", "Vector", "",0,0,0
			P: "Ambient", "Vector3D", "Vector", "",0,0,0
			P: "Diffuse", "Vector3D", "Vector", "",1,1,1
			P: "Opacity", "double", "Number", "",1
		}
	}
	Video: 1148836096, "Video::DiffuseColor_Texture", "Clip" {
		Type: "Clip"
		Properties70:  {
			P: "Path", "KString", "XRefUrl", "", "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		}
		UseMipMap: 0
		Filename: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
	}
	Texture: 294700000, "Texture::DiffuseColor_Texture", "" {
		Type: "TextureVideoClip"
		Version: 202
		TextureName: "Texture::DiffuseColor_Texture"
		Media: "Video::DiffuseColor_Texture"
		FileName: "C:\Users\<USER>\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		RelativeFilename: "..\..\..\Documents\Unity_Projects\use_cases_cinemachine\Assets\Plugins\ProCore\ProBuilder\Resources\Textures\GridBox_Default.png"
		ModelUVTranslation: 0,0
		ModelUVScaling: 1,1
		Texture_Alpha_Source: "None"
		Cropping: 0,0,0,0
	}
}

; Object connections
;------------------------------------------------------------------

Connections:  {
	
	;Model::ProB_Floor_10x10, Model::RootNode
	C: "OO",1145400560,0
	
	;Material::Default_Prototype, Model::ProB_Floor_10x10
	C: "OO",1148826896,1145400560
	
	;Material::Default_Prototype, Model::ProB_Floor_10x10
	C: "OO",1148826896,1145400560
	
	;Geometry::Scene, Model::ProB_Floor_10x10
	C: "OO",1143701296,1145400560
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype
	C: "OO",294700000,1148826896
	
	;Texture::DiffuseColor_Texture, Material::Default_Prototype
	C: "OP",294700000,1148826896, "DiffuseColor"
	
	;Video::DiffuseColor_Texture, Texture::DiffuseColor_Texture
	C: "OO",1148836096,294700000
}
;Takes section
;----------------------------------------------------

Takes:  {
	Current: ""
}
