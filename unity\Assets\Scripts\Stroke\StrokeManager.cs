using System;
using App.CameraSystem;
using BallManager;
using BallManager.StrokeData.Data;
using Flag;
using UnityEngine;

namespace Stroke
{
    public class StrokeManager : MonoBehaviour
    {
        [SerializeField] private GolfBallManager golfBallManager;
        [SerializeField] private FlagManager flagManager;
        [SerializeField] private CameraDirector cameraDirector;
        
        public Action onFinished;
        public StrokeData CurrentStrokeData { get; private set; }

        private void OnDestroy()
        {
            golfBallManager.FinishedMoving -= OnStrokeFinished;
        }

        public void ShowStroke(StrokeData stroke)
        {
            CurrentStrokeData = stroke;

            golfBallManager.GenerateBall(stroke);
            flagManager.GenerateFlag(stroke.HoleDetails.PinPosition);

            cameraDirector.PrepareShotTracking(flagManager.Flag.transform, null);

            Invoke(nameof(StartStroke), 3f);
        }

        public void ResetStrokeSystem()
        {
            CurrentStrokeData = null;
            golfBallManager.DestroyBall();
            flagManager.DestroyFlag();
        }


        private void StartStroke()
        {
            golfBallManager.StartStroke();
            golfBallManager.FinishedMoving -= OnStrokeFinished; // Prevent duplicate subscriptions
            golfBallManager.FinishedMoving += OnStrokeFinished;
        }
        
        private void OnStrokeFinished()
        {
            golfBallManager.FinishedMoving -= OnStrokeFinished;
            onFinished?.Invoke();
        }
    }
}