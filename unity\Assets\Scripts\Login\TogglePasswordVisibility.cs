using TMPro;
using UnityEngine;

namespace Login
{
    public class TogglePasswordVisibility : MonoBehaviour
    {
        [SerializeField] private TMP_InputField passwordInputField;

        private bool isPasswordVisible = false;

        private void Start()
        {
            if(!InspectorValidator.CheckAssigned(passwordInputField, nameof(passwordInputField), this))
            {
                return;
            }
        }

        public void UpdatePasswordVisibility()
        {
            isPasswordVisible = !isPasswordVisible;

            passwordInputField.contentType = isPasswordVisible ? TMP_InputField.ContentType.Standard : TMP_InputField.ContentType.Password;
            passwordInputField.ForceLabelUpdate();
            passwordInputField.Select();
            passwordInputField.ActivateInputField();
        }
    }
}
