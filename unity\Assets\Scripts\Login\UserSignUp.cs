using System;
using Constants;
using Data;
using Login.State;
using Login.Validator;
using Network.Auth;
using Network.Services;
using Sky.GenEx.Toast;
using UnityEngine;
using UnityEngine.UI;

namespace Login
{
    public class UserSignUp : BaseLoginState
    {
        [Header("Validators")]
        [SerializeField] private InputValidator emailValidator;
        [SerializeField] private InputValidator userNameValidator;
        [SerializeField] private InputValidator passwordValidator;

        [Header("Buttons")]
        [SerializeField] private Button startPlayingButton;
        [SerializeField] private Button backButton;
        [SerializeField] private Button termsOfServiceButton;


        public override void Enter()
        {
            gameObject.SetActive(true);
            startPlayingButton.onClick.AddListener(SignUserUp);
            SetButtonInteractivity(true);
            backButton.onClick.AddListener(() => onFinished(AuthState.Welcome));
        }

        private void SetButtonInteractivity(bool isInteractable)
        {
            startPlayingButton.interactable = isInteractable;
            backButton.interactable = isInteractable;
            termsOfServiceButton.interactable = isInteractable;
        }

        private void SignUserUp()
        {
            if (TryBasicValidation())
            {
                SendUserDataAsync();
            }
            else
            {
                Debug.LogError("User sign-up failed due to validation errors.");
            }
        }

        private bool TryBasicValidation()
        {
            InputValidationResult emailResult = emailValidator.ValidateInputText();
            InputValidationResult userNameResult = userNameValidator.ValidateInputText();
            InputValidationResult passwordResult = passwordValidator.ValidateInputText();

            if (emailResult != InputValidationResult.ValidFormat)
            {
                Debug.LogError("Invalid email format.");
                return false;
            }
            if (userNameResult != InputValidationResult.ValidFormat)
            {
                Debug.LogError("Invalid username format.");
                return false;
            }
            if (passwordResult != InputValidationResult.ValidFormat)
            {
                Debug.LogError("Invalid password format.");
                return false;
            }
            return true;
        }

        private async void SendUserDataAsync()
        {
            try
            {
                string email = emailValidator.InputText;
                string username = userNameValidator.InputText;
                string password = passwordValidator.InputText;

                SetButtonInteractivity(false);
                var registerResult = await AuthService.Register(email, username, password);

                if (!registerResult.IsSuccess)
                {
                    Debug.LogError($"User registration failed: {registerResult.Error.Message}");
                    MessageToastController.Instance.ShowToast(registerResult.Error.Message, ToastDuration.Long, ToastType.Error);
                    SetButtonInteractivity(true);
                }
                else
                {
                    Debug.Log("User registration successful.");
                    MessageToastController.Instance.ShowToast("Registration Successful!\nLogging in...", ToastDuration.Long);

                    var signInResult = await AuthService.Login(email, password);
                    // Check if this MonoBehaviour is still valid
                    if (this == null || !this.gameObject) return;

                    if (!signInResult.IsSuccess)
                    {
                        Debug.LogError($"User sign-in failed: {signInResult.Error.Message}");
                        var updateMessage = $"{signInResult.Error.Message} \nPlease try to login.";
                        MessageToastController.Instance.ShowToast(updateMessage, ToastDuration.Long, ToastType.Error);
                        onFinished(AuthState.SignIn);
                    }
                    else
                    {
                        Debug.Log("User sign-in successful.");
                        Debug.Log("Auth Token: " + signInResult.Value);
                        SessionData.ToggleFTUE(true);
                        AuthTokenManager.Instance.UpdateTokens(signInResult.Value);
                        onFinished(AuthState.Authenticated);
                    }
                }
            }
            catch (Exception e)
            {
                Debug.LogException(e);
                MessageToastController.Instance.ShowToast(DefaultMessages.SomethingWentWrong);
            }
        }

        public override void Exit()
        {
            emailValidator.ClearAll();
            userNameValidator.ClearAll();
            passwordValidator.ClearAll();

            startPlayingButton.onClick.RemoveListener(SignUserUp);
            backButton.onClick.RemoveAllListeners();
            gameObject.SetActive(false);
        }
    }
}
