import { useRef, useState } from "react";
import { GameTheGreenIcon } from "./icons";
import { useSpring } from "@react-spring/web";
import { FlagIcon } from "./icons/FlagIcon";
import { getRandomFact } from "../assets/loadingFacts";

export const Loader = ({ progress }: { progress: number }) => {
  const stop1Ref = useRef<SVGStopElement>(null);
  const stop2Ref = useRef<SVGStopElement>(null);

  const [fact] = useState(getRandomFact());

  useSpring({
    percentage: `${Math.floor(progress * 100)}%`,
    onChange: ({ value }) => {
      if (!stop1Ref.current || !stop2Ref.current) return;
      stop1Ref.current!.setAttribute("offset", value.percentage);
      stop2Ref.current!.setAttribute("offset", value.percentage);
    },
    config: {
      tension: 280,
      friction: 120,
      clamp: true,
    },
  });

  return (
    <div className="w-full h-full grid grid-rows-3 items-center bg-background absolute z-10 p-4">
      <div className="relative row-start-2 flex justify-center items-center w-full h-full p-8">
        <GameTheGreenIcon
          mask="url(#progress-mask)"
          className="absolute w-full h-full top-0 left-0"
        />
        <svg className="absolute w-full h-full top-0 left-0">
          <defs>
            <linearGradient id="progress-gradient" x1="0" y1="1" x2="0" y2="0">
              <stop offset="0%" stopColor="white" />
              <stop ref={stop1Ref} stopColor="white" />
              <stop ref={stop2Ref} stopColor="#333" />
              <stop offset="100%" stopColor="#333" />
            </linearGradient>
          </defs>
          <mask id="progress-mask">
            <rect x="0" y="60%" width="100%" height="40%" fill="white" />
            <rect
              x="0"
              y="0"
              width="100%"
              height="60%"
              fill="url(#progress-gradient)"
            />
          </mask>
        </svg>
      </div>
      <div className="row-start-3">
        <div className="bg-cb-blue-500 px-4 py-6 rounded-2xl gap-4 text-white w-full flex">
          <div className="basis-6">
            <FlagIcon className="w-6 h-6" />
          </div>
          <div>
            <div className="text-sm">Did you know?</div>
            <div className="text-xl">{fact}</div>
          </div>
        </div>
      </div>
    </div>
  );
};
