using System.Collections.Generic;
using BallManager.StrokeData.DataContracts;

namespace BallManager.StrokeData.Data
{
	public class TrajectoryData
	{
		public BallTrajectoryData[] BallTrajectory { get; }

		public TrajectoryData(TrajectoryDataContract trajectoryDataContract)
		{
			var trajectoriesList = new List<BallTrajectoryData>();

			foreach (var data in trajectoryDataContract.BallTrajectory)
			{
				trajectoriesList.Add(data == null ? null : new BallTrajectoryData(data));
			}
			BallTrajectory = trajectoriesList.ToArray();
		}
	}
}
