import React from "react";
import ReactDOM from "react-dom/client";
import "./styles/globals.css";
import App from "./App";
import { getAwsRum } from "./services/rum";

const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(
  <React.StrictMode>
    <App />
  </React.StrictMode>,
);

document.addEventListener("visibilitychange", () => {
  if (document.hidden) {
    getAwsRum()?.dispatch();
  }
});
