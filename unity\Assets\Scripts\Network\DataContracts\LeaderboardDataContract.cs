using Newtonsoft.Json;

namespace Network.DataContracts
{
    public class LeaderboardEntryDataContract
    {
        [JsonProperty("rank")]
        public int Rank { get; set; }

        [JsonProperty("score")]
        public int Score { get; set; }

        [JsonProperty("userName")]
        public string UserName { get; set; }
    }

    public class LeaderboardByUserIdDataContract
    {
        [JsonProperty("prev")]
        public LeaderboardEntryDataContract[] Prev { get; set; }
        [JsonProperty("current")]
        public LeaderboardEntryDataContract Current { get; set; }
        [JsonProperty("next")]
        public LeaderboardEntryDataContract[] Next { get; set; }
    }

    public class LeaderboardDataContract
    {
        [Json<PERSON>roperty("leaderboardByUserId")]
        public LeaderboardByUserIdDataContract LeaderboardByUserId { get; set; }
    }
}