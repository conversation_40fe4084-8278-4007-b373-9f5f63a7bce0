﻿using System;
using Network.Response;
using UnityEngine;

namespace Network.Auth
{
    public static class AuthErrorParser
    {
        [Serializable]
        public class AuthErrorResponse
        {
            public string __type;
            public string message;
        }

        public static ServiceError Parse(long responseCode, string responseText, string fallbackError)
        {
            string errorMessage = null;

            try
            {
                var errorResponse = JsonUtility.FromJson<AuthErrorResponse>(responseText);
                if (errorResponse != null && !string.IsNullOrEmpty(errorResponse.message))
                {
                    errorMessage = errorResponse.message;
                }
            }
            catch
            {
                errorMessage = fallbackError;
            }

            // Use the backend message if available, otherwise fallback
            return new ServiceError(responseCode, errorMessage ?? fallbackError);
        }
    }
}