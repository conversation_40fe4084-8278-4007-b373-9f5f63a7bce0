using System.Collections.Generic;
using TabMenu.Data;
using UnityEngine;
using UnityEngine.Pool;
using UnityEngine.UI;
using Network.Services;
using Sky.GenEx.Toast;

namespace TabMenu.Containers
{
    /// <summary>
    /// Container for the Ranking tab content
    /// </summary>
    public class RankingContainer : TabContainer
    {
        [SerializeField] private Toggle overallToggle;
        [SerializeField] private Toggle householdToggle;
        [SerializeField] private Transform rankedListContainer;
        [SerializeField] private GameObject inactivePoolParent;
        [SerializeField] private GameObject rankingCellPrefab;

        private const string fetchErrorMessage = "Error getting leaderboard. Please try again later.";

        private RankingData currentRankingData;
        private List<RankingCell> activeCells = new List<RankingCell>();
        private bool isShowingOverall = true;

        private ObjectPool<RankingCell> rankingCellPool;

        public RankingContainer()
        {
            tabType = TabType.Ranking;
        }

        public override void Initialize(TabMenuManager menuManager)
        {
            base.Initialize(menuManager);
            
            if (overallToggle != null)
            {
                overallToggle.onValueChanged.AddListener(OnOverallToggleChanged);
                overallToggle.isOn = true;
            }

            if (householdToggle != null)
            {
                householdToggle.onValueChanged.AddListener(OnHouseholdToggleChanged);
            }

            currentRankingData = new RankingData();

            rankingCellPool = new ObjectPool<RankingCell>(
                CreatePooledItem,
                OnTakeFromPool,
                OnReturnedToPool,
                OnDestroyPoolObject,
                collectionCheck: true,
                defaultCapacity: 10,
                maxSize: 50
            );

            UpdateRankingDisplay();
        }

        public override void OnSelect()
        {
            base.OnSelect();
        }

        public override void OnDeselected()
        {
            base.OnDeselected();
        }

        public override void OnShow()
        {
            base.OnShow();
            RefreshRankingData();
        }

        public override void OnHide()
        {
            base.OnHide();
        }

        private void OnOverallToggleChanged(bool isOn)
        {
            if (isOn && !isShowingOverall)
            {
                isShowingOverall = true;
                UpdateRankingDisplay();
            }
        }

        private void OnHouseholdToggleChanged(bool isOn)
        {
            if (isOn && isShowingOverall)
            {
                isShowingOverall = false;
                UpdateRankingDisplay();
            }
        }

        public void SetRankingData(RankingData rankingData)
        {
            currentRankingData = rankingData ?? new RankingData();
            UpdateRankingDisplay();
        }

        private async void RefreshRankingData()
        {
            var leaderboardResult = await LeaderBoardService.GetLeaderboard();
            
            if (leaderboardResult.IsSuccess)
            {
                var leaderboardData = leaderboardResult.Value;
                var rankingData = new RankingData();
                
                foreach (var entry in leaderboardData.Prev)
                {
                    var prevEntry = new RankingEntry(
                        entry.Rank,
                        entry.UserName,
                        entry.Score,
                        false // Previous entries are not the current player
                    );
                    rankingData.OverallRankings.Add(prevEntry);
                }
                var realEntry = new RankingEntry(
                    leaderboardData.Current.Rank,
                    leaderboardData.Current.UserName,
                    leaderboardData.Current.Score,
                    true // This is the current player
                );
                rankingData.OverallRankings.Add(realEntry);
                foreach (var entry in leaderboardData.Next)
                {
                    var nextEntry = new RankingEntry(
                        entry.Rank,
                        entry.UserName,
                        entry.Score,
                        false // Next entries are not the current player
                    );
                    rankingData.OverallRankings.Add(nextEntry);
                }
                
                SetRankingData(rankingData);
            }
            else
            {
                Debug.LogError($"Failed to fetch leaderboard: {leaderboardResult.Error.Message}");
                MessageToastController.Instance.ShowToast(fetchErrorMessage, ToastDuration.Long, ToastType.Error);
                SetRankingData(new RankingData());
            }
        }

        private void UpdateRankingDisplay()
        {
            ClearActiveCells();

            if (currentRankingData == null || rankingCellPrefab == null || rankedListContainer == null)
                return;

            var rankings = isShowingOverall ? currentRankingData.OverallRankings : currentRankingData.HouseholdRankings;

            foreach (var entry in rankings)
            {
                CreateRankingCell(entry);
            }
        }

        private RankingCell CreatePooledItem()
        {
            GameObject go = Instantiate(rankingCellPrefab, rankedListContainer);
            RankingCell cell = go.GetComponent<RankingCell>();
            return cell;
        }

        private void OnTakeFromPool(RankingCell cell)
        {
            cell.transform.SetParent(rankedListContainer, false);
            cell.gameObject.SetActive(true);
        }

        private void OnReturnedToPool(RankingCell cell)
        {
            cell.transform.SetParent(inactivePoolParent.transform, false);
            cell.gameObject.SetActive(false);
        }

        private void OnDestroyPoolObject(RankingCell cell)
        {
            if (cell != null && cell.gameObject != null)
                Destroy(cell.gameObject);
        }

        private void CreateRankingCell(RankingEntry entry)
        {
            RankingCell cell = rankingCellPool.Get();

            string nameAbbreviation = cell.GetNameAbbreviation(entry.PlayerName);
            Color color = GenerateColorFromName(entry.PlayerName);

            cell.transform.SetParent(rankedListContainer, false);
            cell.SetRankingData(
                entry.Rank,
                entry.PlayerName,
                entry.TotalPoints,
                entry.IsCurrentPlayer,
                nameAbbreviation,
                color
            );

            activeCells.Add(cell);
        }

        private void ClearActiveCells()
        {
            foreach (var cell in activeCells)
            {
                if (cell != null)
                    rankingCellPool.Release(cell);
            }

            activeCells.Clear();
        }

        private Color GenerateColorFromName(string name)
        {
            string seedString = name.Length > 0 ? name : "?";
            int hash = seedString.GetHashCode();
            return new Color(
                ((hash & 0xFF) / 255f) * 0.7f,
                (((hash >> 8) & 0xFF) / 255f) * 0.7f,
                (((hash >> 16) & 0xFF) / 255f) * 0.7f
            );
        }

        private void OnDestroy()
        {
            if (overallToggle != null)
                overallToggle.onValueChanged.RemoveListener(OnOverallToggleChanged);
            
            if (householdToggle != null)
                householdToggle.onValueChanged.RemoveListener(OnHouseholdToggleChanged);
        }
    }
}